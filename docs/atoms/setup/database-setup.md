---
title: "Database Setup"
category: "atoms"
subcategory: "setup"
tags: ["database", "setup", "prisma", "postgresql", "sqlite"]
last_updated: "2025-06-15"
last_validated: "2025-06-15"
dependencies: ["environment.md"]
used_by: []
maintainer: "development-team"
ai_context: "Database setup procedures for FAAFO Career Platform"
---

## Database Setup

### **Development Database (SQLite)**
```bash
cd faafo-career-platform

# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Seed database with test data
npx prisma db seed
```

### **Production Database (PostgreSQL)**
```bash
# Set DATABASE_URL in .env.local
DATABASE_URL="postgresql://username:password@host:port/database"

# Run migrations
npx prisma migrate deploy

# Generate client
npx prisma generate
```

### **Database Studio**
```bash
# Open Prisma Studio for database management
npx prisma studio
```

### **Reset Database**
```bash
# Reset development database
npx prisma migrate reset

# Confirm with 'y' when prompted
```

### **Verification**
```bash
# Check database connection
npm run db:check

# Expected output:
# ✅ Database connection successful
# ✅ All migrations applied
# ✅ Seed data loaded
```
