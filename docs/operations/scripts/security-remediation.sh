#!/bin/bash

# 🚨 CRITICAL SECURITY REMEDIATION SCRIPT
# This script addresses the remaining security vulnerabilities in the Salary Calculator

set -e  # Exit on any error

echo "🚨 STARTING CRITICAL SECURITY REMEDIATION"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Must be run from the project root directory"
    exit 1
fi

echo "📋 SECURITY REMEDIATION CHECKLIST"
echo "================================="

# 1. Fix dependency vulnerabilities
echo "1. Fixing dependency vulnerabilities..."
print_warning "This may introduce breaking changes"
read -p "Continue with npm audit fix --force? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    npm audit fix --force
    print_status "Dependency vulnerabilities fixed"
else
    print_warning "Skipping dependency fixes - SECURITY RISK REMAINS"
fi

# 2. Run tests to identify failures
echo "2. Running test suite to identify failures..."
npm test -- --passWithNoTests --verbose 2>&1 | tee test-results.log || true

# Count test failures
FAILED_TESTS=$(grep -c "FAIL" test-results.log || echo "0")
FAILED_SUITES=$(grep -c "Test Suites:.*failed" test-results.log || echo "0")

if [ "$FAILED_TESTS" -gt "0" ] || [ "$FAILED_SUITES" -gt "0" ]; then
    print_error "$FAILED_TESTS failed tests, $FAILED_SUITES failed suites"
    print_error "CRITICAL: Tests must pass before production deployment"
else
    print_status "All tests passing"
fi

# 3. Check for console statements
echo "3. Checking for console statements in production code..."
CONSOLE_COUNT=$(find src -name "*.ts" -o -name "*.tsx" | xargs grep -l "console\." | wc -l)
if [ "$CONSOLE_COUNT" -gt "0" ]; then
    print_error "Found $CONSOLE_COUNT files with console statements"
    find src -name "*.ts" -o -name "*.tsx" | xargs grep -n "console\." || true
    print_error "SECURITY RISK: Console statements may leak sensitive information"
else
    print_status "No console statements found in production code"
fi

# 4. Validate environment configuration
echo "4. Validating environment configuration..."
if [ ! -f ".env.local" ] && [ ! -f ".env" ]; then
    print_error "No environment file found"
    print_error "CRITICAL: Environment variables not configured"
else
    print_status "Environment file exists"
fi

# 5. Check for CSRF protection
echo "5. Verifying CSRF protection is enabled..."
if grep -q "requireCSRF: false" src/app/api/tools/salary-calculator/route.ts; then
    print_error "CSRF protection is disabled"
    print_error "CRITICAL VULNERABILITY: CSRF attacks possible"
else
    print_status "CSRF protection is enabled"
fi

# 6. Check for fake data generation
echo "6. Checking for fake data generation..."
if grep -q "Math.random()" src/app/api/tools/salary-calculator/route.ts; then
    print_error "Fake data generation detected"
    print_error "INTEGRITY ISSUE: Users receiving fabricated data"
else
    print_status "No fake data generation found"
fi

# 7. Security headers check
echo "7. Checking security middleware configuration..."
if [ -f "src/lib/security-middleware.ts" ]; then
    print_status "Security middleware exists"
else
    print_error "Security middleware missing"
fi

# 8. Rate limiting check
echo "8. Checking rate limiting implementation..."
RATE_LIMIT_FILES=$(find src -name "*rate*limit*" | wc -l)
if [ "$RATE_LIMIT_FILES" -gt "1" ]; then
    print_warning "Multiple rate limiting implementations found"
    print_warning "POTENTIAL ISSUE: Conflicting rate limiters"
    find src -name "*rate*limit*"
else
    print_status "Rate limiting implementation found"
fi

# Generate security report
echo "9. Generating security report..."
cat > SECURITY_STATUS.md << EOF
# Security Status Report
Generated: $(date)

## Test Results
- Failed Tests: $FAILED_TESTS
- Failed Suites: $FAILED_SUITES
- Console Statements: $CONSOLE_COUNT files

## Security Checklist
- [$([ "$FAILED_TESTS" -eq "0" ] && echo "x" || echo " ")] All tests passing
- [$([ "$CONSOLE_COUNT" -eq "0" ] && echo "x" || echo " ")] No console statements
- [$([ -f ".env.local" ] || [ -f ".env" ] && echo "x" || echo " ")] Environment configured
- [$(! grep -q "requireCSRF: false" src/app/api/tools/salary-calculator/route.ts && echo "x" || echo " ")] CSRF protection enabled
- [$(! grep -q "Math.random()" src/app/api/tools/salary-calculator/route.ts && echo "x" || echo " ")] No fake data generation

## Production Readiness
$([ "$FAILED_TESTS" -eq "0" ] && [ "$CONSOLE_COUNT" -eq "0" ] && echo "✅ READY FOR PRODUCTION" || echo "❌ NOT READY FOR PRODUCTION")
EOF

print_status "Security report generated: SECURITY_STATUS.md"

# Final assessment
echo ""
echo "🎯 FINAL SECURITY ASSESSMENT"
echo "============================"

if [ "$FAILED_TESTS" -eq "0" ] && [ "$CONSOLE_COUNT" -eq "0" ]; then
    print_status "SECURITY REMEDIATION SUCCESSFUL"
    print_status "Application may be ready for production deployment"
else
    print_error "SECURITY REMEDIATION INCOMPLETE"
    print_error "DO NOT DEPLOY TO PRODUCTION"
    echo ""
    echo "Remaining issues:"
    [ "$FAILED_TESTS" -gt "0" ] && print_error "- $FAILED_TESTS failed tests"
    [ "$CONSOLE_COUNT" -gt "0" ] && print_error "- $CONSOLE_COUNT files with console statements"
fi

echo ""
echo "📋 NEXT STEPS:"
echo "1. Review SECURITY_AUDIT_REPORT.md for detailed findings"
echo "2. Review SECURITY_STATUS.md for current status"
echo "3. Fix any remaining issues identified above"
echo "4. Re-run this script to verify fixes"
echo "5. Only deploy when all security checks pass"

echo ""
print_warning "REMEMBER: Security is not optional. Fix all issues before deployment."
