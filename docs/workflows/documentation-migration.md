---
title: "Documentation Migration Workflow"
category: "workflows"
tags: ["migration", "documentation", "atomic-design", "cleanup"]
last_updated: "2025-06-15"
last_validated: "2025-06-15"
dependencies: ["atoms/concepts/directory-structure.md", "atoms/concepts/naming-conventions.md"]
used_by: []
maintainer: "documentation-team"
ai_context: "Systematic workflow for migrating legacy documentation to atomic design system"
---

# Documentation Migration Workflow

This workflow guides the systematic migration of legacy documentation files to the new atomic design system.

## Migration Strategy

### **Phase 1: Analysis & Planning**

1. **Identify Legacy Files**
```bash
# Find problematic files
find docs/ -name "DOCUMENTATION_*" -type f
find docs/ -name "*_SUMMARY.md" -type f
find docs/ -name "*_COMPLETE.md" -type f
```

2. **Analyze Content Overlap**
```bash
# Generate usage graph
python3 scripts/generate-usage-graph.py

# Check for orphaned files
python3 scripts/validate-includes.py
```

### **Phase 2: Content Extraction**

{% include "atoms/concepts/directory-structure.md" %}

{% include "atoms/concepts/naming-conventions.md" %}

### **Phase 3: Atomic Content Creation**

For each legacy file:

1. **Extract Reusable Content**
   - Setup procedures → `atoms/setup/`
   - Commands → `atoms/commands/`
   - Concepts → `atoms/concepts/`
   - Procedures → `atoms/procedures/`

2. **Create Atomic Files**
```bash
# Example atomic content creation
docs/atoms/setup/environment.md
docs/atoms/commands/testing.md
docs/atoms/concepts/validation-system.md
docs/atoms/procedures/deployment-checklist.md
```

3. **Add Proper Metadata**
```yaml
---
title: "Clear, Descriptive Title"
category: "atoms"
subcategory: "setup|commands|concepts|procedures"
tags: ["tag1", "tag2", "tag3"]
last_updated: "2025-06-15"
last_validated: "2025-06-15"
dependencies: []
used_by: []
maintainer: "team-name"
ai_context: "Brief description for AI understanding"
---
```

### **Phase 4: Workflow Composition**

1. **Create Complete Workflows**
```bash
# Example workflows
docs/workflows/development-setup.md
docs/workflows/testing.md
docs/workflows/deployment.md
```

2. **Use Transclusion**
```markdown
{% include "atoms/setup/environment.md" %}
{% include "atoms/commands/testing.md" context="integration-testing" %}
```

### **Phase 5: Legacy File Archival**

1. **Move to Archives**
```bash
# Archive legacy files
mv docs/DOCUMENTATION_*.md docs/archives/
mv docs/*_SUMMARY.md docs/archives/
mv docs/*_COMPLETE.md docs/archives/
```

2. **Update References**
```bash
# Find and update broken links
python3 scripts/validate-includes.py
# Fix broken references to point to new atomic content
```

## Migration Checklist

### **Per Legacy File**
- [ ] Content analyzed for reusable components
- [ ] Atomic content extracted and created
- [ ] Proper metadata added to all atoms
- [ ] Workflows created that compose atoms
- [ ] Legacy file moved to archives
- [ ] References updated

### **System-Wide**
- [ ] All includes validate successfully
- [ ] No orphaned content
- [ ] Build system works without errors
- [ ] Usage graph shows proper relationships
- [ ] Documentation count reduced significantly

## Quality Gates

### **Before Migration**
- Legacy file contains useful content
- Content is not duplicated elsewhere
- Migration plan documented

### **After Migration**
- All atomic content has proper metadata
- Workflows compose correctly
- No broken includes or references
- Build system validates successfully

## Success Metrics

### **Quantitative**
- **File Count**: Reduced from 114 to 30-40 files
- **Metadata Completeness**: 100% (up from 10%)
- **Broken Links**: 0 (down from 74)
- **Orphaned Files**: < 5 (down from 54)

### **Qualitative**
- Clear navigation paths
- Consistent structure
- Reusable content blocks
- AI-optimized organization

## Validation Commands

```bash
# Validate metadata
python3 scripts/validate-metadata.py

# Validate includes
python3 scripts/validate-includes.py

# Build documentation
python3 scripts/build-composed-docs.py

# Generate usage graph
python3 scripts/generate-usage-graph.py
```

## Rollback Plan

If migration causes issues:

1. **Restore from Archives**
```bash
# Restore specific file
cp docs/archives/LEGACY_FILE.md docs/

# Restore all if needed
cp docs/archives/*.md docs/
```

2. **Validate System**
```bash
# Check system health
python3 scripts/validate-includes.py
```

---

**Migration Status**: In Progress
**Target Completion**: Week 3
**Quality Standard**: 100% validation passing
