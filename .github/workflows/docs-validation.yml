name: Documentation Validation

on:
  push:
    paths:
      - 'docs/**'
      - '.github/workflows/docs-validation.yml'
  pull_request:
    paths:
      - 'docs/**'
      - '.github/workflows/docs-validation.yml'

jobs:
  validate-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyyaml markdown beautifulsoup4 requests
        
    - name: Validate metadata
      run: python scripts/validate-metadata.py
      
    - name: Validate includes
      run: python scripts/validate-includes.py
      
    - name: Check link health
      run: python scripts/check-link-health.py
      
    - name: Validate structure
      run: |
        # Check for forbidden duplicate structures
        if [ -d "faafo-career-platform/docs" ]; then
          echo "❌ ERROR: Forbidden duplicate docs structure found"
          echo "Content should be in root docs/ directory only"
          exit 1
        fi
        
        # Check for required directories
        required_dirs=("docs/atoms" "docs/workflows" "docs/reference" "docs/archives")
        for dir in "${required_dirs[@]}"; do
          if [ ! -d "$dir" ]; then
            echo "❌ ERROR: Required directory $dir not found"
            exit 1
          fi
        done
        
        echo "✅ Documentation structure validation passed"
        
    - name: Generate usage graph
      run: python scripts/generate-usage-graph.py
      
    - name: Check for orphaned files
      run: |
        python scripts/find-orphaned-content.py
        
    - name: Validate freshness
      run: |
        python scripts/check-content-freshness.py --max-age 90
        
    - name: Build documentation
      run: |
        if [ -f "mkdocs.yml" ]; then
          pip install mkdocs mkdocs-material mkdocs-macros-plugin
          mkdocs build --strict
        fi
        
    - name: Generate metrics report
      run: |
        python scripts/generate-docs-metrics.py > docs-metrics.json
        
    - name: Upload metrics
      uses: actions/upload-artifact@v3
      with:
        name: documentation-metrics
        path: docs-metrics.json
        
    - name: Comment PR with metrics
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          if (fs.existsSync('docs-metrics.json')) {
            const metrics = JSON.parse(fs.readFileSync('docs-metrics.json', 'utf8'));
            const comment = `
            ## 📊 Documentation Metrics
            
            - **Total Files**: ${metrics.total_files}
            - **Atoms**: ${metrics.atoms_count}
            - **Workflows**: ${metrics.workflows_count}
            - **Metadata Completeness**: ${metrics.metadata_completeness}%
            - **Link Health**: ${metrics.link_health}%
            - **Content Freshness**: ${metrics.content_freshness}%
            - **Orphaned Files**: ${metrics.orphaned_files}
            
            ${metrics.validation_errors.length > 0 ? 
              '### ❌ Validation Errors\n' + metrics.validation_errors.join('\n') : 
              '### ✅ All validations passed!'}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Scan for sensitive information
      run: |
        # Check for accidentally committed sensitive data in docs
        if grep -r -i "password\|secret\|key\|token" docs/ --exclude-dir=archives; then
          echo "❌ WARNING: Potential sensitive information found in documentation"
          echo "Please review and remove any sensitive data"
          exit 1
        fi
        
        echo "✅ No sensitive information detected"
        
    - name: Validate external links security
      run: |
        python scripts/validate-external-links-security.py
