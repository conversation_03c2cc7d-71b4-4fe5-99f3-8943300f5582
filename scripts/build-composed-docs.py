#!/usr/bin/env python3
"""
Documentation Build System
Builds composed documentation from atomic content blocks with transclusion support.
"""

import os
import re
import yaml
from pathlib import Path
import shutil
import json
from datetime import datetime
import sys

class DocumentationBuilder:
    def __init__(self, docs_dir="docs", build_dir="site"):
        self.docs_dir = Path(docs_dir)
        self.build_dir = Path(build_dir)
        self.errors = []
        self.warnings = []
        self.processed_files = set()
        self.include_cache = {}
        
        # Include patterns
        self.include_patterns = [
            (r'{%\s*include\s+"([^"]+)"\s*(?:context="([^"]*)")?\s*%}', 'jinja'),
            (r'{%\s*include\s+\'([^\']+)\'\s*(?:context=\'([^\']*)\')?\s*%}', 'jinja'),
            (r'{{>\s*([^}]+)\s*}}', 'handlebars'),
        ]
        
    def extract_frontmatter(self, content):
        """Extract YAML frontmatter from content."""
        if not content.startswith('---'):
            return None, content
            
        parts = content.split('---', 2)
        if len(parts) < 3:
            return None, content
            
        try:
            frontmatter = yaml.safe_load(parts[1])
            body = parts[2].lstrip('\n')
            return frontmatter, body
        except yaml.YAMLError as e:
            self.errors.append(f"Invalid YAML frontmatter: {str(e)}")
            return None, content
            
    def resolve_include_path(self, include_path, current_file):
        """Resolve include path relative to current file or docs root."""
        current_dir = current_file.parent
        
        # Try relative to current file
        resolved = current_dir / include_path
        if resolved.exists():
            return resolved
            
        # Try relative to docs root
        resolved = self.docs_dir / include_path
        if resolved.exists():
            return resolved
            
        return None
        
    def process_includes(self, content, current_file, context=None):
        """Process include statements in content."""
        processed_content = content
        
        for pattern, include_type in self.include_patterns:
            def replace_include(match):
                include_path = match.group(1).strip()
                include_context = match.group(2) if match.lastindex > 1 else context
                
                resolved_path = self.resolve_include_path(include_path, current_file)
                if resolved_path is None:
                    error_msg = f"Include not found: '{include_path}' in {current_file.relative_to(self.docs_dir)}"
                    self.errors.append(error_msg)
                    return f"<!-- ERROR: {error_msg} -->"
                    
                # Check for circular includes
                if str(resolved_path) in self.processed_files:
                    error_msg = f"Circular include detected: {resolved_path.relative_to(self.docs_dir)}"
                    self.errors.append(error_msg)
                    return f"<!-- ERROR: {error_msg} -->"
                    
                # Load and process included content
                try:
                    with open(resolved_path, 'r', encoding='utf-8') as f:
                        include_content = f.read()
                        
                    # Extract frontmatter from included content
                    include_meta, include_body = self.extract_frontmatter(include_content)
                    
                    # Add to processed files to prevent circular includes
                    self.processed_files.add(str(resolved_path))
                    
                    # Recursively process includes in the included content
                    processed_include = self.process_includes(include_body, resolved_path, include_context)
                    
                    # Remove from processed files after processing
                    self.processed_files.discard(str(resolved_path))
                    
                    # Apply context if specified
                    if include_context and include_meta:
                        processed_include = self.apply_context(processed_include, include_context, include_meta)
                        
                    return processed_include
                    
                except Exception as e:
                    error_msg = f"Error processing include '{include_path}': {str(e)}"
                    self.errors.append(error_msg)
                    return f"<!-- ERROR: {error_msg} -->"
                    
            processed_content = re.sub(pattern, replace_include, processed_content, flags=re.MULTILINE)
            
        return processed_content
        
    def apply_context(self, content, context, metadata):
        """Apply context-specific modifications to included content."""
        # Context-specific processing can be added here
        # For example, filtering content based on context
        
        if context == "quick-start" and metadata:
            # For quick start context, only include essential steps
            lines = content.split('\n')
            filtered_lines = []
            skip_section = False
            
            for line in lines:
                if line.startswith('### Advanced') or line.startswith('## Advanced'):
                    skip_section = True
                elif line.startswith('#'):
                    skip_section = False
                    
                if not skip_section:
                    filtered_lines.append(line)
                    
            return '\n'.join(filtered_lines)
            
        return content
        
    def build_file(self, source_file, target_file):
        """Build a single documentation file."""
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Extract frontmatter
            frontmatter, body = self.extract_frontmatter(content)
            
            # Process includes
            self.processed_files.clear()
            processed_body = self.process_includes(body, source_file)
            
            # Rebuild content with frontmatter
            if frontmatter:
                # Add build metadata
                frontmatter['built_at'] = datetime.now().isoformat()
                frontmatter['source_file'] = str(source_file.relative_to(self.docs_dir))
                
                built_content = f"---\n{yaml.dump(frontmatter, default_flow_style=False)}---\n\n{processed_body}"
            else:
                built_content = processed_body
                
            # Ensure target directory exists
            target_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Write built file
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(built_content)
                
            return True
            
        except Exception as e:
            self.errors.append(f"Error building {source_file}: {str(e)}")
            return False
            
    def copy_static_files(self):
        """Copy static files that don't need processing."""
        static_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.svg', '.pdf', '.json'}
        
        for file_path in self.docs_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in static_extensions:
                relative_path = file_path.relative_to(self.docs_dir)
                target_path = self.build_dir / relative_path
                
                target_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(file_path, target_path)
                
    def generate_build_manifest(self):
        """Generate a manifest of built files."""
        manifest = {
            'build_time': datetime.now().isoformat(),
            'source_dir': str(self.docs_dir),
            'build_dir': str(self.build_dir),
            'files_built': [],
            'errors': self.errors,
            'warnings': self.warnings
        }
        
        for file_path in self.build_dir.rglob('*.md'):
            relative_path = file_path.relative_to(self.build_dir)
            manifest['files_built'].append(str(relative_path))
            
        manifest_path = self.build_dir / 'build-manifest.json'
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
            
    def build_all(self):
        """Build all documentation files."""
        print("🏗️  Building composed documentation...")
        
        # Clean build directory
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir(parents=True, exist_ok=True)
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return False
            
        print(f"Found {len(md_files)} markdown files to process")
        
        # Build each file
        built_count = 0
        for source_file in md_files:
            relative_path = source_file.relative_to(self.docs_dir)
            target_file = self.build_dir / relative_path
            
            if self.build_file(source_file, target_file):
                built_count += 1
                
        # Copy static files
        self.copy_static_files()
        
        # Generate build manifest
        self.generate_build_manifest()
        
        # Report results
        self.report_results(built_count, len(md_files))
        
        return len(self.errors) == 0
        
    def report_results(self, built_count, total_count):
        """Report build results."""
        print(f"\n📊 Build Results:")
        print(f"✅ Files built: {built_count}/{total_count}")
        print(f"📁 Build directory: {self.build_dir}")
        print(f"❌ Errors: {len(self.errors)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        if self.warnings:
            print(f"\n⚠️  Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
                
        if self.errors:
            print(f"\n💥 Build failed with {len(self.errors)} errors")
            sys.exit(1)
        else:
            print(f"\n✅ Documentation build completed successfully!")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Build composed documentation')
    parser.add_argument('--docs-dir', default='docs', help='Documentation source directory')
    parser.add_argument('--build-dir', default='site', help='Build output directory')
    
    args = parser.parse_args()
    
    builder = DocumentationBuilder(args.docs_dir, args.build_dir)
    success = builder.build_all()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
