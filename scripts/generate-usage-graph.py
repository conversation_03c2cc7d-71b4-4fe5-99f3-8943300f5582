#!/usr/bin/env python3
"""
Documentation Usage Graph Generator
Generates visual and data representations of documentation relationships and usage patterns.
"""

import os
import re
import json
import yaml
from pathlib import Path
from datetime import datetime
import sys

class UsageGraphGenerator:
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.dependency_graph = {}
        self.usage_graph = {}
        self.metadata_graph = {}
        self.errors = []
        
        # Include patterns to match
        self.include_patterns = [
            r'{%\s*include\s+"([^"]+)"\s*%}',
            r'{%\s*include\s+\'([^\']+)\'\s*%}',
            r'{{>\s*([^}]+)\s*}}',
        ]
        
        # Link patterns
        self.link_patterns = [
            r'\[([^\]]+)\]\(([^)]+\.md)\)',  # [text](file.md)
            r'\[([^\]]+)\]\(\.\.\/([^)]+\.md)\)',  # [text](../file.md)
        ]
        
    def extract_frontmatter(self, file_path):
        """Extract YAML frontmatter from markdown file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if not content.startswith('---'):
                return {}
                
            parts = content.split('---', 2)
            if len(parts) < 3:
                return {}
                
            return yaml.safe_load(parts[1]) or {}
            
        except Exception as e:
            self.errors.append(f"Error reading metadata from {file_path}: {str(e)}")
            return {}
    
    def find_relationships(self, file_path):
        """Find all relationships (includes and links) in a file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            self.errors.append(f"Error reading {file_path}: {str(e)}")
            return [], []
            
        includes = []
        links = []
        
        # Find includes
        for pattern in self.include_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                includes.append(match.group(1))
                
        # Find links
        for pattern in self.link_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                if pattern.endswith(r'\.md)\)'):  # Standard link
                    links.append(match.group(2))
                else:  # Relative link
                    links.append(match.group(2))
                    
        return includes, links
        
    def resolve_path(self, target_path, current_file):
        """Resolve target path relative to current file or docs root."""
        current_dir = current_file.parent
        
        # Try relative to current file
        resolved = current_dir / target_path
        if resolved.exists():
            return resolved.relative_to(self.docs_dir)
            
        # Try relative to docs root
        resolved = self.docs_dir / target_path
        if resolved.exists():
            return Path(target_path)
            
        return None
        
    def analyze_file(self, file_path):
        """Analyze a single file for relationships and metadata."""
        relative_path = file_path.relative_to(self.docs_dir)
        
        # Extract metadata
        metadata = self.extract_frontmatter(file_path)
        self.metadata_graph[str(relative_path)] = metadata
        
        # Find relationships
        includes, links = self.find_relationships(file_path)
        
        # Process includes (strong dependencies)
        dependencies = []
        for include_path in includes:
            resolved = self.resolve_path(include_path, file_path)
            if resolved:
                dependencies.append(str(resolved))
            else:
                self.errors.append(f"{relative_path}: Include not found: {include_path}")
                
        # Process links (weak dependencies)
        for link_path in links:
            resolved = self.resolve_path(link_path, file_path)
            if resolved:
                dependencies.append(str(resolved))
                
        # Store dependencies
        self.dependency_graph[str(relative_path)] = dependencies
        
        # Build reverse usage graph
        for dep in dependencies:
            if dep not in self.usage_graph:
                self.usage_graph[dep] = []
            self.usage_graph[dep].append(str(relative_path))
            
    def calculate_metrics(self):
        """Calculate usage and dependency metrics."""
        metrics = {
            'total_files': len(self.dependency_graph),
            'files_with_dependencies': len([f for f in self.dependency_graph.values() if f]),
            'total_dependencies': sum(len(deps) for deps in self.dependency_graph.values()),
            'orphaned_files': [],
            'most_used_files': [],
            'most_dependent_files': [],
            'circular_dependencies': [],
            'categories': {},
            'freshness': {}
        }
        
        # Find orphaned files (never used)
        all_files = set(self.dependency_graph.keys())
        used_files = set(self.usage_graph.keys())
        metrics['orphaned_files'] = list(all_files - used_files)
        
        # Most used files
        usage_counts = [(f, len(users)) for f, users in self.usage_graph.items()]
        metrics['most_used_files'] = sorted(usage_counts, key=lambda x: x[1], reverse=True)[:10]
        
        # Most dependent files
        dep_counts = [(f, len(deps)) for f, deps in self.dependency_graph.items()]
        metrics['most_dependent_files'] = sorted(dep_counts, key=lambda x: x[1], reverse=True)[:10]
        
        # Category analysis
        for file_path, metadata in self.metadata_graph.items():
            category = metadata.get('category', 'unknown')
            if category not in metrics['categories']:
                metrics['categories'][category] = 0
            metrics['categories'][category] += 1
            
        # Freshness analysis
        stale_files = []
        for file_path, metadata in self.metadata_graph.items():
            last_updated = metadata.get('last_updated')
            if last_updated:
                try:
                    update_date = datetime.strptime(str(last_updated), '%Y-%m-%d')
                    days_old = (datetime.now() - update_date).days
                    if days_old > 90:
                        stale_files.append((file_path, days_old))
                except:
                    pass
                    
        metrics['freshness']['stale_files'] = sorted(stale_files, key=lambda x: x[1], reverse=True)
        
        return metrics
        
    def generate_mermaid_graph(self):
        """Generate Mermaid diagram of documentation relationships."""
        mermaid = ["graph TD"]
        
        # Add nodes with categories
        for file_path, metadata in self.metadata_graph.items():
            category = metadata.get('category', 'unknown')
            node_id = file_path.replace('/', '_').replace('.md', '').replace('-', '_')
            title = metadata.get('title', file_path)
            
            # Style nodes by category
            if category == 'atoms':
                mermaid.append(f'    {node_id}["{title}"]:::atom')
            elif category == 'workflows':
                mermaid.append(f'    {node_id}["{title}"]:::workflow')
            elif category == 'reference':
                mermaid.append(f'    {node_id}["{title}"]:::reference')
            else:
                mermaid.append(f'    {node_id}["{title}"]')
                
        # Add relationships
        for file_path, dependencies in self.dependency_graph.items():
            if dependencies:
                source_id = file_path.replace('/', '_').replace('.md', '').replace('-', '_')
                for dep in dependencies:
                    target_id = dep.replace('/', '_').replace('.md', '').replace('-', '_')
                    mermaid.append(f'    {source_id} --> {target_id}')
                    
        # Add styles
        mermaid.extend([
            "    classDef atom fill:#e1f5fe,stroke:#01579b,stroke-width:2px",
            "    classDef workflow fill:#f3e5f5,stroke:#4a148c,stroke-width:2px",
            "    classDef reference fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px"
        ])
        
        return '\n'.join(mermaid)
        
    def generate_all(self):
        """Generate complete usage analysis."""
        print("📊 Generating documentation usage graph...")
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return False
            
        print(f"Analyzing {len(md_files)} files...")
        
        # Analyze each file
        for file_path in md_files:
            self.analyze_file(file_path)
            
        # Calculate metrics
        metrics = self.calculate_metrics()
        
        # Generate outputs
        self.save_graph_data(metrics)
        self.save_mermaid_diagram()
        self.generate_reports(metrics)
        
        # Report results
        self.report_results(metrics)
        
        return len(self.errors) == 0
        
    def save_graph_data(self, metrics):
        """Save graph data to JSON."""
        graph_data = {
            'generated_at': datetime.now().isoformat(),
            'dependencies': self.dependency_graph,
            'usage': self.usage_graph,
            'metadata': self.metadata_graph,
            'metrics': metrics
        }
        
        with open('docs-usage-graph.json', 'w') as f:
            json.dump(graph_data, f, indent=2)
            
    def save_mermaid_diagram(self):
        """Save Mermaid diagram to file."""
        mermaid_content = self.generate_mermaid_graph()
        
        with open('docs-relationship-diagram.mmd', 'w') as f:
            f.write(mermaid_content)
            
    def generate_reports(self, metrics):
        """Generate human-readable reports."""
        # Usage report
        with open('docs-usage-report.md', 'w') as f:
            f.write("# Documentation Usage Report\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Summary\n\n")
            f.write(f"- **Total Files**: {metrics['total_files']}\n")
            f.write(f"- **Files with Dependencies**: {metrics['files_with_dependencies']}\n")
            f.write(f"- **Total Dependencies**: {metrics['total_dependencies']}\n")
            f.write(f"- **Orphaned Files**: {len(metrics['orphaned_files'])}\n\n")
            
            if metrics['most_used_files']:
                f.write("## Most Used Files\n\n")
                for file_path, count in metrics['most_used_files']:
                    f.write(f"- **{file_path}**: {count} references\n")
                f.write("\n")
                
            if metrics['orphaned_files']:
                f.write("## Orphaned Files\n\n")
                for file_path in metrics['orphaned_files']:
                    f.write(f"- {file_path}\n")
                f.write("\n")
                
            if metrics['freshness']['stale_files']:
                f.write("## Stale Content (>90 days)\n\n")
                for file_path, days in metrics['freshness']['stale_files']:
                    f.write(f"- **{file_path}**: {days} days old\n")
                    
    def report_results(self, metrics):
        """Report generation results."""
        print(f"\n📊 Usage Graph Results:")
        print(f"✅ Files analyzed: {metrics['total_files']}")
        print(f"🔗 Total dependencies: {metrics['total_dependencies']}")
        print(f"📄 Files generated:")
        print(f"   - docs-usage-graph.json")
        print(f"   - docs-relationship-diagram.mmd")
        print(f"   - docs-usage-report.md")
        
        if metrics['most_used_files']:
            top_file = metrics['most_used_files'][0]
            print(f"🏆 Most used file: {top_file[0]} ({top_file[1]} references)")
            
        if metrics['orphaned_files']:
            print(f"⚠️  Orphaned files: {len(metrics['orphaned_files'])}")
            
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        if self.errors:
            print(f"\n💥 Graph generation failed with {len(self.errors)} errors")
            sys.exit(1)
        else:
            print(f"\n✅ Usage graph generated successfully!")

def main():
    generator = UsageGraphGenerator()
    generator.generate_all()

if __name__ == "__main__":
    main()
