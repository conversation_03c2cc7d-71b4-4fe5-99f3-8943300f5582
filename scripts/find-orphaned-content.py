#!/usr/bin/env python3
"""
Orphaned Content Finder
Identifies documentation files that are never referenced by other files.
"""

import os
import re
from pathlib import Path
import json
import sys

class OrphanedContentFinder:
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.all_files = set()
        self.referenced_files = set()
        self.errors = []
        
        # Link patterns to match
        self.link_patterns = [
            r'\[([^\]]+)\]\(([^)]+\.md)\)',  # [text](file.md)
            r'{%\s*include\s+"([^"]+\.md)"\s*%}',  # {% include "file.md" %}
            r'{%\s*include\s+\'([^\']+\.md)\'\s*%}',  # {% include 'file.md' %}
            r'{{>\s*([^}]+\.md)\s*}}',  # {{> file.md }}
        ]
        
        # Files that should be excluded from orphan check
        self.exclude_patterns = [
            'README.md',
            'index.md',
            'STYLE_GUIDE.md',
            r'archives/.*',  # Archive files are intentionally orphaned
            r'templates/.*',  # Template files are intentionally orphaned
            r'reference/.*',  # Reference files may be orphaned
        ]
        
    def should_exclude(self, file_path):
        """Check if file should be excluded from orphan check."""
        relative_path = str(file_path.relative_to(self.docs_dir))
        
        for pattern in self.exclude_patterns:
            if re.match(pattern, relative_path) or pattern == relative_path:
                return True
        return False
        
    def find_references(self, content, current_file):
        """Find all file references in content."""
        references = []
        
        for pattern in self.link_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                if pattern.startswith(r'\['):  # Markdown link
                    ref_path = match.group(2)
                else:  # Include statement
                    ref_path = match.group(1)
                    
                # Resolve reference path
                resolved = self.resolve_reference(ref_path, current_file)
                if resolved:
                    references.append(resolved)
                    
        return references
        
    def resolve_reference(self, ref_path, current_file):
        """Resolve reference path to actual file."""
        current_dir = current_file.parent
        
        # Remove anchor if present
        clean_path = ref_path.split('#')[0]
        if not clean_path:
            return None
            
        # Try relative to current file
        resolved = current_dir / clean_path
        if resolved.exists() and resolved.suffix == '.md':
            return resolved.relative_to(self.docs_dir)
            
        # Try relative to docs root
        resolved = self.docs_dir / clean_path
        if resolved.exists() and resolved.suffix == '.md':
            return Path(clean_path)
            
        return None
        
    def analyze_file(self, file_path):
        """Analyze a single file for references."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            self.errors.append(f"Error reading {file_path}: {str(e)}")
            return
            
        # Find all references in this file
        references = self.find_references(content, file_path)
        
        # Add to referenced files set
        for ref in references:
            self.referenced_files.add(str(ref))
            
    def find_orphaned_files(self):
        """Find all orphaned files."""
        print("🔍 Finding orphaned documentation files...")
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return []
            
        print(f"Analyzing {len(md_files)} files...")
        
        # Build set of all files
        for file_path in md_files:
            relative_path = str(file_path.relative_to(self.docs_dir))
            self.all_files.add(relative_path)
            
        # Analyze each file for references
        for file_path in md_files:
            self.analyze_file(file_path)
            
        # Find orphaned files
        orphaned = []
        for file_path in self.all_files:
            file_obj = self.docs_dir / file_path
            
            # Skip excluded files
            if self.should_exclude(file_obj):
                continue
                
            # Check if file is referenced
            if file_path not in self.referenced_files:
                orphaned.append(file_path)
                
        # Sort orphaned files
        orphaned.sort()
        
        # Report results
        self.report_results(orphaned)
        
        return orphaned
        
    def report_results(self, orphaned):
        """Report orphaned file analysis results."""
        print(f"\n📊 Orphaned Content Analysis:")
        print(f"✅ Total files: {len(self.all_files)}")
        print(f"🔗 Referenced files: {len(self.referenced_files)}")
        print(f"👻 Orphaned files: {len(orphaned)}")
        print(f"📈 Reference rate: {len(self.referenced_files)/len(self.all_files)*100:.1f}%")
        
        if orphaned:
            print(f"\n👻 Orphaned Files:")
            
            # Group by category
            categories = {}
            for file_path in orphaned:
                category = file_path.split('/')[0] if '/' in file_path else 'root'
                if category not in categories:
                    categories[category] = []
                categories[category].append(file_path)
                
            for category, files in sorted(categories.items()):
                print(f"\n  📁 {category}/ ({len(files)} files):")
                for file_path in sorted(files):
                    print(f"    - {file_path}")
                    
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        # Save detailed report
        self.save_orphaned_report(orphaned)
        
        if orphaned:
            print(f"\n⚠️  Found {len(orphaned)} orphaned files")
            print(f"💡 Consider:")
            print(f"   - Adding references to orphaned content")
            print(f"   - Moving unused files to archives/")
            print(f"   - Deleting truly unnecessary files")
        else:
            print(f"\n✅ No orphaned files found!")
            
    def save_orphaned_report(self, orphaned):
        """Save detailed orphaned files report."""
        report = {
            'generated_at': str(Path.cwd()),
            'total_files': len(self.all_files),
            'referenced_files': len(self.referenced_files),
            'orphaned_files': orphaned,
            'reference_rate': len(self.referenced_files)/len(self.all_files)*100 if self.all_files else 0,
            'errors': self.errors
        }
        
        with open('orphaned-content-report.json', 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"📄 Detailed report saved to orphaned-content-report.json")

def main():
    finder = OrphanedContentFinder()
    orphaned = finder.find_orphaned_files()
    
    # Exit with warning code if orphaned files found
    if orphaned:
        sys.exit(2)  # Warning exit code
    else:
        sys.exit(0)  # Success

if __name__ == "__main__":
    main()
