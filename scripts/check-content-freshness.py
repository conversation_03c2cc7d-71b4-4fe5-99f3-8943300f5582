#!/usr/bin/env python3
"""
Content Freshness Checker
Identifies documentation files that haven't been updated recently.
"""

import os
import yaml
from pathlib import Path
from datetime import datetime, timedelta
import sys
import argparse

class ContentFreshnessChecker:
    def __init__(self, docs_dir="docs", max_age_days=90):
        self.docs_dir = Path(docs_dir)
        self.max_age_days = max_age_days
        self.stale_files = []
        self.fresh_files = []
        self.no_date_files = []
        self.errors = []
        
    def extract_frontmatter(self, file_path):
        """Extract YAML frontmatter from markdown file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if not content.startswith('---'):
                return {}
                
            parts = content.split('---', 2)
            if len(parts) < 3:
                return {}
                
            return yaml.safe_load(parts[1]) or {}
            
        except Exception as e:
            self.errors.append(f"Error reading metadata from {file_path}: {str(e)}")
            return {}
    
    def get_file_dates(self, file_path, metadata):
        """Get relevant dates from file metadata."""
        dates = {}
        
        # Check for last_updated in metadata
        if 'last_updated' in metadata:
            try:
                dates['last_updated'] = datetime.strptime(str(metadata['last_updated']), '%Y-%m-%d')
            except ValueError:
                self.errors.append(f"{file_path}: Invalid last_updated format: {metadata['last_updated']}")
                
        # Check for last_validated in metadata
        if 'last_validated' in metadata:
            try:
                dates['last_validated'] = datetime.strptime(str(metadata['last_validated']), '%Y-%m-%d')
            except ValueError:
                self.errors.append(f"{file_path}: Invalid last_validated format: {metadata['last_validated']}")
                
        # Check for generated_date in metadata (for reference docs)
        if 'generated_date' in metadata:
            try:
                dates['generated_date'] = datetime.strptime(str(metadata['generated_date']), '%Y-%m-%d')
            except ValueError:
                self.errors.append(f"{file_path}: Invalid generated_date format: {metadata['generated_date']}")
                
        # Fall back to file modification time
        if not dates:
            try:
                mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                dates['file_mtime'] = mtime
            except Exception as e:
                self.errors.append(f"{file_path}: Cannot get file modification time: {str(e)}")
                
        return dates
        
    def check_file_freshness(self, file_path):
        """Check if a single file is fresh or stale."""
        relative_path = file_path.relative_to(self.docs_dir)
        
        # Skip certain files
        if file_path.name in ['README.md'] and file_path.parent == self.docs_dir:
            return  # Skip root README
            
        # Extract metadata
        metadata = self.extract_frontmatter(file_path)
        
        # Get dates
        dates = self.get_file_dates(file_path, metadata)
        
        if not dates:
            self.no_date_files.append({
                'path': str(relative_path),
                'category': metadata.get('category', 'unknown'),
                'title': metadata.get('title', str(relative_path))
            })
            return
            
        # Find the most recent date
        most_recent = max(dates.values())
        most_recent_type = [k for k, v in dates.items() if v == most_recent][0]
        
        # Calculate age
        age_days = (datetime.now() - most_recent).days
        
        file_info = {
            'path': str(relative_path),
            'category': metadata.get('category', 'unknown'),
            'title': metadata.get('title', str(relative_path)),
            'most_recent_date': most_recent.strftime('%Y-%m-%d'),
            'most_recent_type': most_recent_type,
            'age_days': age_days
        }
        
        if age_days > self.max_age_days:
            self.stale_files.append(file_info)
        else:
            self.fresh_files.append(file_info)
            
    def check_all_freshness(self):
        """Check freshness of all documentation files."""
        print(f"📅 Checking content freshness (max age: {self.max_age_days} days)...")
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return False
            
        print(f"Checking {len(md_files)} files...")
        
        for file_path in md_files:
            self.check_file_freshness(file_path)
            
        # Sort results by age (oldest first)
        self.stale_files.sort(key=lambda x: x['age_days'], reverse=True)
        self.fresh_files.sort(key=lambda x: x['age_days'], reverse=True)
        
        # Report results
        self.report_results()
        
        return len(self.stale_files) == 0
        
    def report_results(self):
        """Report freshness checking results."""
        total_files = len(self.stale_files) + len(self.fresh_files) + len(self.no_date_files)
        freshness_rate = len(self.fresh_files) / total_files * 100 if total_files > 0 else 0
        
        print(f"\n📊 Content Freshness Results:")
        print(f"✅ Total files checked: {total_files}")
        print(f"🌱 Fresh files (≤{self.max_age_days} days): {len(self.fresh_files)}")
        print(f"🍂 Stale files (>{self.max_age_days} days): {len(self.stale_files)}")
        print(f"❓ Files without dates: {len(self.no_date_files)}")
        print(f"📈 Freshness rate: {freshness_rate:.1f}%")
        
        if self.stale_files:
            print(f"\n🍂 Stale Files (oldest first):")
            
            # Group by category
            categories = {}
            for file_info in self.stale_files:
                category = file_info['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(file_info)
                
            for category, files in sorted(categories.items()):
                print(f"\n  📁 {category} ({len(files)} files):")
                for file_info in files:
                    print(f"    - {file_info['path']} ({file_info['age_days']} days old, {file_info['most_recent_type']})")
                    
        if self.no_date_files:
            print(f"\n❓ Files Without Date Metadata:")
            for file_info in self.no_date_files:
                print(f"  - {file_info['path']} ({file_info['category']})")
                
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        # Save detailed report
        self.save_freshness_report()
        
        if self.stale_files:
            print(f"\n⚠️  Found {len(self.stale_files)} stale files")
            print(f"💡 Consider updating files older than {self.max_age_days} days")
        else:
            print(f"\n✅ All files are fresh!")
            
    def save_freshness_report(self):
        """Save detailed freshness report."""
        report = {
            'generated_at': datetime.now().isoformat(),
            'max_age_days': self.max_age_days,
            'total_files': len(self.stale_files) + len(self.fresh_files) + len(self.no_date_files),
            'fresh_files': len(self.fresh_files),
            'stale_files': len(self.stale_files),
            'no_date_files': len(self.no_date_files),
            'freshness_rate': len(self.fresh_files) / (len(self.stale_files) + len(self.fresh_files) + len(self.no_date_files)) * 100 if (len(self.stale_files) + len(self.fresh_files) + len(self.no_date_files)) > 0 else 0,
            'stale_file_details': self.stale_files,
            'no_date_file_details': self.no_date_files,
            'errors': self.errors
        }
        
        with open('content-freshness-report.json', 'w') as f:
            import json
            json.dump(report, f, indent=2)
            
        print(f"📄 Detailed report saved to content-freshness-report.json")

def main():
    parser = argparse.ArgumentParser(description='Check documentation content freshness')
    parser.add_argument('--max-age', type=int, default=90, help='Maximum age in days before content is considered stale')
    parser.add_argument('--docs-dir', default='docs', help='Documentation directory')
    
    args = parser.parse_args()
    
    checker = ContentFreshnessChecker(args.docs_dir, args.max_age)
    success = checker.check_all_freshness()
    
    # Exit with warning code if stale files found
    if not success:
        sys.exit(2)  # Warning exit code
    else:
        sys.exit(0)  # Success

if __name__ == "__main__":
    main()
