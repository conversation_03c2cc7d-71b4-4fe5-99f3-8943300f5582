#!/usr/bin/env python3
"""
Documentation Link Health Checker
Validates that all internal and external links in documentation are working.
"""

import os
import re
import requests
from pathlib import Path
from urllib.parse import urljoin, urlparse
import sys
import time

class LinkHealthChecker:
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.errors = []
        self.warnings = []
        self.checked_urls = {}  # Cache for external URLs
        
        # Link patterns to match
        self.link_patterns = [
            r'\[([^\]]+)\]\(([^)]+)\)',  # [text](url)
            r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>',  # <a href="url">
            r'href=["\']([^"\']+)["\']',  # href="url"
        ]
        
        # Skip these URL patterns
        self.skip_patterns = [
            r'^mailto:',
            r'^tel:',
            r'^#',  # Anchors
            r'^javascript:',
            r'^\$\{',  # Template variables
        ]
        
    def should_skip_url(self, url):
        """Check if URL should be skipped."""
        for pattern in self.skip_patterns:
            if re.match(pattern, url, re.IGNORECASE):
                return True
        return False
        
    def find_links(self, content):
        """Find all links in content."""
        links = []
        
        for pattern in self.link_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if pattern.startswith(r'\['):  # Markdown link
                    url = match.group(2)
                else:  # HTML link
                    url = match.group(1)
                    
                if not self.should_skip_url(url):
                    links.append(url.strip())
                    
        return list(set(links))  # Remove duplicates
        
    def resolve_internal_link(self, link, current_file):
        """Resolve internal link relative to current file."""
        if link.startswith('http'):
            return None  # External link
            
        current_dir = current_file.parent
        
        # Remove anchor if present
        link_path = link.split('#')[0]
        if not link_path:  # Just an anchor
            return current_file  # Link to same file
            
        # Try relative to current file
        resolved = current_dir / link_path
        if resolved.exists():
            return resolved
            
        # Try relative to docs root
        resolved = self.docs_dir / link_path
        if resolved.exists():
            return resolved
            
        return None
        
    def check_external_url(self, url):
        """Check if external URL is accessible."""
        if url in self.checked_urls:
            return self.checked_urls[url]
            
        try:
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                
            response = requests.head(url, timeout=10, allow_redirects=True)
            success = response.status_code < 400
            
            self.checked_urls[url] = success
            
            if not success:
                self.warnings.append(f"External URL returned {response.status_code}: {url}")
                
            return success
            
        except requests.RequestException as e:
            self.checked_urls[url] = False
            self.warnings.append(f"External URL failed: {url} ({str(e)})")
            return False
            
    def check_file_links(self, file_path):
        """Check all links in a single file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            self.errors.append(f"Error reading {file_path}: {str(e)}")
            return
            
        relative_path = file_path.relative_to(self.docs_dir)
        links = self.find_links(content)
        
        for link in links:
            if link.startswith('http'):
                # External link - check if accessible
                if not self.check_external_url(link):
                    pass  # Warning already added in check_external_url
            else:
                # Internal link - check if file exists
                resolved = self.resolve_internal_link(link, file_path)
                if resolved is None:
                    self.errors.append(f"{relative_path}: Broken internal link: {link}")
                    
    def check_all_links(self):
        """Check links in all documentation files."""
        print("🔗 Checking documentation link health...")
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return False
            
        print(f"Checking links in {len(md_files)} files...")
        
        for file_path in md_files:
            self.check_file_links(file_path)
            
        # Report results
        self.report_results()
        
        return len(self.errors) == 0
        
    def report_results(self):
        """Report link checking results."""
        total_external = len(self.checked_urls)
        working_external = sum(1 for working in self.checked_urls.values() if working)
        
        print(f"\n📊 Link Health Results:")
        print(f"✅ Files checked: {len(list(self.docs_dir.rglob('*.md')))}")
        print(f"🔗 External URLs checked: {total_external}")
        print(f"✅ Working external URLs: {working_external}")
        print(f"❌ Broken external URLs: {total_external - working_external}")
        print(f"❌ Internal link errors: {len(self.errors)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ Internal Link Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        if self.warnings:
            print(f"\n⚠️  External Link Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
                
        if self.errors:
            print(f"\n💥 Link health check failed with {len(self.errors)} errors")
            sys.exit(1)
        else:
            print(f"\n✅ Link health check passed!")
            if self.warnings:
                print(f"   (with {len(self.warnings)} warnings)")

def main():
    checker = LinkHealthChecker()
    checker.check_all_links()

if __name__ == "__main__":
    main()
