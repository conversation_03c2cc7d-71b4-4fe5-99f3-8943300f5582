#!/usr/bin/env python3
"""
Documentation Metadata Validator
Validates that all documentation files have complete and correct metadata.
"""

import os
import yaml
import re
from pathlib import Path
from datetime import datetime, timedelta
import sys

class MetadataValidator:
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.errors = []
        self.warnings = []
        
        # Required metadata fields
        self.required_fields = {
            'atoms': ['title', 'category', 'subcategory', 'tags', 'last_updated', 'maintainer', 'ai_context'],
            'workflows': ['title', 'category', 'tags', 'last_updated', 'dependencies', 'maintainer', 'ai_context'],
            'reference': ['title', 'category', 'generated_date', 'generator', 'ai_context'],
            'archives': ['title', 'category', 'archived_date', 'reason', 'ai_context']
        }
        
        # Valid values for controlled fields
        self.valid_categories = ['atoms', 'workflows', 'reference', 'archives']
        self.valid_subcategories = ['setup', 'commands', 'concepts', 'procedures', 'testing', 'deployment', 'api', 'ui']
        
    def extract_frontmatter(self, file_path):
        """Extract YAML frontmatter from markdown file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for frontmatter
            if not content.startswith('---'):
                return None
                
            # Extract frontmatter
            parts = content.split('---', 2)
            if len(parts) < 3:
                return None
                
            frontmatter = parts[1].strip()
            return yaml.safe_load(frontmatter)
            
        except Exception as e:
            self.errors.append(f"Error reading {file_path}: {str(e)}")
            return None
    
    def validate_file(self, file_path):
        """Validate a single documentation file."""
        relative_path = file_path.relative_to(self.docs_dir)
        
        # Skip certain files
        if file_path.name in ['README.md', 'STYLE_GUIDE.md']:
            return
            
        metadata = self.extract_frontmatter(file_path)
        
        if metadata is None:
            self.errors.append(f"{relative_path}: Missing or invalid frontmatter")
            return
            
        # Determine expected category from path
        path_parts = relative_path.parts
        if len(path_parts) > 1:
            expected_category = path_parts[0]
        else:
            expected_category = 'workflows'  # Default for root files
            
        # Validate category
        category = metadata.get('category')
        if not category:
            self.errors.append(f"{relative_path}: Missing 'category' field")
        elif category not in self.valid_categories:
            self.errors.append(f"{relative_path}: Invalid category '{category}'. Must be one of: {self.valid_categories}")
        elif category != expected_category and expected_category in self.valid_categories:
            self.warnings.append(f"{relative_path}: Category '{category}' doesn't match path location '{expected_category}'")
            
        # Validate required fields based on category
        if category in self.required_fields:
            required = self.required_fields[category]
            for field in required:
                if field not in metadata:
                    self.errors.append(f"{relative_path}: Missing required field '{field}' for category '{category}'")
                    
        # Validate specific fields
        self.validate_dates(relative_path, metadata)
        self.validate_tags(relative_path, metadata)
        self.validate_dependencies(relative_path, metadata)
        self.validate_subcategory(relative_path, metadata)
        
    def validate_dates(self, file_path, metadata):
        """Validate date fields."""
        date_fields = ['last_updated', 'last_validated', 'generated_date', 'archived_date']
        
        for field in date_fields:
            if field in metadata:
                try:
                    date_str = metadata[field]
                    if isinstance(date_str, str):
                        datetime.strptime(date_str, '%Y-%m-%d')
                    elif not isinstance(date_str, datetime):
                        self.errors.append(f"{file_path}: Invalid date format for '{field}'. Use YYYY-MM-DD")
                except ValueError:
                    self.errors.append(f"{file_path}: Invalid date format for '{field}'. Use YYYY-MM-DD")
                    
        # Check if content is stale
        if 'last_updated' in metadata:
            try:
                last_updated = datetime.strptime(str(metadata['last_updated']), '%Y-%m-%d')
                if datetime.now() - last_updated > timedelta(days=90):
                    self.warnings.append(f"{file_path}: Content is stale (last updated {metadata['last_updated']})")
            except:
                pass
                
    def validate_tags(self, file_path, metadata):
        """Validate tags field."""
        if 'tags' in metadata:
            tags = metadata['tags']
            if not isinstance(tags, list):
                self.errors.append(f"{file_path}: 'tags' must be a list")
            elif len(tags) == 0:
                self.warnings.append(f"{file_path}: Empty tags list")
            elif len(tags) > 10:
                self.warnings.append(f"{file_path}: Too many tags ({len(tags)}). Consider reducing to 10 or fewer")
                
    def validate_dependencies(self, file_path, metadata):
        """Validate dependencies field."""
        if 'dependencies' in metadata:
            deps = metadata['dependencies']
            if not isinstance(deps, list):
                self.errors.append(f"{file_path}: 'dependencies' must be a list")
            else:
                for dep in deps:
                    if not isinstance(dep, str):
                        self.errors.append(f"{file_path}: Dependency '{dep}' must be a string")
                    elif not dep.endswith('.md'):
                        self.warnings.append(f"{file_path}: Dependency '{dep}' should end with .md")
                        
    def validate_subcategory(self, file_path, metadata):
        """Validate subcategory field."""
        if 'subcategory' in metadata:
            subcat = metadata['subcategory']
            if subcat not in self.valid_subcategories:
                self.warnings.append(f"{file_path}: Subcategory '{subcat}' not in recommended list: {self.valid_subcategories}")
                
    def validate_all(self):
        """Validate all documentation files."""
        print("🔍 Validating documentation metadata...")
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return
            
        print(f"Found {len(md_files)} markdown files")
        
        for file_path in md_files:
            self.validate_file(file_path)
            
        # Report results
        self.report_results()
        
    def report_results(self):
        """Report validation results."""
        print(f"\n📊 Validation Results:")
        print(f"✅ Files processed: {len(list(self.docs_dir.rglob('*.md')))}")
        print(f"❌ Errors: {len(self.errors)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        if self.warnings:
            print(f"\n⚠️  Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
                
        if self.errors:
            print(f"\n💥 Validation failed with {len(self.errors)} errors")
            sys.exit(1)
        else:
            print(f"\n✅ Metadata validation passed!")
            if self.warnings:
                print(f"   (with {len(self.warnings)} warnings)")

def main():
    validator = MetadataValidator()
    validator.validate_all()

if __name__ == "__main__":
    main()
