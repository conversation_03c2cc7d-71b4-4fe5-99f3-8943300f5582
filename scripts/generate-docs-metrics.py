#!/usr/bin/env python3
"""
Documentation Metrics Generator
Generates comprehensive metrics and health dashboard for documentation system.
"""

import os
import json
import yaml
from pathlib import Path
from datetime import datetime
import subprocess
import sys

class DocumentationMetrics:
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.metrics = {
            'generated_at': datetime.now().isoformat(),
            'docs_directory': str(self.docs_dir),
            'total_files': 0,
            'atoms_count': 0,
            'workflows_count': 0,
            'reference_count': 0,
            'archives_count': 0,
            'metadata_completeness': 0,
            'link_health': 0,
            'content_freshness': 0,
            'orphaned_files': 0,
            'build_success': False,
            'validation_errors': [],
            'quality_score': 0,
            'categories': {},
            'file_breakdown': {},
            'top_referenced_files': [],
            'system_health': 'unknown'
        }
        
    def count_files_by_category(self):
        """Count files by category and type."""
        md_files = list(self.docs_dir.rglob("*.md"))
        self.metrics['total_files'] = len(md_files)
        
        categories = {}
        file_breakdown = {
            'atoms': {'setup': 0, 'commands': 0, 'concepts': 0, 'procedures': 0},
            'workflows': 0,
            'reference': 0,
            'archives': 0,
            'legacy': 0
        }
        
        for file_path in md_files:
            relative_path = file_path.relative_to(self.docs_dir)
            path_parts = relative_path.parts
            
            # Categorize by directory structure
            if len(path_parts) > 1:
                category = path_parts[0]
                
                if category == 'atoms':
                    self.metrics['atoms_count'] += 1
                    if len(path_parts) > 2:
                        subcategory = path_parts[1]
                        if subcategory in file_breakdown['atoms']:
                            file_breakdown['atoms'][subcategory] += 1
                elif category == 'workflows':
                    self.metrics['workflows_count'] += 1
                    file_breakdown['workflows'] += 1
                elif category == 'reference':
                    self.metrics['reference_count'] += 1
                    file_breakdown['reference'] += 1
                elif category == 'archives':
                    self.metrics['archives_count'] += 1
                    file_breakdown['archives'] += 1
                else:
                    file_breakdown['legacy'] += 1
                    
                categories[category] = categories.get(category, 0) + 1
            else:
                file_breakdown['legacy'] += 1
                categories['root'] = categories.get('root', 0) + 1
                
        self.metrics['categories'] = categories
        self.metrics['file_breakdown'] = file_breakdown
        
    def check_metadata_completeness(self):
        """Check metadata completeness across all files."""
        md_files = list(self.docs_dir.rglob("*.md"))
        files_with_metadata = 0
        
        for file_path in md_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if content.startswith('---'):
                    parts = content.split('---', 2)
                    if len(parts) >= 3:
                        try:
                            metadata = yaml.safe_load(parts[1])
                            if metadata and isinstance(metadata, dict):
                                # Check for required fields
                                required_fields = ['title', 'category', 'last_updated']
                                if all(field in metadata for field in required_fields):
                                    files_with_metadata += 1
                        except yaml.YAMLError:
                            pass
            except Exception:
                pass
                
        if md_files:
            self.metrics['metadata_completeness'] = (files_with_metadata / len(md_files)) * 100
        else:
            self.metrics['metadata_completeness'] = 0
            
    def run_validation_checks(self):
        """Run validation scripts and collect results."""
        validation_results = {}
        
        # Run metadata validation
        try:
            result = subprocess.run(
                ['python3', 'scripts/validate-metadata.py'],
                capture_output=True,
                text=True,
                timeout=60
            )
            validation_results['metadata'] = {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
        except Exception as e:
            validation_results['metadata'] = {
                'success': False,
                'output': f"Error running metadata validation: {str(e)}"
            }
            
        # Run includes validation
        try:
            result = subprocess.run(
                ['python3', 'scripts/validate-includes.py'],
                capture_output=True,
                text=True,
                timeout=60
            )
            validation_results['includes'] = {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
        except Exception as e:
            validation_results['includes'] = {
                'success': False,
                'output': f"Error running includes validation: {str(e)}"
            }
            
        # Run build test
        try:
            result = subprocess.run(
                ['python3', 'scripts/build-composed-docs.py', '--docs-dir', 'docs', '--build-dir', 'site-test'],
                capture_output=True,
                text=True,
                timeout=120
            )
            validation_results['build'] = {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
            self.metrics['build_success'] = result.returncode == 0
        except Exception as e:
            validation_results['build'] = {
                'success': False,
                'output': f"Error running build test: {str(e)}"
            }
            self.metrics['build_success'] = False
            
        # Collect validation errors
        errors = []
        for check, result in validation_results.items():
            if not result['success']:
                errors.append(f"{check}: {result['output'][:200]}...")
                
        self.metrics['validation_errors'] = errors
        
        return validation_results
        
    def load_usage_data(self):
        """Load usage data from usage graph if available."""
        usage_file = Path('docs-usage-graph.json')
        if usage_file.exists():
            try:
                with open(usage_file, 'r') as f:
                    usage_data = json.load(f)
                    
                # Extract top referenced files
                if 'metrics' in usage_data and 'most_used_files' in usage_data['metrics']:
                    self.metrics['top_referenced_files'] = usage_data['metrics']['most_used_files'][:5]
                    
                # Extract orphaned files count
                if 'metrics' in usage_data and 'orphaned_files' in usage_data['metrics']:
                    self.metrics['orphaned_files'] = len(usage_data['metrics']['orphaned_files'])
                    
            except Exception as e:
                self.metrics['validation_errors'].append(f"Error loading usage data: {str(e)}")
                
    def calculate_quality_score(self):
        """Calculate overall quality score."""
        score = 0
        max_score = 100
        
        # Metadata completeness (30 points)
        score += (self.metrics['metadata_completeness'] / 100) * 30
        
        # Build success (25 points)
        if self.metrics['build_success']:
            score += 25
            
        # Atomic structure adoption (20 points)
        total_content = self.metrics['atoms_count'] + self.metrics['workflows_count']
        if self.metrics['total_files'] > 0:
            atomic_ratio = total_content / self.metrics['total_files']
            score += min(atomic_ratio * 20, 20)
            
        # Low orphaned files (15 points)
        if self.metrics['total_files'] > 0:
            orphan_ratio = self.metrics['orphaned_files'] / self.metrics['total_files']
            score += max(0, (1 - orphan_ratio) * 15)
            
        # No validation errors (10 points)
        if not self.metrics['validation_errors']:
            score += 10
            
        self.metrics['quality_score'] = min(score, max_score)
        
        # Determine system health
        if score >= 90:
            self.metrics['system_health'] = 'excellent'
        elif score >= 75:
            self.metrics['system_health'] = 'good'
        elif score >= 60:
            self.metrics['system_health'] = 'fair'
        else:
            self.metrics['system_health'] = 'poor'
            
    def generate_metrics(self):
        """Generate complete metrics."""
        print("📊 Generating documentation metrics...")
        
        # Count files and categories
        self.count_files_by_category()
        
        # Check metadata completeness
        self.check_metadata_completeness()
        
        # Run validation checks
        validation_results = self.run_validation_checks()
        
        # Load usage data
        self.load_usage_data()
        
        # Calculate quality score
        self.calculate_quality_score()
        
        # Report results
        self.report_metrics()
        
        return self.metrics
        
    def report_metrics(self):
        """Report metrics summary."""
        print(f"\n📊 Documentation System Metrics:")
        print(f"🏆 Quality Score: {self.metrics['quality_score']:.1f}/100 ({self.metrics['system_health']})")
        print(f"📄 Total Files: {self.metrics['total_files']}")
        print(f"⚛️  Atomic Content: {self.metrics['atoms_count']} atoms, {self.metrics['workflows_count']} workflows")
        print(f"📋 Metadata Completeness: {self.metrics['metadata_completeness']:.1f}%")
        print(f"🏗️  Build Success: {'✅' if self.metrics['build_success'] else '❌'}")
        print(f"👻 Orphaned Files: {self.metrics['orphaned_files']}")
        print(f"❌ Validation Errors: {len(self.metrics['validation_errors'])}")
        
        if self.metrics['top_referenced_files']:
            print(f"\n🏆 Most Referenced Files:")
            for file_path, count in self.metrics['top_referenced_files']:
                print(f"  - {file_path}: {count} references")
                
        if self.metrics['validation_errors']:
            print(f"\n❌ Validation Issues:")
            for error in self.metrics['validation_errors'][:3]:  # Show first 3
                print(f"  - {error}")
            if len(self.metrics['validation_errors']) > 3:
                print(f"  ... and {len(self.metrics['validation_errors']) - 3} more")

def main():
    generator = DocumentationMetrics()
    metrics = generator.generate_metrics()
    
    # Output metrics as JSON for CI/CD
    print(json.dumps(metrics, indent=2))

if __name__ == "__main__":
    main()
