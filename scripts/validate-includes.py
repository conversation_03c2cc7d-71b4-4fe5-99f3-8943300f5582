#!/usr/bin/env python3
"""
Documentation Includes Validator
Validates that all include statements resolve to existing files and creates dependency graphs.
"""

import os
import re
from pathlib import Path
import json
import sys

class IncludesValidator:
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.errors = []
        self.warnings = []
        self.dependency_graph = {}
        self.usage_graph = {}
        
        # Include patterns to match
        self.include_patterns = [
            r'{%\s*include\s+"([^"]+)"\s*%}',  # {% include "file.md" %}
            r'{%\s*include\s+\'([^\']+)\'\s*%}',  # {% include 'file.md' %}
            r'{{>\s*([^}]+)\s*}}',  # {{> file.md }}
            r'\[([^\]]+)\]\(([^)]+\.md)\)',  # [text](file.md)
        ]
        
    def find_includes(self, content, file_path):
        """Find all include statements in content."""
        includes = []
        
        for pattern in self.include_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                if pattern.endswith(r'\.md)\)'):  # Link pattern
                    include_path = match.group(2)
                else:
                    include_path = match.group(1)
                    
                includes.append({
                    'path': include_path,
                    'line': content[:match.start()].count('\n') + 1,
                    'match': match.group(0)
                })
                
        return includes
        
    def resolve_include_path(self, include_path, current_file):
        """Resolve include path relative to current file or docs root."""
        current_dir = current_file.parent
        
        # Try relative to current file
        resolved = current_dir / include_path
        if resolved.exists():
            return resolved
            
        # Try relative to docs root
        resolved = self.docs_dir / include_path
        if resolved.exists():
            return resolved
            
        # Try with docs/ prefix if not already present
        if not include_path.startswith('docs/'):
            resolved = self.docs_dir / f"docs/{include_path}"
            if resolved.exists():
                return resolved
                
        return None
        
    def validate_file(self, file_path):
        """Validate includes in a single file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            self.errors.append(f"Error reading {file_path}: {str(e)}")
            return
            
        relative_path = file_path.relative_to(self.docs_dir)
        includes = self.find_includes(content, file_path)
        
        if not includes:
            return
            
        # Initialize dependency tracking
        self.dependency_graph[str(relative_path)] = []
        
        for include in includes:
            include_path = include['path']
            resolved_path = self.resolve_include_path(include_path, file_path)
            
            if resolved_path is None:
                self.errors.append(
                    f"{relative_path}:{include['line']}: Include not found: '{include_path}'"
                )
            else:
                # Track dependency
                resolved_relative = resolved_path.relative_to(self.docs_dir)
                self.dependency_graph[str(relative_path)].append(str(resolved_relative))
                
                # Track usage (reverse dependency)
                if str(resolved_relative) not in self.usage_graph:
                    self.usage_graph[str(resolved_relative)] = []
                self.usage_graph[str(resolved_relative)].append(str(relative_path))
                
                # Validate include file exists and is readable
                try:
                    with open(resolved_path, 'r', encoding='utf-8') as f:
                        include_content = f.read()
                        
                    # Check for circular dependencies
                    if self.has_circular_dependency(str(relative_path), str(resolved_relative)):
                        self.errors.append(
                            f"{relative_path}: Circular dependency detected with {resolved_relative}"
                        )
                        
                except Exception as e:
                    self.errors.append(
                        f"{relative_path}:{include['line']}: Cannot read included file '{resolved_relative}': {str(e)}"
                    )
                    
    def has_circular_dependency(self, file1, file2):
        """Check for circular dependencies between two files."""
        def check_path(current, target, visited):
            if current == target:
                return True
            if current in visited:
                return False
                
            visited.add(current)
            
            for dep in self.dependency_graph.get(current, []):
                if check_path(dep, target, visited.copy()):
                    return True
                    
            return False
            
        return check_path(file2, file1, set())
        
    def find_orphaned_files(self):
        """Find files that are never included by others."""
        all_files = set(str(f.relative_to(self.docs_dir)) for f in self.docs_dir.rglob("*.md"))
        referenced_files = set(self.usage_graph.keys())
        
        # Exclude certain files from orphan check
        exclude_patterns = [
            'README.md',
            'STYLE_GUIDE.md',
            'workflows/',  # Workflows are entry points
            'archives/',   # Archives are intentionally orphaned
        ]
        
        orphaned = []
        for file_path in all_files:
            if file_path not in referenced_files:
                # Check if it should be excluded
                should_exclude = any(
                    pattern in file_path for pattern in exclude_patterns
                )
                if not should_exclude:
                    orphaned.append(file_path)
                    
        return orphaned
        
    def validate_all(self):
        """Validate all documentation files."""
        print("🔍 Validating documentation includes...")
        
        # Find all markdown files
        md_files = list(self.docs_dir.rglob("*.md"))
        
        if not md_files:
            self.errors.append("No markdown files found in docs directory")
            return
            
        print(f"Found {len(md_files)} markdown files")
        
        for file_path in md_files:
            self.validate_file(file_path)
            
        # Find orphaned files
        orphaned = self.find_orphaned_files()
        if orphaned:
            self.warnings.extend([f"Orphaned file (never included): {f}" for f in orphaned])
            
        # Generate dependency graph
        self.save_dependency_graph()
        
        # Report results
        self.report_results()
        
    def save_dependency_graph(self):
        """Save dependency graph to JSON file."""
        graph_data = {
            'dependencies': self.dependency_graph,
            'usage': self.usage_graph,
            'generated_at': str(Path.cwd()),
            'stats': {
                'total_files': len(self.dependency_graph),
                'files_with_dependencies': len([f for f in self.dependency_graph.values() if f]),
                'most_used_files': sorted(
                    [(f, len(users)) for f, users in self.usage_graph.items()],
                    key=lambda x: x[1],
                    reverse=True
                )[:10]
            }
        }
        
        with open('docs-dependency-graph.json', 'w') as f:
            json.dump(graph_data, f, indent=2)
            
        print(f"📊 Dependency graph saved to docs-dependency-graph.json")
        
    def report_results(self):
        """Report validation results."""
        print(f"\n📊 Include Validation Results:")
        print(f"✅ Files processed: {len(list(self.docs_dir.rglob('*.md')))}")
        print(f"🔗 Files with dependencies: {len([f for f in self.dependency_graph.values() if f])}")
        print(f"📄 Total dependencies: {sum(len(deps) for deps in self.dependency_graph.values())}")
        print(f"❌ Errors: {len(self.errors)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        
        if self.usage_graph:
            most_used = max(self.usage_graph.items(), key=lambda x: len(x[1]))
            print(f"🏆 Most included file: {most_used[0]} ({len(most_used[1])} times)")
        
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"  - {error}")
                
        if self.warnings:
            print(f"\n⚠️  Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
                
        if self.errors:
            print(f"\n💥 Include validation failed with {len(self.errors)} errors")
            sys.exit(1)
        else:
            print(f"\n✅ Include validation passed!")
            if self.warnings:
                print(f"   (with {len(self.warnings)} warnings)")

def main():
    validator = IncludesValidator()
    validator.validate_all()

if __name__ == "__main__":
    main()
