{"name": "faafo-monorepo", "version": "1.0.0", "private": true, "scripts": {"build": "cd faafo-career-platform && npm run build", "start": "cd faafo-career-platform && npm start", "dev": "cd faafo-career-platform && npm run dev"}, "workspaces": ["faafo-career-platform"], "dependencies": {"@sentry/nextjs": "^9.28.0", "isomorphic-dompurify": "^2.25.0", "next": "^15.3.3"}, "devDependencies": {"identity-obj-proxy": "^3.0.0"}}