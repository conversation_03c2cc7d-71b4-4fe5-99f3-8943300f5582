#!/usr/bin/env python3
"""
Check what authentication elements are present after login
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
import time

def check_auth_elements():
    """Check what elements are present after successful login"""
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        print("🔍 Checking authentication elements after login...")
        
        # Login first
        print("1️⃣ Logging in...")
        page.goto("http://localhost:3002/login")
        page.wait_for_load_state('networkidle')
        
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        print(f"2️⃣ Current URL: {page.url}")
        
        # Check for all possible authentication indicators
        print("3️⃣ Checking for authentication indicators...")
        
        selectors_to_check = [
            "button:has-text('Sign Out')",
            "button:has-text('Sign out')", 
            "button:has-text('Logout')",
            "button:has-text('Log out')",
            "a:has-text('Profile')",
            "a:has-text('profile')",
            "a[href='/profile']",
            "a[href*='profile']",
            ".user-menu",
            ".profile-menu",
            "[data-testid='user-menu']",
            "[data-testid='logout']",
            "[data-testid='signout']",
            ".logout",
            ".signout",
            ".user-name",
            ".username"
        ]
        
        found_elements = []
        for selector in selectors_to_check:
            element = page.query_selector(selector)
            if element:
                text = element.text_content() or ""
                found_elements.append(f"   ✅ {selector}: '{text.strip()}'")
        
        if found_elements:
            print("   Found authentication indicators:")
            for elem in found_elements:
                print(elem)
        else:
            print("   ❌ No authentication indicators found!")
        
        # Check all buttons and links
        print("4️⃣ All buttons on page:")
        buttons = page.query_selector_all("button")
        for i, btn in enumerate(buttons[:10]):  # First 10 buttons
            text = btn.text_content() or ""
            if text.strip():
                print(f"   Button {i+1}: '{text.strip()}'")
        
        print("5️⃣ All links on page:")
        links = page.query_selector_all("a")
        for i, link in enumerate(links[:10]):  # First 10 links
            text = link.text_content() or ""
            href = link.get_attribute("href") or ""
            if text.strip():
                print(f"   Link {i+1}: '{text.strip()}' -> {href}")
        
        time.sleep(5)
        browser.close()

if __name__ == "__main__":
    check_auth_elements()
