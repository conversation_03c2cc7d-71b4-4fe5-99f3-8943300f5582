/* Enhanced Testerat Report Styles */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --text-color: #333;
    --bg-color: #f5f5f5;
    --card-bg: #ffffff;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background: var(--bg-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

.header .subtitle {
    font-size: 1.2em;
    opacity: 0.9;
    margin-bottom: 5px;
}

/* Grid Layouts */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: var(--card-bg);
    padding: 25px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
}

.summary-card h3 {
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px;
    font-weight: 600;
}

.summary-card .value {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

/* Status Colors */
.success { color: var(--success-color); }
.warning { color: var(--warning-color); }
.error { color: var(--error-color); }

/* Section Styles */
.section {
    background: var(--card-bg);
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    color: #495057;
    font-size: 1.5em;
    font-weight: 600;
}

.section-content {
    padding: 20px;
}

/* Test Result Styles */
.test-result {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    border-left: 4px solid;
    transition: all 0.2s ease;
}

.test-result:hover {
    transform: translateX(2px);
}

.test-result.passed {
    background: #d4edda;
    border-color: var(--success-color);
}

.test-result.failed {
    background: #f8d7da;
    border-color: var(--error-color);
}

.test-result.skipped {
    background: #fff3cd;
    border-color: var(--warning-color);
}

.test-result h4 {
    margin-bottom: 8px;
    font-size: 1.1em;
    font-weight: 600;
}

.test-result .details {
    color: #666;
    margin-bottom: 10px;
    line-height: 1.5;
}

/* Recommendations */
.recommendations {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
    border-left: 3px solid #2196f3;
}

.recommendations h5 {
    color: #1976d2;
    margin-bottom: 8px;
    font-weight: 600;
}

.recommendations ul {
    margin-left: 20px;
}

.recommendations li {
    margin-bottom: 4px;
}

/* Chart Container */
.chart-container {
    height: 400px;
    margin: 20px 0;
    position: relative;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 8px;
}

.badge.critical { 
    background: var(--error-color); 
    color: white; 
}

.badge.high { 
    background: var(--warning-color); 
    color: white; 
}

.badge.medium { 
    background: #3498db; 
    color: white; 
}

.badge.low { 
    background: #95a5a6; 
    color: white; 
}

/* Footer */
.footer {
    text-align: center;
    padding: 30px;
    color: #666;
    border-top: 1px solid var(--border-color);
    margin-top: 30px;
    background: var(--card-bg);
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .header .subtitle {
        font-size: 1em;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-card .value {
        font-size: 2em;
    }
    
    .section-content {
        padding: 15px;
    }
    
    .test-result {
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 1.8em;
    }
    
    .summary-card {
        padding: 20px;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
    }
    
    .header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
    }
    
    .section {
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
    
    .test-result {
        break-inside: avoid;
    }
    
    .chart-container {
        height: 300px;
    }
}
