<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Testerat Report - {{session.app_name}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .summary-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .success { color: #27ae60; }
        .warning { color: #f39c12; }
        .error { color: #e74c3c; }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .test-result {
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .test-result.passed {
            background: #d4edda;
            border-color: #27ae60;
        }
        
        .test-result.failed {
            background: #f8d7da;
            border-color: #e74c3c;
        }
        
        .test-result.skipped {
            background: #fff3cd;
            border-color: #f39c12;
        }
        
        .test-result h4 {
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .test-result .details {
            color: #666;
            margin-bottom: 10px;
        }
        
        .recommendations {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .recommendations h5 {
            color: #1976d2;
            margin-bottom: 8px;
        }
        
        .recommendations ul {
            margin-left: 20px;
        }
        
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .badge.critical { background: #e74c3c; color: white; }
        .badge.high { background: #f39c12; color: white; }
        .badge.medium { background: #3498db; color: white; }
        .badge.low { background: #95a5a6; color: white; }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎯 Enhanced Testerat Report</h1>
            <div class="subtitle">{{session.app_name}} - {{session.url}}</div>
            <div class="subtitle">Framework: {{session.framework}} | {{session.execution_time}}s</div>
        </div>
        
        <!-- Summary Cards -->
        <div class="summary-grid">
            <div class="summary-card">
                <h3>Total Tests</h3>
                <div class="value">{{summary.total_tests}}</div>
            </div>
            <div class="summary-card">
                <h3>Passed</h3>
                <div class="value success">{{summary.passed}}</div>
            </div>
            <div class="summary-card">
                <h3>Failed</h3>
                <div class="value error">{{summary.failed}}</div>
            </div>
            <div class="summary-card">
                <h3>Success Rate</h3>
                <div class="value {{summary.success_rate >= 80 and 'success' or (summary.success_rate >= 60 and 'warning' or 'error')}}">
                    {{summary.success_rate}}%
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="section">
            <div class="section-header">
                <h2>📊 Test Results Overview</h2>
            </div>
            <div class="section-content">
                <div class="chart-container">
                    <canvas id="resultsChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Critical Issues -->
        {% if critical_issues %}
        <div class="section">
            <div class="section-header">
                <h2>🚨 Critical Issues</h2>
            </div>
            <div class="section-content">
                {% for issue in critical_issues %}
                <div class="test-result failed">
                    <h4>{{issue.test_name}} <span class="badge critical">{{issue.severity}}</span></h4>
                    <div class="details">{{issue.details}}</div>
                    {% if issue.recommendations %}
                    <div class="recommendations">
                        <h5>💡 Recommendations:</h5>
                        <ul>
                            {% for rec in issue.recommendations %}
                            <li>{{rec}}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- All Test Results -->
        <div class="section">
            <div class="section-header">
                <h2>📋 Detailed Test Results</h2>
            </div>
            <div class="section-content">
                {% for result in all_results %}
                <div class="test-result {{result.status.lower()}}">
                    <h4>{{result.test_name}} <span class="badge {{result.severity.lower()}}">{{result.severity}}</span></h4>
                    <div class="details">{{result.details}}</div>
                    <div style="font-size: 0.9em; color: #888;">
                        Execution time: {{result.execution_time}}s
                        {% if result.framework_detected %}| Framework: {{result.framework_detected}}{% endif %}
                    </div>
                    {% if result.recommendations %}
                    <div class="recommendations">
                        <h5>💡 Recommendations:</h5>
                        <ul>
                            {% for rec in result.recommendations %}
                            <li>{{rec}}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>Generated by Enhanced Testerat v2.0.0 | {{timestamp}}</p>
            <p>Universal Web Testing Framework</p>
        </div>
    </div>
    
    <script>
        // Results Chart
        const ctx = document.getElementById('resultsChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed', 'Skipped'],
                datasets: [{
                    data: [{{summary.passed}}, {{summary.failed}}, {{summary.skipped or 0}}],
                    backgroundColor: ['#27ae60', '#e74c3c', '#f39c12'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
