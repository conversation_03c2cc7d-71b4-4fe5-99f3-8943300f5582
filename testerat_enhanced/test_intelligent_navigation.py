#!/usr/bin/env python3
"""
Intelligent App Navigation Testing
Uses actual app structure and component knowledge to navigate properly
"""

from playwright.sync_api import sync_playwright
import time
import j<PERSON>

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        return True
    return False

def smart_element_finder(page, element_descriptions):
    """
    Intelligently find elements using multiple strategies
    """
    for desc in element_descriptions:
        # Strategy 1: Direct selector
        if 'selector' in desc:
            element = page.query_selector(desc['selector'])
            if element:
                return element
        
        # Strategy 2: Text content
        if 'text' in desc:
            element = page.query_selector(f'text="{desc["text"]}"')
            if element:
                return element
            # Try partial text match using :has-text()
            element = page.query_selector(f':has-text("{desc["text"]}")')
            if element:
                return element
        
        # Strategy 3: Button with text
        if 'button_text' in desc:
            element = page.query_selector(f'button:has-text("{desc["button_text"]}")')
            if element:
                return element
        
        # Strategy 4: Role-based
        if 'role' in desc:
            element = page.query_selector(f'[role="{desc["role"]}"]')
            if element:
                return element
    
    return None

def take_page_snapshot(page, description=""):
    """Take a snapshot of current page state for debugging"""
    try:
        # Get page title
        title = page.title()
        
        # Get current URL
        url = page.url
        
        # Get visible text content (first 500 chars)
        body_text = page.query_selector('body')
        text_content = body_text.text_content()[:500] if body_text else "No body content"
        
        # Get all buttons
        buttons = page.query_selector_all('button')
        button_texts = [btn.text_content().strip() for btn in buttons if btn.text_content().strip()]
        
        # Get all links
        links = page.query_selector_all('a')
        link_texts = [link.text_content().strip() for link in links if link.text_content().strip()]
        
        print(f"\n📸 PAGE SNAPSHOT: {description}")
        print(f"   URL: {url}")
        print(f"   Title: {title}")
        print(f"   Buttons found: {button_texts[:10]}")  # First 10 buttons
        print(f"   Links found: {link_texts[:10]}")      # First 10 links
        print(f"   Text preview: {text_content[:200]}...")
        
        return {
            'url': url,
            'title': title,
            'buttons': button_texts,
            'links': link_texts,
            'text': text_content
        }
    except Exception as e:
        print(f"   ❌ Error taking snapshot: {e}")
        return None

def test_interview_practice_intelligent(page):
    """Test interview practice with intelligent navigation"""
    print("\n🎤 INTELLIGENT INTERVIEW PRACTICE TESTING")
    print("=" * 60)
    
    results = []
    
    # Test 1: Navigate to interview practice
    print("   🎯 Testing navigation to interview practice...")
    try:
        page.goto("http://localhost:3001/interview-practice")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        snapshot = take_page_snapshot(page, "Interview Practice Page")
        
        # Check if we're on the right page
        if 'interview' in snapshot['title'].lower() or 'interview' in snapshot['url']:
            print("   ✅ Successfully navigated to interview practice page")
            results.append(('Navigation to Interview Practice', True))
        else:
            print("   ❌ Failed to navigate to interview practice page")
            results.append(('Navigation to Interview Practice', False))
            
    except Exception as e:
        print(f"   ❌ Navigation error: {e}")
        results.append(('Navigation to Interview Practice', False))
    
    # Test 2: Look for main interface elements
    print("   🎯 Testing main interface elements...")
    try:
        # Based on the actual page snapshot, look for specific elements we saw
        start_button_options = [
            {'button_text': 'Start New Practice'},  # This was in the snapshot
            {'button_text': 'Start Your First Practice'},  # This was also in the snapshot
            {'button_text': 'View Progress'},
            {'button_text': 'Show Testing Tool'},
            {'selector': 'button[data-testid="start-session"]'}
        ]

        start_button = smart_element_finder(page, start_button_options)
        
        if start_button:
            print("   ✅ Found start session button")
            results.append(('Start Session Button', True))
        else:
            print("   ❌ Start session button not found")
            results.append(('Start Session Button', False))
            
        # Look for dashboard elements
        dashboard_elements = page.query_selector_all('div[class*="card"], .card, [data-testid*="card"]')
        if len(dashboard_elements) > 0:
            print(f"   ✅ Found {len(dashboard_elements)} dashboard elements")
            results.append(('Dashboard Elements', True))
        else:
            print("   ❌ No dashboard elements found")
            results.append(('Dashboard Elements', False))
            
    except Exception as e:
        print(f"   ❌ Interface elements error: {e}")
        results.append(('Start Session Button', False))
        results.append(('Dashboard Elements', False))
    
    # Test 3: Try to start a session
    print("   🎯 Testing session creation...")
    try:
        # Look for any button that might start a session (using actual buttons we found)
        session_buttons = [
            {'button_text': 'Start New Practice'},
            {'button_text': 'Start Your First Practice'},
            {'button_text': 'Show Testing Tool'},  # This might open a testing interface
            {'selector': 'button[data-testid="quick-practice"]'},
            {'selector': 'button[data-testid="start-session"]'}
        ]
        
        session_button = smart_element_finder(page, session_buttons)
        
        if session_button:
            print("   ✅ Found session creation button, attempting to click...")
            session_button.click()
            page.wait_for_timeout(3000)
            
            # Check if something changed
            new_snapshot = take_page_snapshot(page, "After clicking session button")
            
            # Look for configuration or session interface
            config_indicators = [
                'configuration',
                'setup',
                'wizard',
                'session',
                'question',
                'practice'
            ]
            
            page_changed = False
            for indicator in config_indicators:
                if indicator in new_snapshot['text'].lower():
                    page_changed = True
                    break
            
            if page_changed:
                print("   ✅ Session creation initiated successfully")
                results.append(('Session Creation', True))
            else:
                print("   ⚠️ Session button clicked but no clear change detected")
                results.append(('Session Creation', False))
        else:
            print("   ❌ No session creation button found")
            results.append(('Session Creation', False))
            
    except Exception as e:
        print(f"   ❌ Session creation error: {e}")
        results.append(('Session Creation', False))
    
    return results

def test_career_paths_intelligent(page):
    """Test career paths with intelligent navigation"""
    print("\n🛤️ INTELLIGENT CAREER PATHS TESTING")
    print("=" * 60)
    
    results = []
    
    # Test 1: Navigate to career paths
    print("   🎯 Testing navigation to career paths...")
    try:
        page.goto("http://localhost:3001/career-paths")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        snapshot = take_page_snapshot(page, "Career Paths Page")
        
        # Check if we're on the right page
        if 'career' in snapshot['title'].lower() or 'career-paths' in snapshot['url']:
            print("   ✅ Successfully navigated to career paths page")
            results.append(('Navigation to Career Paths', True))
        else:
            print("   ❌ Failed to navigate to career paths page")
            results.append(('Navigation to Career Paths', False))
            
    except Exception as e:
        print(f"   ❌ Navigation error: {e}")
        results.append(('Navigation to Career Paths', False))
    
    # Test 2: Look for career path cards
    print("   🎯 Testing career path cards...")
    try:
        # Look for career path cards using various selectors
        card_selectors = [
            'div[class*="card"]',
            '.card',
            '[data-testid*="career"]',
            '[data-testid*="path"]',
            'article',
            'div[class*="grid"] > div'
        ]
        
        total_cards = 0
        for selector in card_selectors:
            cards = page.query_selector_all(selector)
            if len(cards) > total_cards:
                total_cards = len(cards)
        
        if total_cards > 0:
            print(f"   ✅ Found {total_cards} potential career path cards")
            results.append(('Career Path Cards', True))
        else:
            print("   ❌ No career path cards found")
            results.append(('Career Path Cards', False))
            
    except Exception as e:
        print(f"   ❌ Career path cards error: {e}")
        results.append(('Career Path Cards', False))
    
    return results

def test_resume_builder_intelligent(page):
    """Test resume builder with intelligent navigation"""
    print("\n📄 INTELLIGENT RESUME BUILDER TESTING")
    print("=" * 60)
    
    results = []
    
    # Test 1: Navigate to resume builder
    print("   🎯 Testing navigation to resume builder...")
    try:
        page.goto("http://localhost:3001/resume-builder")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        snapshot = take_page_snapshot(page, "Resume Builder Page")
        
        # Check if we're on the right page
        if 'resume' in snapshot['title'].lower() or 'resume-builder' in snapshot['url']:
            print("   ✅ Successfully navigated to resume builder page")
            results.append(('Navigation to Resume Builder', True))
        else:
            print("   ❌ Failed to navigate to resume builder page")
            results.append(('Navigation to Resume Builder', False))
            
    except Exception as e:
        print(f"   ❌ Navigation error: {e}")
        results.append(('Navigation to Resume Builder', False))
    
    # Test 2: Try to create a new resume
    print("   🎯 Testing resume creation...")
    try:
        # Look for "Create New Resume" button that we saw in the snapshot
        create_button = page.query_selector('button:has-text("Create New Resume")')

        if create_button:
            print("   ✅ Found 'Create New Resume' button, clicking...")
            create_button.click()
            page.wait_for_timeout(3000)

            # Now look for form elements
            inputs = page.query_selector_all('input, textarea, select')

            if len(inputs) > 0:
                print(f"   ✅ After clicking, found {len(inputs)} form inputs")
                results.append(('Resume Creation Form', True))
            else:
                print("   ❌ No form elements found after clicking create")
                results.append(('Resume Creation Form', False))
        else:
            print("   ❌ Create New Resume button not found")
            results.append(('Resume Creation Form', False))

    except Exception as e:
        print(f"   ❌ Resume creation error: {e}")
        results.append(('Resume Creation Form', False))
    
    return results

def main():
    print("🧪 INTELLIGENT APP NAVIGATION TESTING")
    print("=" * 80)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        all_results = []
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run intelligent tests
            interview_results = test_interview_practice_intelligent(page)
            career_results = test_career_paths_intelligent(page)
            resume_results = test_resume_builder_intelligent(page)
            
            all_results.extend(interview_results)
            all_results.extend(career_results)
            all_results.extend(resume_results)
            
            # Generate report
            print("\n" + "=" * 80)
            print("📊 INTELLIGENT NAVIGATION TEST RESULTS")
            print("=" * 80)
            
            passed = sum(1 for _, success in all_results if success)
            total = len(all_results)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 OVERALL SUMMARY:")
            print(f"   Total Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {total - passed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for test_name, success in all_results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {status}: {test_name}")
            
            print(f"\n🎯 FINAL ASSESSMENT:")
            if success_rate >= 90:
                print("🎉 EXCELLENT: App navigation working perfectly!")
            elif success_rate >= 80:
                print("✅ GOOD: Most features accessible, minor issues to address")
            elif success_rate >= 70:
                print("⚠️ FAIR: Core features accessible, some improvements needed")
            else:
                print("❌ POOR: Significant navigation issues found")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 20 seconds for inspection...")
            time.sleep(20)
            browser.close()

if __name__ == "__main__":
    main()
