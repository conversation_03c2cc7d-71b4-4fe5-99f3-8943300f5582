#!/usr/bin/env python3
"""
Comprehensive Live End-to-End Testing for FAAFO Application
Executes all required testing flows with detailed validation and reporting.
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from enum import Enum

from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser, BrowserContext


class TestStatus(Enum):
    """Test status enumeration"""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    SKIPPED = "skipped"


class TestResult:
    """Test result data structure"""
    def __init__(self, test_name: str, status: TestStatus, details: str = "", error_message: str = "", timestamp: datetime = None):
        self.test_name = test_name
        self.status = status
        self.details = details
        self.error_message = error_message
        self.timestamp = timestamp or datetime.now()


class ComprehensiveLiveTestRunner:
    """Comprehensive test runner for FAAFO application"""

    def __init__(self, base_url: str = "http://localhost:3001"):
        self.base_url = base_url
        self.headless = False  # Show browser for live testing
        self.timeout = 30000
        self.viewport_width = 1920
        self.viewport_height = 1080
        self.test_results: List[TestResult] = []
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None

        # Test credentials
        self.test_email = "<EMAIL>"
        self.test_password = "testpassword"
        
    async def setup_browser(self):
        """Initialize browser and context"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=self.headless,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.context = await self.browser.new_context(
            viewport={'width': self.viewport_width, 'height': self.viewport_height}
        )
        self.page = await self.context.new_page()

        # Set longer timeouts for comprehensive testing
        self.page.set_default_timeout(self.timeout)
        
    async def teardown_browser(self):
        """Clean up browser resources"""
        if self.browser:
            await self.browser.close()
            
    def log_test_result(self, test_name: str, status: TestStatus, details: str = "", error: str = ""):
        """Log test result"""
        result = TestResult(
            test_name=test_name,
            status=status,
            details=details,
            error_message=error,
            timestamp=datetime.now()
        )
        self.test_results.append(result)
        
        status_symbol = "✅" if status == TestStatus.PASSED else "❌" if status == TestStatus.FAILED else "⚠️"
        print(f"{status_symbol} {test_name}: {details}")
        if error:
            print(f"   Error: {error}")
            
    async def wait_for_page_load(self, timeout: int = 10000):
        """Wait for page to fully load"""
        try:
            await self.page.wait_for_load_state('networkidle', timeout=timeout)
            await asyncio.sleep(1)  # Additional wait for dynamic content
        except Exception as e:
            print(f"Warning: Page load timeout - {str(e)}")
            
    async def test_environment_setup(self) -> bool:
        """Test 1: Setup and Environment Verification"""
        print("\n🔧 Phase 1: Setup and Environment Verification")
        
        try:
            # Test application accessibility
            await self.page.goto(self.base_url)
            await self.wait_for_page_load()
            
            title = await self.page.title()
            self.log_test_result(
                "Application Accessibility", 
                TestStatus.PASSED,
                f"Application accessible at {self.base_url}, title: {title}"
            )
            
            # Check for React/Next.js indicators
            next_indicator = await self.page.query_selector('#__next')
            if next_indicator:
                self.log_test_result(
                    "Framework Detection",
                    TestStatus.PASSED,
                    "Next.js framework detected"
                )
            else:
                self.log_test_result(
                    "Framework Detection",
                    TestStatus.WARNING,
                    "Next.js indicators not found"
                )
                
            return True
            
        except Exception as e:
            self.log_test_result(
                "Environment Setup",
                TestStatus.FAILED,
                "Failed to access application",
                str(e)
            )
            return False
            
    async def test_authentication_flow(self) -> bool:
        """Test 2: Authentication Flow Testing"""
        print("\n🔐 Phase 2: Authentication Flow Testing")
        
        try:
            # Navigate to login page
            await self.page.goto(f"{self.base_url}/login")
            await self.wait_for_page_load()
            
            # Check if login form exists
            email_input = await self.page.query_selector('input[type="email"], input[name="email"]')
            password_input = await self.page.query_selector('input[type="password"], input[name="password"]')
            
            if not email_input or not password_input:
                self.log_test_result(
                    "Login Form Detection",
                    TestStatus.FAILED,
                    "Login form elements not found"
                )
                return False
                
            self.log_test_result(
                "Login Form Detection",
                TestStatus.PASSED,
                "Login form elements found"
            )
            
            # Fill login credentials
            await email_input.fill(self.test_email)
            await password_input.fill(self.test_password)
            
            # Submit login form
            login_button = await self.page.query_selector('button[type="submit"], .login-btn, button:has-text("Sign in")')
            if login_button:
                await login_button.click()
                await self.wait_for_page_load()
                
                # Check for successful login (redirect to dashboard or user indicator)
                current_url = self.page.url
                if '/login' not in current_url:
                    self.log_test_result(
                        "Login Authentication",
                        TestStatus.PASSED,
                        f"Successfully logged in, redirected to: {current_url}"
                    )
                    return True
                else:
                    self.log_test_result(
                        "Login Authentication",
                        TestStatus.FAILED,
                        "Login failed - still on login page"
                    )
                    return False
            else:
                self.log_test_result(
                    "Login Button",
                    TestStatus.FAILED,
                    "Login button not found"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Authentication Flow",
                TestStatus.FAILED,
                "Authentication test failed",
                str(e)
            )
            return False
            
    async def test_resume_builder_flow(self) -> bool:
        """Test 3: Resume Builder Flow Testing"""
        print("\n📄 Phase 3: Resume Builder Flow Testing")
        
        try:
            # Navigate to resume builder
            await self.page.goto(f"{self.base_url}/resume-builder")
            await self.wait_for_page_load()
            
            # Check if resume builder page loads
            page_content = await self.page.content()
            if 'resume' in page_content.lower() or 'builder' in page_content.lower():
                self.log_test_result(
                    "Resume Builder Access",
                    TestStatus.PASSED,
                    "Resume builder page accessible"
                )
            else:
                self.log_test_result(
                    "Resume Builder Access",
                    TestStatus.FAILED,
                    "Resume builder page not found"
                )
                return False
                
            # Test template selection (if available)
            templates = await self.page.query_selector_all('.template, [data-testid*="template"], .resume-template')
            if templates:
                self.log_test_result(
                    "Template Selection",
                    TestStatus.PASSED,
                    f"Found {len(templates)} resume templates"
                )
                
                # Click first template
                await templates[0].click()
                await self.wait_for_page_load()
            else:
                self.log_test_result(
                    "Template Selection",
                    TestStatus.WARNING,
                    "No templates found - may be different UI structure"
                )
                
            # Test form sections
            form_sections = await self.page.query_selector_all('form, .form-section, [data-testid*="section"]')
            if form_sections:
                self.log_test_result(
                    "Form Sections",
                    TestStatus.PASSED,
                    f"Found {len(form_sections)} form sections"
                )
            else:
                self.log_test_result(
                    "Form Sections",
                    TestStatus.WARNING,
                    "No form sections detected"
                )
                
            return True
            
        except Exception as e:
            self.log_test_result(
                "Resume Builder Flow",
                TestStatus.FAILED,
                "Resume builder test failed",
                str(e)
            )
            return False
            
    async def run_comprehensive_tests(self):
        """Execute all comprehensive tests"""
        print("🚀 Starting Comprehensive Live End-to-End Testing")
        print(f"Target Application: {self.base_url}")
        print(f"Test Credentials: {self.test_email}")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            await self.setup_browser()
            
            # Execute test phases
            phase_results = []
            
            # Phase 1: Environment Setup
            phase_results.append(await self.test_environment_setup())
            
            # Phase 2: Authentication (only if environment setup passed)
            if phase_results[-1]:
                phase_results.append(await self.test_authentication_flow())
            else:
                self.log_test_result("Authentication Flow", TestStatus.SKIPPED, "Skipped due to environment setup failure")
                phase_results.append(False)
                
            # Phase 3: Resume Builder (only if authentication passed)
            if phase_results[-1]:
                phase_results.append(await self.test_resume_builder_flow())
            else:
                self.log_test_result("Resume Builder Flow", TestStatus.SKIPPED, "Skipped due to authentication failure")
                phase_results.append(False)
                
        except Exception as e:
            self.log_test_result(
                "Test Execution",
                TestStatus.FAILED,
                "Critical test execution error",
                str(e)
            )
            
        finally:
            await self.teardown_browser()
            
        # Generate comprehensive report
        end_time = time.time()
        duration = end_time - start_time
        
        await self.generate_final_report(duration)
        
    async def generate_final_report(self, duration: float):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.status == TestStatus.PASSED])
        failed_tests = len([r for r in self.test_results if r.status == TestStatus.FAILED])
        warning_tests = len([r for r in self.test_results if r.status == TestStatus.WARNING])
        skipped_tests = len([r for r in self.test_results if r.status == TestStatus.SKIPPED])
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️  Warnings: {warning_tests}")
        print(f"⏭️  Skipped: {skipped_tests}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        # Detailed results
        print("\n📋 DETAILED TEST RESULTS:")
        for result in self.test_results:
            status_symbol = {
                TestStatus.PASSED: "✅",
                TestStatus.FAILED: "❌", 
                TestStatus.WARNING: "⚠️",
                TestStatus.SKIPPED: "⏭️"
            }.get(result.status, "❓")
            
            print(f"{status_symbol} {result.test_name}: {result.details}")
            if result.error_message:
                print(f"   Error: {result.error_message}")
                
        # Save detailed report
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "duration": duration,
            "summary": {
                "total": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "warnings": warning_tests,
                "skipped": skipped_tests,
                "success_rate": success_rate
            },
            "test_results": [
                {
                    "test_name": r.test_name,
                    "status": r.status.value,
                    "details": r.details,
                    "error_message": r.error_message,
                    "timestamp": r.timestamp.isoformat()
                }
                for r in self.test_results
            ]
        }
        
        # Save to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(f"testerat_reports/comprehensive_test_report_{timestamp}.json")
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
            
        print(f"\n💾 Detailed report saved to: {report_file}")
        
        # Final assessment
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! Application is functioning correctly.")
        elif failed_tests <= 2:
            print(f"\n⚠️  {failed_tests} test(s) failed. Review and fix issues before production.")
        else:
            print(f"\n🚨 {failed_tests} tests failed. Significant issues detected - requires immediate attention.")


async def main():
    """Main execution function"""
    runner = ComprehensiveLiveTestRunner()
    await runner.run_comprehensive_tests()


if __name__ == "__main__":
    asyncio.run(main())
