#!/usr/bin/env python3
"""
Comprehensive Resume Builder Testing
Tests the complete resume builder workflow including form filling,
template selection, preview, and export functionality
"""

from playwright.sync_api import sync_playwright
import time

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        return True
    return False

def test_resume_builder_access(page):
    """Test accessing resume builder"""
    print("\n📄 TESTING RESUME BUILDER ACCESS")
    print("=" * 50)
    
    results = []
    
    # Test direct URL access
    print("   🎯 Testing direct URL access...")
    try:
        page.goto("http://localhost:3001/tools/resume-builder")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Check if page loads
        page_content = page.query_selector('h1, h2, [data-testid="resume-builder"]')
        if page_content and ('resume' in page_content.text_content().lower() or 'builder' in page_content.text_content().lower()):
            print("   ✅ Resume builder page loads")
            results.append(('Page Access', True))
        else:
            print("   ❌ Resume builder page not loading properly")
            results.append(('Page Access', False))
    except Exception as e:
        print(f"   ❌ Page access error: {e}")
        results.append(('Page Access', False))
    
    return results

def test_resume_form_sections(page):
    """Test all resume form sections"""
    print("\n📝 TESTING RESUME FORM SECTIONS")
    print("=" * 50)
    
    results = []
    
    page.goto("http://localhost:3001/tools/resume-builder")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Test 1: Personal Information Section
    print("   🎯 Testing Personal Information section...")
    try:
        # Look for personal info fields
        name_input = page.query_selector('input[name*="name"], input[placeholder*="name"], [data-testid="name-input"]')
        email_input = page.query_selector('input[name*="email"], input[placeholder*="email"], [data-testid="email-input"]')
        phone_input = page.query_selector('input[name*="phone"], input[placeholder*="phone"], [data-testid="phone-input"]')
        
        if name_input and email_input:
            # Fill out personal information
            name_input.fill("John Doe")
            email_input.fill("<EMAIL>")
            if phone_input:
                phone_input.fill("(*************")
            
            print("   ✅ Personal Information section working")
            results.append(('Personal Info Section', True))
        else:
            print("   ❌ Personal Information fields not found")
            results.append(('Personal Info Section', False))
    except Exception as e:
        print(f"   ❌ Personal Info section error: {e}")
        results.append(('Personal Info Section', False))
    
    # Test 2: Experience Section
    print("   🎯 Testing Experience section...")
    try:
        # Look for experience fields or add experience button
        add_experience_btn = page.query_selector('button:has-text("Add Experience"), [data-testid="add-experience"]')
        experience_section = page.query_selector('[data-testid="experience-section"], .experience-section')
        
        if add_experience_btn or experience_section:
            if add_experience_btn:
                add_experience_btn.click()
                page.wait_for_timeout(1000)
            
            # Look for experience form fields
            job_title = page.query_selector('input[name*="title"], input[placeholder*="title"], [data-testid="job-title"]')
            company = page.query_selector('input[name*="company"], input[placeholder*="company"], [data-testid="company"]')
            
            if job_title and company:
                job_title.fill("Software Developer")
                company.fill("Tech Company Inc.")
                print("   ✅ Experience section working")
                results.append(('Experience Section', True))
            else:
                print("   ⚠️ Experience section found but fields incomplete")
                results.append(('Experience Section', False))
        else:
            print("   ❌ Experience section not found")
            results.append(('Experience Section', False))
    except Exception as e:
        print(f"   ❌ Experience section error: {e}")
        results.append(('Experience Section', False))
    
    # Test 3: Education Section
    print("   🎯 Testing Education section...")
    try:
        add_education_btn = page.query_selector('button:has-text("Add Education"), [data-testid="add-education"]')
        education_section = page.query_selector('[data-testid="education-section"], .education-section')
        
        if add_education_btn or education_section:
            if add_education_btn:
                add_education_btn.click()
                page.wait_for_timeout(1000)
            
            # Look for education fields
            degree_input = page.query_selector('input[name*="degree"], input[placeholder*="degree"], [data-testid="degree"]')
            school_input = page.query_selector('input[name*="school"], input[placeholder*="school"], [data-testid="school"]')
            
            if degree_input and school_input:
                degree_input.fill("Bachelor of Science in Computer Science")
                school_input.fill("University of Technology")
                print("   ✅ Education section working")
                results.append(('Education Section', True))
            else:
                print("   ⚠️ Education section found but fields incomplete")
                results.append(('Education Section', False))
        else:
            print("   ❌ Education section not found")
            results.append(('Education Section', False))
    except Exception as e:
        print(f"   ❌ Education section error: {e}")
        results.append(('Education Section', False))
    
    # Test 4: Skills Section
    print("   🎯 Testing Skills section...")
    try:
        skills_input = page.query_selector('input[name*="skill"], input[placeholder*="skill"], [data-testid="skills-input"]')
        add_skill_btn = page.query_selector('button:has-text("Add Skill"), [data-testid="add-skill"]')
        
        if skills_input or add_skill_btn:
            if skills_input:
                skills_input.fill("JavaScript")
                page.keyboard.press('Enter')
                page.wait_for_timeout(500)
                
                skills_input.fill("Python")
                page.keyboard.press('Enter')
                page.wait_for_timeout(500)
            
            print("   ✅ Skills section working")
            results.append(('Skills Section', True))
        else:
            print("   ❌ Skills section not found")
            results.append(('Skills Section', False))
    except Exception as e:
        print(f"   ❌ Skills section error: {e}")
        results.append(('Skills Section', False))
    
    return results

def test_resume_templates(page):
    """Test resume template selection"""
    print("\n🎨 TESTING RESUME TEMPLATES")
    print("=" * 50)
    
    results = []
    
    # Test template selection
    print("   🎯 Testing template selection...")
    try:
        # Look for template options
        template_selector = page.query_selector('[data-testid="template-selector"], .template-selector')
        template_buttons = page.query_selector_all('button[data-template], .template-option, button:has-text("Template")')
        
        if template_selector or len(template_buttons) > 0:
            if len(template_buttons) > 0:
                # Try selecting different templates
                for i, template_btn in enumerate(template_buttons[:3]):  # Test first 3 templates
                    template_btn.click()
                    page.wait_for_timeout(1000)
                    print(f"   ✅ Template {i+1} selectable")
            
            print("   ✅ Template selection working")
            results.append(('Template Selection', True))
        else:
            print("   ❌ Template selection not found")
            results.append(('Template Selection', False))
    except Exception as e:
        print(f"   ❌ Template selection error: {e}")
        results.append(('Template Selection', False))
    
    return results

def test_resume_preview_and_export(page):
    """Test resume preview and export functionality"""
    print("\n👁️ TESTING PREVIEW AND EXPORT")
    print("=" * 50)
    
    results = []
    
    # Test 1: Preview functionality
    print("   🎯 Testing resume preview...")
    try:
        preview_btn = page.query_selector('button:has-text("Preview"), [data-testid="preview-button"]')
        preview_section = page.query_selector('[data-testid="resume-preview"], .resume-preview')
        
        if preview_btn:
            preview_btn.click()
            page.wait_for_timeout(2000)
            
            # Check if preview appears
            preview_content = page.query_selector('[data-testid="resume-preview"], .resume-preview, .preview-content')
            if preview_content:
                print("   ✅ Resume preview working")
                results.append(('Resume Preview', True))
            else:
                print("   ❌ Resume preview not showing")
                results.append(('Resume Preview', False))
        elif preview_section:
            print("   ✅ Resume preview visible")
            results.append(('Resume Preview', True))
        else:
            print("   ❌ Resume preview not found")
            results.append(('Resume Preview', False))
    except Exception as e:
        print(f"   ❌ Resume preview error: {e}")
        results.append(('Resume Preview', False))
    
    # Test 2: Export options
    print("   🎯 Testing export options...")
    try:
        export_btn = page.query_selector('button:has-text("Export"), button:has-text("Download"), [data-testid="export-button"]')
        pdf_btn = page.query_selector('button:has-text("PDF"), [data-testid="export-pdf"]')
        
        if export_btn or pdf_btn:
            print("   ✅ Export options available")
            results.append(('Export Options', True))
        else:
            print("   ❌ Export options not found")
            results.append(('Export Options', False))
    except Exception as e:
        print(f"   ❌ Export options error: {e}")
        results.append(('Export Options', False))
    
    # Test 3: Save functionality
    print("   🎯 Testing save functionality...")
    try:
        save_btn = page.query_selector('button:has-text("Save"), [data-testid="save-resume"]')
        
        if save_btn:
            save_btn.click()
            page.wait_for_timeout(2000)
            
            # Look for success message or confirmation
            success_msg = page.query_selector('text*="saved", text*="success", .success-message')
            if success_msg:
                print("   ✅ Save functionality working")
                results.append(('Save Functionality', True))
            else:
                print("   ⚠️ Save button clicked but no confirmation")
                results.append(('Save Functionality', False))
        else:
            print("   ❌ Save button not found")
            results.append(('Save Functionality', False))
    except Exception as e:
        print(f"   ❌ Save functionality error: {e}")
        results.append(('Save Functionality', False))
    
    return results

def main():
    print("🧪 COMPREHENSIVE RESUME BUILDER TESTING")
    print("=" * 70)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        all_results = []
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run all test suites
            access_results = test_resume_builder_access(page)
            form_results = test_resume_form_sections(page)
            template_results = test_resume_templates(page)
            export_results = test_resume_preview_and_export(page)
            
            all_results.extend(access_results)
            all_results.extend(form_results)
            all_results.extend(template_results)
            all_results.extend(export_results)
            
            # Generate report
            print("\n" + "=" * 70)
            print("📊 RESUME BUILDER TEST RESULTS")
            print("=" * 70)
            
            passed = sum(1 for _, success in all_results if success)
            total = len(all_results)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 OVERALL SUMMARY:")
            print(f"   Total Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {total - passed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for test_name, success in all_results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {status}: {test_name}")
            
            print(f"\n🎯 FINAL ASSESSMENT:")
            if success_rate >= 90:
                print("🎉 EXCELLENT: Resume Builder working perfectly!")
            elif success_rate >= 80:
                print("✅ GOOD: Most functionality working, minor issues to address")
            elif success_rate >= 70:
                print("⚠️ FAIR: Core functionality working, some improvements needed")
            else:
                print("❌ POOR: Significant issues need immediate attention")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 15 seconds for inspection...")
            time.sleep(15)
            browser.close()

if __name__ == "__main__":
    main()
