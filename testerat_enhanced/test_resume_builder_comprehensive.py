#!/usr/bin/env python3
"""
COMPREHENSIVE Resume Builder Testing Suite
Tests every aspect of the resume builder functionality
"""

from playwright.sync_api import sync_playwright
import time
import json

class ResumeBuilderComprehensiveTester:
    def __init__(self):
        self.test_results = {
            'authentication': {},
            'navigation': {},
            'interface': {},
            'forms': {},
            'data_persistence': {},
            'templates': {},
            'export': {},
            'validation': {},
            'accessibility': {},
            'performance': {},
            'security': {},
            'edge_cases': {}
        }
        
        self.test_data = {
            'personal': {
                'firstName': 'John',
                'lastName': 'Doe',
                'email': '<EMAIL>',
                'phone': '+****************',
                'location': 'San Francisco, CA',
                'website': 'https://johndoe.com',
                'linkedin': 'https://linkedin.com/in/johndoe',
                'summary': 'Experienced software engineer with 5+ years in full-stack development.'
            },
            'experience': [
                {
                    'company': 'Tech Corp',
                    'position': 'Senior Software Engineer',
                    'startDate': '2020-01',
                    'endDate': '2023-12',
                    'description': 'Led development of web applications using React and Node.js',
                    'current': False
                },
                {
                    'company': 'Innovation Labs',
                    'position': 'Lead Software Engineer',
                    'startDate': '2024-01',
                    'endDate': '',
                    'description': 'Leading development of next-generation web applications',
                    'current': True
                }
            ],
            'education': [
                {
                    'institution': 'University of California, Berkeley',
                    'degree': 'Bachelor of Science',
                    'field': 'Computer Science',
                    'startDate': '2016-08',
                    'endDate': '2020-05',
                    'gpa': '3.8'
                }
            ],
            'skills': [
                {'name': 'JavaScript', 'level': 'Expert'},
                {'name': 'Python', 'level': 'Advanced'},
                {'name': 'React', 'level': 'Expert'},
                {'name': 'Node.js', 'level': 'Advanced'}
            ]
        }

    def run_comprehensive_tests(self):
        """Run all comprehensive tests"""
        print("🎯 COMPREHENSIVE RESUME BUILDER TESTING SUITE")
        print("=" * 60)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            page = browser.new_page()
            
            try:
                # Phase 1: Authentication & Access
                self.test_authentication_comprehensive(page)
                
                # Phase 2: Navigation & Interface
                self.test_navigation_comprehensive(page)
                
                # Phase 3: Form Functionality
                self.test_forms_comprehensive(page)
                
                # Phase 4: Data Persistence
                self.test_data_persistence_comprehensive(page)
                
                # Phase 5: Templates & Preview
                self.test_templates_comprehensive(page)
                
                # Phase 6: Export Functionality
                self.test_export_comprehensive(page)

                # Phase 7: Validation & Error Handling
                self.test_validation_comprehensive(page)

                # Phase 8: Accessibility
                self.test_accessibility_comprehensive(page)

                # Phase 9: Performance
                self.test_performance_comprehensive(page)

                # Phase 10: Security
                self.test_security_comprehensive(page)

                # Phase 11: Edge Cases
                self.test_edge_cases_comprehensive(page)
                
                # Generate comprehensive report
                self.generate_comprehensive_report()
                
            except Exception as e:
                print(f"❌ CRITICAL ERROR: {e}")
            finally:
                print("\n🔍 Keeping browser open for final inspection...")
                time.sleep(10)
                browser.close()

    def test_authentication_comprehensive(self, page):
        """Comprehensive authentication testing"""
        print("\n🔐 PHASE 1: COMPREHENSIVE AUTHENTICATION TESTING")
        print("-" * 50)
        
        auth_tests = {
            'unauthenticated_access': self.test_unauthenticated_access,
            'login_process': self.test_login_process,
            'session_persistence': self.test_session_persistence,
            'logout_process': self.test_logout_process,
            'protected_routes': self.test_protected_routes
        }
        
        for test_name, test_func in auth_tests.items():
            try:
                result = test_func(page)
                self.test_results['authentication'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['authentication'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_unauthenticated_access(self, page):
        """Test access without authentication"""
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Check if redirected to login or if access is allowed
        current_url = page.url
        if "/login" in current_url:
            return {'passed': True, 'details': 'Properly redirected to login when unauthenticated'}
        elif "/resume-builder" in current_url:
            return {'passed': True, 'details': 'Resume builder accessible without authentication'}
        else:
            return {'passed': False, 'details': f'Unexpected redirect to: {current_url}'}

    def test_login_process(self, page):
        """Test comprehensive login process"""
        # Navigate to login
        page.goto("http://localhost:3002/login")
        page.wait_for_load_state('networkidle')
        
        # Check form elements
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        if not all([email_input, password_input, submit_btn]):
            return {'passed': False, 'details': 'Login form elements missing'}
        
        # Test login with valid credentials
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Check authentication indicators
        auth_indicators = [
            '[aria-label="View your profile"]',
            '[aria-label="Sign out of your account"]',
            'button:has-text("Sign out")'
        ]
        
        is_authenticated = any(page.query_selector(selector) for selector in auth_indicators)
        
        if is_authenticated:
            return {'passed': True, 'details': 'Login successful with valid credentials'}
        else:
            return {'passed': False, 'details': 'Login failed with valid credentials'}

    def test_session_persistence(self, page):
        """Test session persistence across page reloads"""
        # Ensure we're authenticated first
        page.goto("http://localhost:3002")
        page.wait_for_load_state('networkidle')
        
        # Check if authenticated
        auth_indicator = page.query_selector('[aria-label="Sign out of your account"]')
        if not auth_indicator:
            return {'passed': False, 'details': 'Not authenticated for session test'}
        
        # Reload page
        page.reload()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Check if still authenticated
        auth_indicator_after = page.query_selector('[aria-label="Sign out of your account"]')
        
        if auth_indicator_after:
            return {'passed': True, 'details': 'Session persisted across page reload'}
        else:
            return {'passed': False, 'details': 'Session lost after page reload'}

    def test_logout_process(self, page):
        """Test logout functionality"""
        page.goto("http://localhost:3002")
        page.wait_for_load_state('networkidle')
        
        # Find logout button
        logout_btn = page.query_selector('[aria-label="Sign out of your account"]')
        if not logout_btn:
            return {'passed': False, 'details': 'Logout button not found'}
        
        # Click logout
        logout_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Check if logged out
        login_indicator = page.query_selector('[aria-label="Log in to your account"]')
        
        if login_indicator:
            return {'passed': True, 'details': 'Logout successful'}
        else:
            return {'passed': False, 'details': 'Logout failed - still authenticated'}

    def test_protected_routes(self, page):
        """Test access to protected routes"""
        protected_routes = [
            '/resume-builder',
            '/dashboard',
            '/profile'
        ]
        
        accessible_routes = 0
        for route in protected_routes:
            try:
                page.goto(f"http://localhost:3002{route}")
                page.wait_for_load_state('networkidle')
                time.sleep(1)
                
                if route in page.url:
                    accessible_routes += 1
            except:
                continue
        
        return {
            'passed': accessible_routes > 0,
            'details': f'{accessible_routes}/{len(protected_routes)} protected routes accessible'
        }

    def test_navigation_comprehensive(self, page):
        """Comprehensive navigation testing"""
        print("\n🧭 PHASE 2: COMPREHENSIVE NAVIGATION TESTING")
        print("-" * 50)
        
        nav_tests = {
            'direct_url_access': self.test_direct_url_access,
            'navigation_links': self.test_navigation_links,
            'breadcrumb_navigation': self.test_breadcrumb_navigation,
            'back_button_functionality': self.test_back_button_functionality,
            'deep_linking': self.test_deep_linking
        }
        
        for test_name, test_func in nav_tests.items():
            try:
                result = test_func(page)
                self.test_results['navigation'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['navigation'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_direct_url_access(self, page):
        """Test direct URL access to resume builder"""
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        if "/resume-builder" in page.url:
            return {'passed': True, 'details': 'Direct URL access successful'}
        else:
            return {'passed': False, 'details': f'Redirected to: {page.url}'}

    def test_navigation_links(self, page):
        """Test navigation links to resume builder"""
        page.goto("http://localhost:3002")
        page.wait_for_load_state('networkidle')
        
        # Look for resume builder links
        link_selectors = [
            'a[href*="resume-builder"]',
            'a[href*="resume"]',
            'a:has-text("Resume Builder")',
            'a:has-text("Resume")'
        ]
        
        link_found = False
        for selector in link_selectors:
            link = page.query_selector(selector)
            if link:
                link.click()
                page.wait_for_load_state('networkidle')
                time.sleep(2)
                if "/resume" in page.url:
                    link_found = True
                    break
        
        if link_found:
            return {'passed': True, 'details': 'Navigation link found and working'}
        else:
            return {'passed': False, 'details': 'No working navigation links found'}

    def test_breadcrumb_navigation(self, page):
        """Test breadcrumb navigation"""
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')
        
        # Look for breadcrumb elements
        breadcrumb_selectors = [
            '.breadcrumb',
            '[aria-label="breadcrumb"]',
            'nav ol',
            '.breadcrumbs'
        ]
        
        breadcrumb_found = any(page.query_selector(selector) for selector in breadcrumb_selectors)
        
        return {
            'passed': breadcrumb_found,
            'details': 'Breadcrumb navigation found' if breadcrumb_found else 'No breadcrumb navigation'
        }

    def test_back_button_functionality(self, page):
        """Test browser back button functionality"""
        # Navigate to home, then resume builder
        page.goto("http://localhost:3002")
        page.wait_for_load_state('networkidle')
        
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')
        
        # Use browser back button
        page.go_back()
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        if page.url == "http://localhost:3002/":
            return {'passed': True, 'details': 'Browser back button works correctly'}
        else:
            return {'passed': False, 'details': f'Back button led to: {page.url}'}

    def test_deep_linking(self, page):
        """Test deep linking to specific resume builder sections"""
        deep_links = [
            '/resume-builder',
            '/resume-builder/new',
            '/resume-builder/edit'
        ]
        
        accessible_links = 0
        for link in deep_links:
            try:
                page.goto(f"http://localhost:3002{link}")
                page.wait_for_load_state('networkidle')
                time.sleep(1)
                
                if "/resume-builder" in page.url:
                    accessible_links += 1
            except:
                continue
        
        return {
            'passed': accessible_links > 0,
            'details': f'{accessible_links}/{len(deep_links)} deep links accessible'
        }

    def test_forms_comprehensive(self, page):
        """Comprehensive form testing"""
        print("\n📝 PHASE 3: COMPREHENSIVE FORM TESTING")
        print("-" * 50)

        # Navigate to resume builder
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')

        # Click create button if available
        create_btn = page.query_selector('button:has-text("Create"), button:has-text("New")')
        if create_btn:
            create_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(2)

        form_tests = {
            'personal_info_form': self.test_personal_info_form,
            'experience_form': self.test_experience_form,
            'education_form': self.test_education_form,
            'skills_form': self.test_skills_form,
            'form_navigation': self.test_form_navigation,
            'auto_save': self.test_auto_save,
            'form_validation': self.test_form_validation_comprehensive
        }

        for test_name, test_func in form_tests.items():
            try:
                result = test_func(page)
                self.test_results['forms'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['forms'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_personal_info_form(self, page):
        """Test personal information form comprehensively"""
        # Navigate to personal info section
        personal_tab = page.query_selector('button:has-text("Personal"), [role="tab"]:has-text("Personal")')
        if personal_tab:
            personal_tab.click()
            page.wait_for_timeout(1000)

        filled_fields = 0
        total_fields = len(self.test_data['personal'])

        for field_name, field_value in self.test_data['personal'].items():
            selectors = [
                f'input[name="{field_name}"]',
                f'input[id="{field_name}"]',
                f'input[placeholder*="{field_name}"]',
                f'textarea[name="{field_name}"]'
            ]

            for selector in selectors:
                try:
                    field = page.query_selector(selector)
                    if field:
                        field.fill(field_value)
                        filled_fields += 1
                        break
                except:
                    continue

        success_rate = (filled_fields / total_fields) * 100
        return {
            'passed': filled_fields >= total_fields * 0.7,  # 70% success rate
            'details': f'Filled {filled_fields}/{total_fields} fields ({success_rate:.1f}%)'
        }

    def test_experience_form(self, page):
        """Test experience form comprehensively"""
        # Navigate to experience section
        exp_tab = page.query_selector('button:has-text("Experience"), [role="tab"]:has-text("Experience")')
        if exp_tab:
            exp_tab.click()
            page.wait_for_timeout(1000)

        experiences_added = 0

        for exp_data in self.test_data['experience']:
            # Look for add button
            add_btn = page.query_selector('button:has-text("Add Experience"), button:has-text("Add")')
            if add_btn:
                add_btn.click()
                page.wait_for_timeout(1000)

                # Fill experience fields
                fields_filled = 0
                for field_name, field_value in exp_data.items():
                    if field_name == 'current':
                        continue

                    selectors = [
                        f'input[name*="{field_name}"]',
                        f'input[placeholder*="{field_name}"]',
                        f'textarea[name*="{field_name}"]'
                    ]

                    for selector in selectors:
                        try:
                            field = page.query_selector(selector)
                            if field and field_value:
                                field.fill(str(field_value))
                                fields_filled += 1
                                break
                        except:
                            continue

                if fields_filled >= 3:  # At least 3 fields filled
                    experiences_added += 1

        return {
            'passed': experiences_added > 0,
            'details': f'Added {experiences_added}/{len(self.test_data["experience"])} experiences'
        }

    def test_education_form(self, page):
        """Test education form comprehensively"""
        # Navigate to education section
        edu_tab = page.query_selector('button:has-text("Education"), [role="tab"]:has-text("Education")')
        if edu_tab:
            edu_tab.click()
            page.wait_for_timeout(1000)

        education_added = 0

        for edu_data in self.test_data['education']:
            # Look for add button
            add_btn = page.query_selector('button:has-text("Add Education"), button:has-text("Add")')
            if add_btn:
                add_btn.click()
                page.wait_for_timeout(1000)

                # Fill education fields
                fields_filled = 0
                for field_name, field_value in edu_data.items():
                    selectors = [
                        f'input[name*="{field_name}"]',
                        f'input[placeholder*="{field_name}"]'
                    ]

                    for selector in selectors:
                        try:
                            field = page.query_selector(selector)
                            if field:
                                field.fill(str(field_value))
                                fields_filled += 1
                                break
                        except:
                            continue

                if fields_filled >= 3:  # At least 3 fields filled
                    education_added += 1

        return {
            'passed': education_added > 0,
            'details': f'Added {education_added}/{len(self.test_data["education"])} education entries'
        }

    def test_skills_form(self, page):
        """Test skills form comprehensively"""
        # Navigate to skills section
        skills_tab = page.query_selector('button:has-text("Skills"), [role="tab"]:has-text("Skills")')
        if skills_tab:
            skills_tab.click()
            page.wait_for_timeout(1000)

        skills_added = 0

        for skill_data in self.test_data['skills']:
            try:
                # Look for skill input
                skill_input = page.query_selector('input[name*="skill"], input[placeholder*="skill"]')
                if skill_input:
                    skill_input.fill(skill_data['name'])
                    page.wait_for_timeout(200)

                    # Look for level selector
                    level_selector = page.query_selector('select[name*="level"], select[name*="proficiency"]')
                    if level_selector:
                        level_selector.select_option(skill_data['level'])
                        page.wait_for_timeout(200)

                    # Look for add button
                    add_skill_btn = page.query_selector('button:has-text("Add Skill"), button:has-text("Add")')
                    if add_skill_btn:
                        add_skill_btn.click()
                        page.wait_for_timeout(500)
                        skills_added += 1
            except:
                continue

        return {
            'passed': skills_added > 0,
            'details': f'Added {skills_added}/{len(self.test_data["skills"])} skills'
        }

    def test_form_navigation(self, page):
        """Test navigation between form sections"""
        sections = ['Personal', 'Experience', 'Education', 'Skills']
        navigated_sections = 0

        for section in sections:
            try:
                tab = page.query_selector(f'button:has-text("{section}"), [role="tab"]:has-text("{section}")')
                if tab:
                    tab.click()
                    page.wait_for_timeout(500)
                    navigated_sections += 1
            except:
                continue

        return {
            'passed': navigated_sections >= 2,
            'details': f'Successfully navigated to {navigated_sections}/{len(sections)} sections'
        }

    def test_auto_save(self, page):
        """Test auto-save functionality"""
        # Fill a field and wait to see if auto-save triggers
        name_field = page.query_selector('input[name="firstName"], input[id="firstName"]')
        if name_field:
            name_field.fill("AutoSave Test")
            page.wait_for_timeout(3000)  # Wait for potential auto-save

            # Look for auto-save indicators
            save_indicators = [
                '.auto-saved',
                '.saving',
                'text="saved"',
                'text="saving"'
            ]

            auto_save_detected = any(page.query_selector(selector) for selector in save_indicators)

            return {
                'passed': auto_save_detected,
                'details': 'Auto-save detected' if auto_save_detected else 'No auto-save indicators found'
            }

        return {'passed': False, 'details': 'Could not test auto-save - no suitable field found'}

    def test_form_validation_comprehensive(self, page):
        """Test comprehensive form validation"""
        validation_tests = 0
        validation_passed = 0

        # Test email validation
        email_field = page.query_selector('input[type="email"]')
        if email_field:
            email_field.fill("invalid-email")
            page.wait_for_timeout(500)

            # Look for validation error
            error_indicators = ['.error', '.invalid', '[role="alert"]']
            if any(page.query_selector(selector) for selector in error_indicators):
                validation_passed += 1
            validation_tests += 1

        # Test required field validation
        required_fields = page.query_selector_all('input[required], input[aria-required="true"]')
        for field in required_fields[:3]:  # Test first 3 required fields
            try:
                field.fill("")
                page.wait_for_timeout(200)

                # Try to trigger validation by clicking save
                save_btn = page.query_selector('button:has-text("Save")')
                if save_btn:
                    save_btn.click()
                    page.wait_for_timeout(500)

                    # Check for validation error
                    if any(page.query_selector(selector) for selector in error_indicators):
                        validation_passed += 1
                    validation_tests += 1
                    break
            except:
                continue

        success_rate = (validation_passed / validation_tests * 100) if validation_tests > 0 else 0
        return {
            'passed': validation_passed > 0,
            'details': f'Validation working: {validation_passed}/{validation_tests} tests ({success_rate:.1f}%)'
        }

    def test_data_persistence_comprehensive(self, page):
        """Comprehensive data persistence testing"""
        print("\n💾 PHASE 4: COMPREHENSIVE DATA PERSISTENCE TESTING")
        print("-" * 50)

        persistence_tests = {
            'save_functionality': self.test_save_functionality,
            'data_reload': self.test_data_reload,
            'draft_saving': self.test_draft_saving,
            'multiple_resumes': self.test_multiple_resumes,
            'data_integrity': self.test_data_integrity
        }

        for test_name, test_func in persistence_tests.items():
            try:
                result = test_func(page)
                self.test_results['data_persistence'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['data_persistence'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_save_functionality(self, page):
        """Test save functionality"""
        save_btn = page.query_selector('button:has-text("Save")')
        if save_btn:
            save_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)

            # Check for success indicators
            success_indicators = [
                '.success', '.saved', '[role="alert"]:has-text("saved")', 'text="saved"'
            ]

            save_success = any(page.query_selector(selector) for selector in success_indicators)
            return {
                'passed': True,  # Save button worked
                'details': 'Save successful with confirmation' if save_success else 'Save completed but no confirmation'
            }

        return {'passed': False, 'details': 'Save button not found'}

    def test_data_reload(self, page):
        """Test data persistence after page reload"""
        # Fill a unique value
        test_value = f"Test_{int(time.time())}"
        name_field = page.query_selector('input[name="firstName"], input[id="firstName"]')

        if name_field:
            name_field.fill(test_value)

            # Save if possible
            save_btn = page.query_selector('button:has-text("Save")')
            if save_btn:
                save_btn.click()
                page.wait_for_timeout(2000)

            # Reload page
            page.reload()
            page.wait_for_load_state('networkidle')
            time.sleep(2)

            # Check if data persisted
            name_field_after = page.query_selector('input[name="firstName"], input[id="firstName"]')
            if name_field_after:
                current_value = name_field_after.input_value()
                if test_value in current_value:
                    return {'passed': True, 'details': 'Data persisted after page reload'}
                else:
                    return {'passed': False, 'details': 'Data lost after page reload'}

        return {'passed': False, 'details': 'Could not test data reload'}

    def test_draft_saving(self, page):
        """Test draft saving functionality"""
        # Look for draft indicators
        draft_indicators = [
            '.draft', 'text="draft"', '[data-status="draft"]', 'button:has-text("Save Draft")'
        ]

        draft_found = any(page.query_selector(selector) for selector in draft_indicators)

        return {
            'passed': draft_found,
            'details': 'Draft functionality found' if draft_found else 'No draft functionality detected'
        }

    def test_multiple_resumes(self, page):
        """Test multiple resume management"""
        # Navigate back to resume list
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')

        # Look for resume list
        list_indicators = [
            '.resume-list', 'table', '.resume-item', 'button:has-text("Create")'
        ]

        list_found = any(page.query_selector(selector) for selector in list_indicators)

        return {
            'passed': list_found,
            'details': 'Resume list found' if list_found else 'No resume list interface detected'
        }

    def test_data_integrity(self, page):
        """Test data integrity"""
        # Fill multiple fields with specific values
        test_data = {
            'firstName': 'DataIntegrityTest',
            'lastName': 'User',
            'email': '<EMAIL>'
        }

        filled_fields = 0
        for field_name, field_value in test_data.items():
            field = page.query_selector(f'input[name="{field_name}"], input[id="{field_name}"]')
            if field:
                field.fill(field_value)
                filled_fields += 1

        # Save
        save_btn = page.query_selector('button:has-text("Save")')
        if save_btn:
            save_btn.click()
            page.wait_for_timeout(2000)

        # Verify data integrity
        integrity_maintained = 0
        for field_name, expected_value in test_data.items():
            field = page.query_selector(f'input[name="{field_name}"], input[id="{field_name}"]')
            if field:
                current_value = field.input_value()
                if current_value == expected_value:
                    integrity_maintained += 1

        success_rate = (integrity_maintained / len(test_data)) * 100
        return {
            'passed': integrity_maintained >= len(test_data) * 0.8,  # 80% integrity
            'details': f'Data integrity: {integrity_maintained}/{len(test_data)} fields ({success_rate:.1f}%)'
        }

    def test_templates_comprehensive(self, page):
        """Comprehensive template testing"""
        print("\n🎨 PHASE 5: COMPREHENSIVE TEMPLATE TESTING")
        print("-" * 50)

        template_tests = {
            'template_selection': self.test_template_selection,
            'preview_functionality': self.test_preview_functionality,
            'template_switching': self.test_template_switching,
            'responsive_preview': self.test_responsive_preview
        }

        for test_name, test_func in template_tests.items():
            try:
                result = test_func(page)
                self.test_results['templates'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['templates'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_template_selection(self, page):
        """Test template selection"""
        template_selectors = [
            '.template', '.template-option', 'button:has-text("Template")', '[data-template]'
        ]

        templates_found = 0
        for selector in template_selectors:
            templates = page.query_selector_all(selector)
            templates_found += len(templates)

        return {
            'passed': templates_found > 0,
            'details': f'Found {templates_found} template options'
        }

    def test_preview_functionality(self, page):
        """Test preview functionality"""
        preview_btn = page.query_selector('button:has-text("Preview")')
        if preview_btn:
            preview_btn.click()
            page.wait_for_timeout(2000)

            # Check if preview opened
            preview_indicators = [
                '.preview', '[role="dialog"]', '.modal', '.resume-preview'
            ]

            preview_opened = any(page.query_selector(selector) for selector in preview_indicators)

            return {
                'passed': preview_opened,
                'details': 'Preview opened successfully' if preview_opened else 'Preview did not open'
            }

        return {'passed': False, 'details': 'Preview button not found'}

    def test_template_switching(self, page):
        """Test switching between templates"""
        templates = page.query_selector_all('.template, .template-option, [data-template]')

        if len(templates) >= 2:
            # Click first template
            templates[0].click()
            page.wait_for_timeout(1000)

            # Click second template
            templates[1].click()
            page.wait_for_timeout(1000)

            return {'passed': True, 'details': 'Template switching functionality available'}

        return {'passed': False, 'details': f'Only {len(templates)} templates found - need 2+ for switching test'}

    def test_responsive_preview(self, page):
        """Test responsive preview"""
        # Look for responsive preview controls
        responsive_controls = [
            'button:has-text("Mobile")', 'button:has-text("Desktop")',
            '.responsive-controls', '[data-viewport]'
        ]

        responsive_found = any(page.query_selector(selector) for selector in responsive_controls)

        return {
            'passed': responsive_found,
            'details': 'Responsive preview controls found' if responsive_found else 'No responsive preview controls'
        }

    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        print("\n📊 COMPREHENSIVE TEST REPORT")
        print("=" * 60)

        total_tests = 0
        passed_tests = 0

        for phase, tests in self.test_results.items():
            if tests:
                phase_total = len(tests)
                phase_passed = sum(1 for test in tests.values() if test.get('passed', False))
                total_tests += phase_total
                passed_tests += phase_passed

                success_rate = (phase_passed / phase_total * 100) if phase_total > 0 else 0
                print(f"\n{phase.upper()}: {phase_passed}/{phase_total} ({success_rate:.1f}%)")

                for test_name, result in tests.items():
                    status = "✅" if result.get('passed', False) else "❌"
                    print(f"  {status} {test_name}: {result.get('details', 'No details')}")

        overall_success = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"\n🎯 OVERALL RESULTS: {passed_tests}/{total_tests} ({overall_success:.1f}%)")

        if overall_success >= 80:
            print("🎉 EXCELLENT: Resume builder is highly functional!")
        elif overall_success >= 60:
            print("✅ GOOD: Resume builder is functional with some improvements needed")
        elif overall_success >= 40:
            print("⚠️ FAIR: Resume builder has basic functionality but needs significant work")
        else:
            print("❌ POOR: Resume builder needs major improvements")

    def test_export_comprehensive(self, page):
        """Comprehensive export testing"""
        print("\n📄 PHASE 6: COMPREHENSIVE EXPORT TESTING")
        print("-" * 50)

        export_tests = {
            'pdf_export': self.test_pdf_export,
            'docx_export': self.test_docx_export,
            'txt_export': self.test_txt_export,
            'download_functionality': self.test_download_functionality
        }

        for test_name, test_func in export_tests.items():
            try:
                result = test_func(page)
                self.test_results['export'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['export'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_pdf_export(self, page):
        """Test PDF export functionality"""
        export_buttons = [
            'button:has-text("Export PDF")', 'button:has-text("Download PDF")',
            'button:has-text("PDF")', '[data-format="pdf"]'
        ]

        pdf_export_found = any(page.query_selector(selector) for selector in export_buttons)

        return {
            'passed': pdf_export_found,
            'details': 'PDF export option found' if pdf_export_found else 'No PDF export option'
        }

    def test_docx_export(self, page):
        """Test DOCX export functionality"""
        export_buttons = [
            'button:has-text("Export DOCX")', 'button:has-text("Download DOCX")',
            'button:has-text("Word")', '[data-format="docx"]'
        ]

        docx_export_found = any(page.query_selector(selector) for selector in export_buttons)

        return {
            'passed': docx_export_found,
            'details': 'DOCX export option found' if docx_export_found else 'No DOCX export option'
        }

    def test_txt_export(self, page):
        """Test TXT export functionality"""
        export_buttons = [
            'button:has-text("Export TXT")', 'button:has-text("Download TXT")',
            'button:has-text("Text")', '[data-format="txt"]'
        ]

        txt_export_found = any(page.query_selector(selector) for selector in export_buttons)

        return {
            'passed': txt_export_found,
            'details': 'TXT export option found' if txt_export_found else 'No TXT export option'
        }

    def test_download_functionality(self, page):
        """Test download functionality"""
        download_buttons = [
            'button:has-text("Download")', 'button:has-text("Export")',
            '.download-btn', '[data-action="download"]'
        ]

        download_found = any(page.query_selector(selector) for selector in download_buttons)

        return {
            'passed': download_found,
            'details': 'Download functionality found' if download_found else 'No download functionality'
        }

    def test_validation_comprehensive(self, page):
        """Comprehensive validation testing"""
        print("\n✅ PHASE 7: COMPREHENSIVE VALIDATION TESTING")
        print("-" * 50)

        # This was already implemented in the forms section
        result = self.test_form_validation_comprehensive(page)
        self.test_results['validation']['form_validation'] = result
        status = "✅ PASS" if result['passed'] else "❌ FAIL"
        print(f"   form_validation: {status} - {result['details']}")

    def test_accessibility_comprehensive(self, page):
        """Comprehensive accessibility testing"""
        print("\n♿ PHASE 8: COMPREHENSIVE ACCESSIBILITY TESTING")
        print("-" * 50)

        accessibility_tests = {
            'aria_labels': self.test_aria_labels,
            'keyboard_navigation': self.test_keyboard_navigation,
            'focus_management': self.test_focus_management,
            'screen_reader_support': self.test_screen_reader_support
        }

        for test_name, test_func in accessibility_tests.items():
            try:
                result = test_func(page)
                self.test_results['accessibility'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['accessibility'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_aria_labels(self, page):
        """Test ARIA labels"""
        aria_elements = page.query_selector_all('[aria-label], [aria-labelledby], [aria-describedby]')
        form_elements = page.query_selector_all('input, button, select, textarea')

        if len(form_elements) > 0:
            aria_coverage = (len(aria_elements) / len(form_elements)) * 100
            return {
                'passed': aria_coverage >= 50,  # 50% coverage
                'details': f'ARIA coverage: {len(aria_elements)}/{len(form_elements)} elements ({aria_coverage:.1f}%)'
            }

        return {'passed': False, 'details': 'No form elements found for ARIA testing'}

    def test_keyboard_navigation(self, page):
        """Test keyboard navigation"""
        # Test Tab navigation
        focusable_elements = page.query_selector_all('input, button, select, textarea, a[href]')

        if len(focusable_elements) >= 3:
            # Focus first element
            focusable_elements[0].focus()

            # Press Tab to navigate
            page.keyboard.press('Tab')
            page.wait_for_timeout(200)

            # Check if focus moved
            focused_element = page.evaluate('document.activeElement.tagName')

            return {
                'passed': focused_element in ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA', 'A'],
                'details': f'Keyboard navigation working - focused: {focused_element}'
            }

        return {'passed': False, 'details': 'Not enough focusable elements for keyboard navigation test'}

    def test_focus_management(self, page):
        """Test focus management"""
        # Test if focus is properly managed when opening modals/dialogs
        modal_triggers = page.query_selector_all('button:has-text("Preview"), button:has-text("Template")')

        if modal_triggers:
            modal_triggers[0].click()
            page.wait_for_timeout(1000)

            # Check if focus moved to modal
            focused_element = page.evaluate('document.activeElement')

            return {
                'passed': focused_element is not None,
                'details': 'Focus management working for modals'
            }

        return {'passed': False, 'details': 'No modal triggers found for focus management test'}

    def test_screen_reader_support(self, page):
        """Test screen reader support"""
        # Check for screen reader specific elements
        sr_elements = page.query_selector_all('.sr-only, .screen-reader-only, [aria-hidden="true"]')
        headings = page.query_selector_all('h1, h2, h3, h4, h5, h6')

        return {
            'passed': len(sr_elements) > 0 or len(headings) > 0,
            'details': f'Screen reader support: {len(sr_elements)} SR elements, {len(headings)} headings'
        }

    def test_performance_comprehensive(self, page):
        """Comprehensive performance testing"""
        print("\n⚡ PHASE 9: COMPREHENSIVE PERFORMANCE TESTING")
        print("-" * 50)

        performance_tests = {
            'page_load_time': self.test_page_load_time,
            'form_responsiveness': self.test_form_responsiveness,
            'save_performance': self.test_save_performance
        }

        for test_name, test_func in performance_tests.items():
            try:
                result = test_func(page)
                self.test_results['performance'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['performance'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_page_load_time(self, page):
        """Test page load time"""
        start_time = time.time()
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')
        load_time = time.time() - start_time

        return {
            'passed': load_time < 5.0,  # 5 second threshold
            'details': f'Page load time: {load_time:.2f}s'
        }

    def test_form_responsiveness(self, page):
        """Test form responsiveness"""
        input_field = page.query_selector('input[type="text"]')
        if input_field:
            start_time = time.time()
            input_field.fill("Performance Test")
            response_time = time.time() - start_time

            return {
                'passed': response_time < 1.0,  # 1 second threshold
                'details': f'Form response time: {response_time:.3f}s'
            }

        return {'passed': False, 'details': 'No input field found for responsiveness test'}

    def test_save_performance(self, page):
        """Test save performance"""
        save_btn = page.query_selector('button:has-text("Save")')
        if save_btn:
            start_time = time.time()
            save_btn.click()
            page.wait_for_load_state('networkidle')
            save_time = time.time() - start_time

            return {
                'passed': save_time < 3.0,  # 3 second threshold
                'details': f'Save time: {save_time:.2f}s'
            }

        return {'passed': False, 'details': 'Save button not found for performance test'}

    def test_security_comprehensive(self, page):
        """Comprehensive security testing"""
        print("\n🔒 PHASE 10: COMPREHENSIVE SECURITY TESTING")
        print("-" * 50)

        security_tests = {
            'xss_protection': self.test_xss_protection,
            'csrf_protection': self.test_csrf_protection,
            'input_sanitization': self.test_input_sanitization
        }

        for test_name, test_func in security_tests.items():
            try:
                result = test_func(page)
                self.test_results['security'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['security'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_xss_protection(self, page):
        """Test XSS protection"""
        xss_payload = "<script>alert('xss')</script>"
        text_inputs = page.query_selector_all('input[type="text"], textarea')

        xss_protected = True
        for input_field in text_inputs[:3]:  # Test first 3 inputs
            try:
                input_field.fill(xss_payload)
                page.wait_for_timeout(500)

                # Check if script executed (bad) or was sanitized (good)
                page_content = page.content()
                if xss_payload in page_content and '<script>' in page_content:
                    xss_protected = False
                    break
            except:
                continue

        return {
            'passed': xss_protected,
            'details': 'XSS protection working' if xss_protected else 'XSS vulnerability detected'
        }

    def test_csrf_protection(self, page):
        """Test CSRF protection"""
        # Look for CSRF tokens
        csrf_tokens = page.query_selector_all('input[name*="csrf"], input[name*="token"], meta[name*="csrf"]')

        return {
            'passed': len(csrf_tokens) > 0,
            'details': f'CSRF tokens found: {len(csrf_tokens)}'
        }

    def test_input_sanitization(self, page):
        """Test input sanitization"""
        malicious_inputs = [
            "javascript:alert('test')",
            "<img src=x onerror=alert('test')>",
            "'; DROP TABLE users; --"
        ]

        sanitization_working = 0
        text_inputs = page.query_selector_all('input[type="text"], textarea')

        for malicious_input in malicious_inputs:
            for input_field in text_inputs[:1]:  # Test first input
                try:
                    input_field.fill(malicious_input)
                    page.wait_for_timeout(200)

                    # Check if input was sanitized
                    current_value = input_field.input_value()
                    if malicious_input != current_value:
                        sanitization_working += 1
                    break
                except:
                    continue

        return {
            'passed': sanitization_working > 0,
            'details': f'Input sanitization: {sanitization_working}/{len(malicious_inputs)} tests passed'
        }

    def test_edge_cases_comprehensive(self, page):
        """Comprehensive edge case testing"""
        print("\n🔬 PHASE 11: COMPREHENSIVE EDGE CASE TESTING")
        print("-" * 50)

        edge_tests = {
            'long_text_handling': self.test_long_text_handling,
            'special_characters': self.test_special_characters,
            'empty_form_submission': self.test_empty_form_submission,
            'browser_compatibility': self.test_browser_compatibility
        }

        for test_name, test_func in edge_tests.items():
            try:
                result = test_func(page)
                self.test_results['edge_cases'][test_name] = result
                status = "✅ PASS" if result['passed'] else "❌ FAIL"
                print(f"   {test_name}: {status} - {result['details']}")
            except Exception as e:
                self.test_results['edge_cases'][test_name] = {
                    'passed': False,
                    'details': f"Error: {str(e)}",
                    'error': True
                }
                print(f"   {test_name}: ❌ ERROR - {str(e)}")

    def test_long_text_handling(self, page):
        """Test handling of very long text"""
        long_text = "A" * 1000  # 1000 character string

        text_areas = page.query_selector_all('textarea')
        if text_areas:
            text_areas[0].fill(long_text)
            page.wait_for_timeout(500)

            current_value = text_areas[0].input_value()

            return {
                'passed': len(current_value) > 0,
                'details': f'Long text handling: {len(current_value)}/{len(long_text)} characters accepted'
            }

        return {'passed': False, 'details': 'No textarea found for long text test'}

    def test_special_characters(self, page):
        """Test handling of special characters"""
        special_chars = "áéíóú ñ ç 中文 العربية 🎉 @#$%^&*()"

        text_inputs = page.query_selector_all('input[type="text"]')
        if text_inputs:
            text_inputs[0].fill(special_chars)
            page.wait_for_timeout(500)

            current_value = text_inputs[0].input_value()

            return {
                'passed': len(current_value) > 0,
                'details': f'Special characters: {len(current_value)}/{len(special_chars)} characters accepted'
            }

        return {'passed': False, 'details': 'No text input found for special characters test'}

    def test_empty_form_submission(self, page):
        """Test empty form submission"""
        save_btn = page.query_selector('button:has-text("Save")')
        if save_btn:
            # Clear all inputs
            inputs = page.query_selector_all('input, textarea')
            for input_field in inputs:
                try:
                    input_field.fill("")
                except:
                    continue

            # Try to save
            save_btn.click()
            page.wait_for_timeout(1000)

            # Check for validation errors
            error_indicators = ['.error', '.invalid', '[role="alert"]']
            validation_shown = any(page.query_selector(selector) for selector in error_indicators)

            return {
                'passed': validation_shown,
                'details': 'Empty form validation working' if validation_shown else 'No validation for empty form'
            }

        return {'passed': False, 'details': 'Save button not found for empty form test'}

    def test_browser_compatibility(self, page):
        """Test browser compatibility features"""
        # Check for modern web features
        modern_features = [
            'input[type="date"]',
            'input[type="email"]',
            'input[type="tel"]',
            '[contenteditable]'
        ]

        features_supported = 0
        for feature in modern_features:
            if page.query_selector(feature):
                features_supported += 1

        return {
            'passed': features_supported > 0,
            'details': f'Modern features: {features_supported}/{len(modern_features)} supported'
        }

if __name__ == "__main__":
    tester = ResumeBuilderComprehensiveTester()
    tester.run_comprehensive_tests()
