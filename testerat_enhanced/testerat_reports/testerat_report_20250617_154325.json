{"metadata": {"report_id": "20250617_154325", "generated_at": "2025-06-17T15:43:25.145176", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.VANILLA_JS", "app_name": "Example Domain", "version": null, "features": [], "confidence": 0.0, "detection_methods": []}, "app_info": {"name": "Example Domain", "url": "https://example.com"}}, "summary": {"suite_name": "Enhanced Testerat - https://example.com", "total_tests": 10, "passed": 7, "failed": 2, "critical_issues": 0, "success_rate": 70.0, "total_execution_time": 10.944710969924927, "start_time": "2025-06-17T15:43:25.145322", "end_time": "2025-06-17T15:43:36.090030"}, "test_results": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:43:31.927766", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:43:32.618960", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T15:43:32.653678", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:32.665880", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:36.072895", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:36.073462", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:36.073609", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T15:43:36.073656", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T15:43:36.084531", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T15:43:36.089981", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:43:31.927766", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:32.665880", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement CSRF protection for form submissions", "Accessibility basics look good - consider comprehensive accessibility audit", "Add workflow testing if multi-step processes exist", "Implement proper access control for /profile", "Performance looks good - continue monitoring", "Implement proper access control for /dashboard", "Security basics look good - consider comprehensive security audit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 2, "passed": 1, "failed": 1}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 3, "failed": 1}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"HIGH": 1, "LOW": 8, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "protected_route_access", "fastest_test": "protected_route_access"}}