{"metadata": {"report_id": "20250619_155828", "generated_at": "2025-06-19T15:58:28.182143", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.NEXTJS", "app_name": "FAAFO Career Platform - Find Your Path to Career Freedom", "version": null, "features": ["interactive-elements", "navigation", "accessibility-labels", "aria-roles"], "confidence": 1.0, "detection_methods": ["DOM analysis"], "detected_patterns": ["_next/static", "next/"]}, "app_info": {"name": "FAAFO Career Platform - Find Your Path to Career Freedom", "url": "http://localhost:3000"}}, "summary": {"suite_name": "Enhanced Testerat - http://localhost:3000", "total_tests": 5, "passed": 3, "failed": 2, "critical_issues": 0, "success_rate": 60.0, "total_execution_time": 7.663517951965332, "start_time": "2025-06-19T15:58:28.182355", "end_time": "2025-06-19T15:58:35.845868"}, "test_results": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:30.335385", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signout: 500", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signout"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:35.717636", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:35.718634", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:35.718667", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-19T15:58:35.845862", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:30.335385", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signout: 500", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signout"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:35.717636", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement CSRF protection for form submissions", "Security basics look good - consider comprehensive security audit", "Fix server error on /api/auth/signout"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"api": {"total": 4, "passed": 2, "failed": 2}, "security": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"MEDIUM": 2, "LOW": 3}, "avg_execution_time": 0.0, "slowest_test": "csrf_protection", "fastest_test": "csrf_protection"}}