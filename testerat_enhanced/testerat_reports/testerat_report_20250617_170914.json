{"metadata": {"report_id": "20250617_170914", "generated_at": "2025-06-17T17:09:14.653678", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.REACT", "app_name": "httpbin.org", "version": null, "features": ["interactive-elements", "accessibility-labels"], "confidence": 0.9, "detection_methods": ["DOM analysis"], "detected_patterns": ["[data-reactroot]"]}, "app_info": {"name": "httpbin.org", "url": "https://httpbin.org"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org", "total_tests": 3, "passed": 3, "failed": 0, "critical_issues": 0, "success_rate": 100.0, "total_execution_time": 8.31021499633789, "start_time": "2025-06-17T17:09:14.653736", "end_time": "2025-06-17T17:09:22.963948"}, "test_results": [{"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T17:09:22.957846", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T17:09:22.962189", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T17:09:22.963939", "framework_detected": "react", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [], "recommendations": ["Performance looks good - continue monitoring", "Security basics look good - consider comprehensive security audit", "Accessibility basics look good - consider comprehensive accessibility audit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 3}, "avg_execution_time": 0.0, "slowest_test": "basic_security", "fastest_test": "basic_security"}}