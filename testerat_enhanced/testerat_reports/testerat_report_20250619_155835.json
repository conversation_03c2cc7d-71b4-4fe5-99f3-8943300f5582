{"metadata": {"report_id": "20250619_155835", "generated_at": "2025-06-19T15:58:35.877568", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.UNKNOWN", "app_name": "Unknown Application", "version": null, "features": [], "confidence": 0.1, "detection_methods": ["DOM analysis"], "detected_patterns": []}, "app_info": {"name": "Unknown Application", "url": "https://httpbin.org/delay/5"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/delay/5", "total_tests": 5, "passed": 4, "failed": 1, "critical_issues": 0, "success_rate": 80.0, "total_execution_time": 33.44648790359497, "start_time": "2025-06-19T15:58:35.877716", "end_time": "2025-06-19T15:59:09.324200"}, "test_results": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:46.135515", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:59:09.172409", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:59:09.172897", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:59:09.173516", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-19T15:59:09.324193", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-19T15:58:46.135515", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement CSRF protection for form submissions", "Security basics look good - consider comprehensive security audit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"api": {"total": 4, "passed": 3, "failed": 1}, "security": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"MEDIUM": 1, "LOW": 4}, "avg_execution_time": 0.0, "slowest_test": "csrf_protection", "fastest_test": "csrf_protection"}}