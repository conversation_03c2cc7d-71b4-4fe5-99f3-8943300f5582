{"metadata": {"report_id": "20250617_181909", "generated_at": "2025-06-17T18:19:09.605366", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.NEXTJS", "app_name": "FAAFO Career Platform - Find Your Path to Career Freedom", "version": null, "features": ["interactive-elements", "navigation", "accessibility-labels", "aria-roles"], "confidence": 1.0, "detection_methods": ["DOM analysis"], "detected_patterns": ["_next/static", "next/"]}, "app_info": {"name": "FAAFO Career Platform - Find Your Path to Career Freedom", "url": "http://localhost:3000"}}, "summary": {"suite_name": "Enhanced Testerat - http://localhost:3000", "total_tests": 13, "passed": 8, "failed": 4, "critical_issues": 0, "success_rate": 61.53846153846154, "total_execution_time": 13.700989961624146, "start_time": "2025-06-17T18:19:09.605501", "end_time": "2025-06-17T18:19:23.306488"}, "test_results": [{"test_name": "auth_state_consistency", "status": "FAILED", "details": "Logged-out content visible to authenticated user; No user-specific content found for authenticated user", "severity": "HIGH", "recommendations": ["Ensure authentication state is properly checked before rendering content", "Ensure authenticated users see personalized content"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:14.290320", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Tested 4 protected routes; Authenticated user redirected from protected route: /dashboard; Authenticated user redirected from protected route: /dashboard", "severity": "HIGH", "recommendations": ["Fix access control for /dashboard", "Fix access control for /dashboard"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:20.305450", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "session_management", "status": "FAILED", "details": "CSRF token not found", "severity": "HIGH", "recommendations": ["Implement CSRF protection"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:21.136203", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "logout_flow", "status": "FAILED", "details": "Logout button not found", "severity": "MEDIUM", "recommendations": ["Ensure logout functionality is accessible"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:21.138888", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:21.962944", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T18:19:21.985214", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "PASSED", "details": "CSRF protection working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T18:19:23.018797", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T18:19:23.195441", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T18:19:23.195755", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T18:19:23.195917", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T18:19:23.302231", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T18:19:23.305107", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T18:19:23.306481", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "auth_state_consistency", "status": "FAILED", "details": "Logged-out content visible to authenticated user; No user-specific content found for authenticated user", "severity": "HIGH", "recommendations": ["Ensure authentication state is properly checked before rendering content", "Ensure authenticated users see personalized content"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:14.290320", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Tested 4 protected routes; Authenticated user redirected from protected route: /dashboard; Authenticated user redirected from protected route: /dashboard", "severity": "HIGH", "recommendations": ["Fix access control for /dashboard", "Fix access control for /dashboard"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:20.305450", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "session_management", "status": "FAILED", "details": "CSRF token not found", "severity": "HIGH", "recommendations": ["Implement CSRF protection"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:21.136203", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "logout_flow", "status": "FAILED", "details": "Logout button not found", "severity": "MEDIUM", "recommendations": ["Ensure logout functionality is accessible"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T18:19:21.138888", "framework_detected": "nextjs", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Fix access control for /dashboard", "Implement CSRF protection", "Accessibility basics look good - consider comprehensive accessibility audit", "Add workflow testing if multi-step processes exist", "Security basics look good - consider comprehensive security audit", "Performance looks good - continue monitoring", "Ensure authentication state is properly checked before rendering content", "Ensure logout functionality is accessible", "Ensure authenticated users see personalized content"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 5, "passed": 1, "failed": 4}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 4, "failed": 0}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"HIGH": 3, "MEDIUM": 1, "LOW": 9}, "avg_execution_time": 0.0, "slowest_test": "auth_state_consistency", "fastest_test": "auth_state_consistency"}}