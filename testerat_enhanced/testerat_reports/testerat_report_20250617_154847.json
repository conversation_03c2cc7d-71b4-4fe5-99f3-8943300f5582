{"metadata": {"report_id": "20250617_154847", "generated_at": "2025-06-17T15:48:47.982055", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.VANILLA_JS", "app_name": "503 Service Temporarily Unavailable", "version": null, "features": [], "confidence": 0.0, "detection_methods": []}, "app_info": {"name": "503 Service Temporarily Unavailable", "url": "https://httpbin.org/html"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/html", "total_tests": 1, "passed": 1, "failed": 0, "critical_issues": 0, "success_rate": 100.0, "total_execution_time": 1.3883779048919678, "start_time": "2025-06-17T15:48:47.982150", "end_time": "2025-06-17T15:48:49.370525"}, "test_results": [{"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T15:48:49.370519", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [], "recommendations": ["Performance looks good - continue monitoring"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 1}, "avg_execution_time": 0.0, "slowest_test": "basic_performance", "fastest_test": "basic_performance"}}