{"metadata": {"report_id": "20250617_154348", "generated_at": "2025-06-17T15:43:48.562719", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.VANILLA_JS", "app_name": "503 Service Temporarily Unavailable", "version": null, "features": [], "confidence": 0.0, "detection_methods": []}, "app_info": {"name": "503 Service Temporarily Unavailable", "url": "https://httpbin.org/delay/5"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/delay/5", "total_tests": 5, "passed": 3, "failed": 2, "critical_issues": 0, "success_rate": 60.0, "total_execution_time": 2.4544429779052734, "start_time": "2025-06-17T15:43:48.562822", "end_time": "2025-06-17T15:43:51.017263"}, "test_results": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:49.923518", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signin: 503; Server error on /api/auth/signout: 503; Server error on /api/csrf-token: 503; Server error on /api/user/profile: 503; Server error on /api/forms/submit: 503", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/auth/signout", "Fix server error on /api/csrf-token", "Fix server error on /api/user/profile", "Fix server error on /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:51.016558", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:51.016997", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:51.017181", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T15:43:51.017251", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:49.923518", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signin: 503; Server error on /api/auth/signout: 503; Server error on /api/csrf-token: 503; Server error on /api/user/profile: 503; Server error on /api/forms/submit: 503", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/auth/signout", "Fix server error on /api/csrf-token", "Fix server error on /api/user/profile", "Fix server error on /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:43:51.016558", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement CSRF protection for form submissions", "Fix server error on /api/forms/submit", "Fix server error on /api/user/profile", "Fix server error on /api/csrf-token", "Fix server error on /api/auth/signout", "Fix server error on /api/auth/signin", "Security basics look good - consider comprehensive security audit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"api": {"total": 4, "passed": 2, "failed": 2}, "security": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"MEDIUM": 2, "LOW": 3}, "avg_execution_time": 0.0, "slowest_test": "csrf_protection", "fastest_test": "csrf_protection"}}