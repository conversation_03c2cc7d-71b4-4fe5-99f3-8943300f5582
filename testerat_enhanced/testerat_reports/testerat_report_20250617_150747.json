{"metadata": {"report_id": "20250617_150747", "generated_at": "2025-06-17T15:07:47.090538", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "Unknown Application", "version": null, "features": ["forms", "interactive-elements"], "confidence": 0.6, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "Unknown Application", "url": "https://httpbin.org/forms/post"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/forms/post", "total_tests": 5, "passed": 3, "failed": 2, "critical_issues": 0, "success_rate": 60.0, "total_execution_time": 1021.3745679855347, "start_time": "2025-06-17T15:07:47.090653", "end_time": "2025-06-17T15:24:48.465220"}, "test_results": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:07:52.499412", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "form_submissions", "status": "PASSED", "details": "Form submissions working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:07:52.736375", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Failed to test endpoint /api/auth/signin: APIRequestContext.get: Request timed out after 30000ms\nCall log:\n  - → GET https://httpbin.org/forms/post/api/auth/signin\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/auth/signout: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/auth/signout\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/csrf-token: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/csrf-token\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/user/profile: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/user/profile\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/forms/submit: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/forms/submit\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n", "severity": "MEDIUM", "recommendations": ["Check endpoint accessibility: /api/auth/signin", "Check endpoint accessibility: /api/auth/signout", "Check endpoint accessibility: /api/csrf-token", "Check endpoint accessibility: /api/user/profile", "Check endpoint accessibility: /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:24:48.463446", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:24:48.465153", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:24:48.465194", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:07:52.499412", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Failed to test endpoint /api/auth/signin: APIRequestContext.get: Request timed out after 30000ms\nCall log:\n  - → GET https://httpbin.org/forms/post/api/auth/signin\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/auth/signout: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/auth/signout\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/csrf-token: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/csrf-token\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/user/profile: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/user/profile\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n; Failed to test endpoint /api/forms/submit: APIRequestContext.get: getaddrinfo ENOTFOUND httpbin.org\nCall log:\n  - → GET https://httpbin.org/forms/post/api/forms/submit\n    - user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/136.0.7103.25 Safari/537.36\n    - accept: */*\n    - accept-encoding: gzip,deflate,br\n", "severity": "MEDIUM", "recommendations": ["Check endpoint accessibility: /api/auth/signin", "Check endpoint accessibility: /api/auth/signout", "Check endpoint accessibility: /api/csrf-token", "Check endpoint accessibility: /api/user/profile", "Check endpoint accessibility: /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:24:48.463446", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Check endpoint accessibility: /api/auth/signin", "Implement CSRF protection for form submissions", "Check endpoint accessibility: /api/csrf-token", "Check endpoint accessibility: /api/auth/signout", "Check endpoint accessibility: /api/user/profile", "Check endpoint accessibility: /api/forms/submit"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"api": {"total": 5, "passed": 3, "failed": 2}}, "severities": {"MEDIUM": 2, "LOW": 3}, "avg_execution_time": 0.0, "slowest_test": "csrf_protection", "fastest_test": "csrf_protection"}}