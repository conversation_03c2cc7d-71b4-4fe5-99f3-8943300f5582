{"metadata": {"report_id": "20250617_150608", "generated_at": "2025-06-17T15:06:08.080055", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.ANGULAR", "app_name": "Unknown Application", "version": null, "features": [], "confidence": 0.6, "detection_methods": ["DOM analysis"]}, "app_info": {"name": "Unknown Application", "url": "https://httpbin.org/html"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/html", "total_tests": 8, "passed": 4, "failed": 3, "critical_issues": 1, "success_rate": 50.0, "total_execution_time": 48.314563035964966, "start_time": "2025-06-17T15:06:08.080295", "end_time": "2025-06-17T15:06:56.394857"}, "test_results": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected; Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Verify authentication method or implement standard login forms", "Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:06:12.899749", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Error testing protected route /profile: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://httpbin.org/html/profile\", waiting until \"networkidle\"\n", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Check route accessibility: /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:06:43.611071", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:06:46.576391", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T15:06:46.612160", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:06:46.623268", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:06:56.377302", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:06:56.377868", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:06:56.378047", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected; Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Verify authentication method or implement standard login forms", "Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:06:12.899749", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "failures": [{"test_name": "authentication_flow", "status": "FAILED", "details": "No custom authentication elements detected; Authentication flow completed but user not properly authenticated", "severity": "CRITICAL", "recommendations": ["Verify authentication method or implement standard login forms", "Verify authentication success indicators and user state management"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:06:12.899749", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Error testing protected route /profile: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://httpbin.org/html/profile\", waiting until \"networkidle\"\n", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Check route accessibility: /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:06:43.611071", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:06:46.623268", "framework_detected": "angular", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Implement proper access control for /dashboard", "Verify authentication success indicators and user state management", "Implement CSRF protection for form submissions", "Verify authentication method or implement standard login forms", "Add workflow testing if multi-step processes exist", "Check route accessibility: /profile"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 3, "passed": 1, "failed": 2}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 3, "failed": 1}}, "severities": {"CRITICAL": 1, "HIGH": 1, "LOW": 5, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "authentication_flow", "fastest_test": "authentication_flow"}}