{"metadata": {"report_id": "20250617_154840", "generated_at": "2025-06-17T15:48:40.471789", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.VANILLA_JS", "app_name": "503 Service Temporarily Unavailable", "version": null, "features": [], "confidence": 0.0, "detection_methods": []}, "app_info": {"name": "503 Service Temporarily Unavailable", "url": "https://httpbin.org/forms/post"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org/forms/post", "total_tests": 10, "passed": 6, "failed": 3, "critical_issues": 0, "success_rate": 60.0, "total_execution_time": 7.475538969039917, "start_time": "2025-06-17T15:48:40.471930", "end_time": "2025-06-17T15:48:47.947466"}, "test_results": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:48:46.060364", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "auth_edge_cases", "status": "PASSED", "details": "Authentication edge cases handled properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:48:46.747429", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-17T15:48:46.783096", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:48:46.794795", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signin: 503; Server error on /api/auth/signout: 503; Server error on /api/csrf-token: 503; Server error on /api/user/profile: 503; Server error on /api/forms/submit: 503", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/auth/signout", "Fix server error on /api/csrf-token", "Fix server error on /api/user/profile", "Fix server error on /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:48:47.928064", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:48:47.929006", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:48:47.929134", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-17T15:48:47.929209", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-17T15:48:47.941115", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-17T15:48:47.947445", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "protected_route_access", "status": "FAILED", "details": "Unauthenticated access allowed to protected route: /dashboard; Unauthenticated access allowed to protected route: /profile", "severity": "HIGH", "recommendations": ["Implement proper access control for /dashboard", "Implement proper access control for /profile"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-17T15:48:46.060364", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:48:46.794795", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signin: 503; Server error on /api/auth/signout: 503; Server error on /api/csrf-token: 503; Server error on /api/user/profile: 503; Server error on /api/forms/submit: 503", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/auth/signout", "Fix server error on /api/csrf-token", "Fix server error on /api/user/profile", "Fix server error on /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-17T15:48:47.928064", "framework_detected": "vanilla_js", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Fix server error on /api/csrf-token", "Security basics look good - consider comprehensive security audit", "Implement proper access control for /dashboard", "Performance looks good - continue monitoring", "Fix server error on /api/auth/signin", "Fix server error on /api/user/profile", "Implement CSRF protection for form submissions", "Accessibility basics look good - consider comprehensive accessibility audit", "Implement proper access control for /profile", "Fix server error on /api/forms/submit", "Fix server error on /api/auth/signout", "Add workflow testing if multi-step processes exist"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 2, "passed": 1, "failed": 1}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 2, "failed": 2}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"HIGH": 1, "LOW": 7, "MEDIUM": 2}, "avg_execution_time": 0.0, "slowest_test": "protected_route_access", "fastest_test": "protected_route_access"}}