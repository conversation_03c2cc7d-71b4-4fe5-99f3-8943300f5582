#!/usr/bin/env python3
"""
Resume Builder Testing Runner

Quick runner script for Resume Builder comprehensive testing.
Provides easy command-line interface for testing the resume builder functionality.

Usage:
    python run_resume_builder_tests.py
    python run_resume_builder_tests.py --headless
    python run_resume_builder_tests.py --url http://localhost:3000
    python run_resume_builder_tests.py --verbose --headless
"""

import sys
import os
import argparse
from pathlib import Path

# Add testerat_enhanced to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from testerat_enhanced.examples.test_resume_builder import ResumeBuilderTester
from testerat_enhanced import UniversalTestConfig


def print_banner():
    """Print testing banner"""
    print("=" * 80)
    print("🎯 FAAFO Career Platform - Resume Builder Testing Suite")
    print("🔧 Enhanced Testerat Framework")
    print("=" * 80)


def print_test_phases():
    """Print test phases information"""
    phases = [
        "1. Authentication & Navigation Testing",
        "2. Resume Creation Flow Testing", 
        "3. Form Functionality Testing (Personal, Experience, Education, Skills)",
        "4. Data Persistence & Save Testing",
        "5. Template Selection & Preview Testing",
        "6. Resume Management Testing",
        "7. Error Handling & Validation Testing",
        "8. Security & Edge Case Testing"
    ]
    
    print("\n📋 Test Phases:")
    for phase in phases:
        print(f"   {phase}")
    print()


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Resume Builder Comprehensive Testing Suite",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test local development server
  python run_resume_builder_tests.py
  
  # Test with custom URL
  python run_resume_builder_tests.py --url http://localhost:3000
  
  # Run in headless mode (faster, no browser window)
  python run_resume_builder_tests.py --headless
  
  # Verbose output with detailed logging
  python run_resume_builder_tests.py --verbose
  
  # Full production-like testing
  python run_resume_builder_tests.py --url https://your-app.com --headless --verbose
        """
    )
    
    parser.add_argument(
        '--url',
        default='http://localhost:3001',
        help='URL to test (default: http://localhost:3001)'
    )
    
    parser.add_argument(
        '--headless', 
        action='store_true',
        help='Run in headless mode (no browser window)'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true',
        help='Enable verbose logging and detailed output'
    )
    
    parser.add_argument(
        '--quick',
        action='store_true',
        help='Run quick tests only (skip comprehensive security testing)'
    )
    
    parser.add_argument(
        '--output-dir',
        default='./testerat_reports',
        help='Directory for test reports (default: ./testerat_reports)'
    )
    
    args = parser.parse_args()
    
    # Print banner and info
    print_banner()
    print(f"📍 Target URL: {args.url}")
    print(f"🔧 Headless Mode: {'Yes' if args.headless else 'No'}")
    print(f"📝 Verbose Logging: {'Yes' if args.verbose else 'No'}")
    print(f"⚡ Quick Mode: {'Yes' if args.quick else 'No'}")
    print(f"📁 Output Directory: {args.output_dir}")
    
    print_test_phases()
    
    # Create configuration
    config = UniversalTestConfig()
    config.headless = args.headless
    config.detailed_logging = args.verbose

    # FORCE correct credentials for FAAFO app
    config.auth_config.test_users = {
        "standard": {"email": "<EMAIL>", "password": "testpassword"},
        "admin": {"email": "<EMAIL>", "password": "adminpassword"}
    }

    # DEBUG: Print the credentials being used
    print(f"🔍 DEBUG: Test credentials being used:")
    print(f"   Email: {config.auth_config.test_users['standard']['email']}")
    print(f"   Password: {config.auth_config.test_users['standard']['password']}")

    # Customize selectors for FAAFO app based on actual elements found
    config.auth_config.selectors.update({
        "auth_indicator": "button:has-text('Sign Out')",
        "logout_button": "button:has-text('Sign Out')",
        "user_menu": "a:has-text('Profile')",
        "email_input": "input[id='email']",
        "password_input": "input[id='password']",
        "login_button": "button[type='submit']"
    })

    # Update URLs and timeouts
    config.auth_config.login_url = "/login"
    config.auth_config.logout_url = "/api/auth/signout"
    config.auth_config.login_timeout = 10  # Increase timeout
    config.auth_config.verification_timeout = 8  # Increase verification timeout

    print(f"🔧 Updated FAAFO app authentication configuration:")
    print(f"   - Auth indicator: button:has-text('Sign Out')")
    print(f"   - Login timeout: 10 seconds")
    print(f"   - Verification timeout: 8 seconds")
    
    # Adjust config for quick mode
    if args.quick:
        config.test_security = False
        config.test_accessibility = False
        config.test_performance = False
        print("⚡ Quick mode enabled - skipping security, accessibility, and performance tests")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize tester
    print("🚀 Initializing Resume Builder Tester...")
    tester = ResumeBuilderTester(config)
    
    try:
        print("🎯 Starting comprehensive testing...")
        print("-" * 60)
        
        # Run comprehensive tests
        results = tester.run_comprehensive_resume_builder_tests(args.url)
        
        print("-" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("-" * 60)
        
        # Print base test results
        base_summary = results['summary']
        print(f"🔧 Base Framework Tests:")
        print(f"   Total: {base_summary['total_tests']}")
        print(f"   Passed: {base_summary['passed']}")
        print(f"   Failed: {base_summary['failed']}")
        print(f"   Success Rate: {base_summary['success_rate']:.1f}%")
        
        # Print specialized test results
        if 'specialized_tests' in results:
            spec_summary = results['summary']['specialized_tests']
            print(f"\n🎯 Resume Builder Specialized Tests:")
            print(f"   Total: {spec_summary['total']}")
            print(f"   Passed: {spec_summary['passed']}")
            print(f"   Failed: {spec_summary['failed']}")
            print(f"   Success Rate: {spec_summary['success_rate']:.1f}%")
        
        # Print critical issues
        total_critical = len(results.get('critical_issues', [])) + len(results.get('specialized_critical_issues', []))
        if total_critical > 0:
            print(f"\n⚠️ CRITICAL ISSUES FOUND: {total_critical}")
            print("-" * 40)
            
            # Show base critical issues
            for issue in results.get('critical_issues', [])[:3]:
                print(f"🔧 {issue['test_name']}: {issue['details']}")
            
            # Show specialized critical issues
            for issue in results.get('specialized_critical_issues', [])[:3]:
                print(f"🎯 {issue['test_name']}: {issue['details']}")
            
            if total_critical > 6:
                print(f"   ... and {total_critical - 6} more issues")
        else:
            print("\n✅ No critical issues found!")
        
        # Print recommendations
        if results.get('recommendations'):
            print(f"\n💡 TOP RECOMMENDATIONS:")
            for i, rec in enumerate(results['recommendations'][:5], 1):
                print(f"   {i}. {rec}")
        
        # Print report files
        if results.get('report_files'):
            print(f"\n📄 GENERATED REPORTS:")
            for report_file in results['report_files']:
                print(f"   📋 {report_file}")
        
        # Print execution summary
        session = results.get('session', {})
        print(f"\n⏱️ EXECUTION SUMMARY:")
        print(f"   Framework Detected: {session.get('framework', 'Unknown')}")
        print(f"   Application: {session.get('app_name', 'Unknown')}")
        print(f"   Execution Time: {session.get('execution_time', 0):.2f} seconds")
        
        print("\n" + "=" * 80)
        print("✅ Resume Builder testing completed successfully!")
        print("📋 Check the generated reports for detailed analysis.")
        print("=" * 80)
        
        # Return appropriate exit code
        if total_critical > 0:
            print("⚠️ Exiting with code 1 due to critical issues found.")
            return 1
        else:
            return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
        return 130
    except Exception as e:
        print(f"\n❌ Testing failed with error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
