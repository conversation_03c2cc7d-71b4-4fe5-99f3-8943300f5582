#!/usr/bin/env python3
"""
Test Dashboard Fixes
Quick test to verify the dashboard consolidation and error handling fixes
"""

from playwright.sync_api import sync_playwright
import time

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        print("✅ Login successful")
        return True
    else:
        print("❌ Login failed - elements not found")
        return False

def test_dashboard_error_handling(page):
    """Test that dashboard handles missing data gracefully"""
    print("\n🏠 TESTING DASHBOARD ERROR HANDLING")
    print("=" * 50)
    
    # Navigate to dashboard
    page.goto("http://localhost:3001/dashboard")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Check for console errors
    console_errors = []
    page.on("console", lambda msg: console_errors.append(msg.text) if msg.type == "error" else None)
    
    # Test Overview tab (should load without errors)
    print("   🎯 Testing Overview tab...")
    overview_tab = page.query_selector('button:has-text("Overview")')
    if overview_tab:
        overview_tab.click()
        page.wait_for_timeout(2000)
        print("   ✅ Overview tab loaded")
    else:
        print("   ❌ Overview tab not found")
    
    # Test Analytics tab (this was causing errors before)
    print("   🎯 Testing Analytics tab...")
    analytics_tab = page.query_selector('button:has-text("Analytics")')
    if analytics_tab:
        analytics_tab.click()
        page.wait_for_timeout(3000)
        
        # Check if analytics content loaded
        analytics_content = page.query_selector('text="My Learning Dashboard"')
        if analytics_content:
            print("   ✅ Analytics tab loaded successfully")
        else:
            print("   ⚠️ Analytics tab loaded but content may be missing")
    else:
        print("   ❌ Analytics tab not found")
    
    # Test Progress & Analytics button from overview
    print("   🎯 Testing Progress & Analytics button...")
    overview_tab = page.query_selector('button:has-text("Overview")')
    if overview_tab:
        overview_tab.click()
        page.wait_for_timeout(1000)
        
        # Find and click the Progress & Analytics button
        progress_button = page.query_selector('button:has-text("Progress & Analytics")')
        if progress_button:
            progress_button.click()
            page.wait_for_timeout(2000)
            
            # Check if it switched to analytics tab
            analytics_content = page.query_selector('text="My Learning Dashboard"')
            if analytics_content:
                print("   ✅ Progress & Analytics button works correctly")
            else:
                print("   ❌ Progress & Analytics button didn't switch tabs")
        else:
            print("   ❌ Progress & Analytics button not found")
    
    # Check for critical console errors
    critical_errors = [err for err in console_errors if 'Failed to fetch' in err or '500' in err or '404' in err]
    
    if len(critical_errors) == 0:
        print("   ✅ No critical console errors found")
    else:
        print(f"   ⚠️ Found {len(critical_errors)} console errors:")
        for error in critical_errors[:3]:  # Show first 3 errors
            print(f"      - {error}")
    
    return len(critical_errors) == 0

def test_all_tabs(page):
    """Test all dashboard tabs work"""
    print("\n📑 TESTING ALL DASHBOARD TABS")
    print("=" * 50)
    
    page.goto("http://localhost:3001/dashboard")
    page.wait_for_load_state('networkidle')
    time.sleep(2)
    
    tabs = ['Overview', 'Progress', 'Goals', 'Achievements', 'Analytics']
    results = []
    
    for tab_name in tabs:
        print(f"   🎯 Testing {tab_name} tab...")
        try:
            tab_button = page.query_selector(f'button:has-text("{tab_name}")')
            if tab_button:
                tab_button.click()
                page.wait_for_timeout(2000)
                
                # Check if tab is active
                active_content = page.query_selector('[data-state="active"]')
                if active_content:
                    print(f"   ✅ {tab_name} tab working")
                    results.append(True)
                else:
                    print(f"   ❌ {tab_name} tab not active")
                    results.append(False)
            else:
                print(f"   ❌ {tab_name} tab button not found")
                results.append(False)
        except Exception as e:
            print(f"   ❌ {tab_name} tab error: {e}")
            results.append(False)
    
    success_rate = (sum(results) / len(results)) * 100
    print(f"\n   📊 Tab Success Rate: {success_rate:.1f}% ({sum(results)}/{len(results)})")
    
    return success_rate >= 80

def main():
    print("🧪 TESTING DASHBOARD FIXES")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run tests
            error_handling_ok = test_dashboard_error_handling(page)
            all_tabs_ok = test_all_tabs(page)
            
            # Final assessment
            print("\n" + "=" * 60)
            print("📊 FINAL TEST RESULTS")
            print("=" * 60)
            
            print(f"✅ Error Handling: {'PASS' if error_handling_ok else 'FAIL'}")
            print(f"✅ All Tabs Working: {'PASS' if all_tabs_ok else 'FAIL'}")
            
            if error_handling_ok and all_tabs_ok:
                print("\n🎉 ALL TESTS PASSED!")
                print("Dashboard consolidation and error handling fixes are working correctly.")
            else:
                print("\n⚠️ SOME TESTS FAILED")
                print("There may still be issues that need attention.")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 10 seconds for inspection...")
            time.sleep(10)
            browser.close()

if __name__ == "__main__":
    main()
