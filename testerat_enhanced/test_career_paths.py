#!/usr/bin/env python3
"""
Comprehensive Career Paths Testing
Tests career path browsing, filtering, detailed views, and bookmarking functionality
"""

from playwright.sync_api import sync_playwright
import time

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        return True
    return False

def test_career_paths_page_access(page):
    """Test accessing career paths page"""
    print("\n🛤️ TESTING CAREER PATHS PAGE ACCESS")
    print("=" * 50)
    
    results = []
    
    # Test direct URL access
    print("   🎯 Testing direct URL access...")
    try:
        page.goto("http://localhost:3001/career-paths")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Check if page loads with career paths
        page_title = page.query_selector('h1, h2')
        career_cards = page.query_selector_all('[data-testid="career-path-card"], .career-path-card, .career-card')
        
        if page_title and ('career' in page_title.text_content().lower() or 'path' in page_title.text_content().lower()):
            print("   ✅ Career paths page loads with title")
            results.append(('Page Title', True))
        else:
            print("   ❌ Career paths page title not found")
            results.append(('Page Title', False))
        
        if len(career_cards) > 0:
            print(f"   ✅ Found {len(career_cards)} career path cards")
            results.append(('Career Path Cards', True))
        else:
            print("   ❌ No career path cards found")
            results.append(('Career Path Cards', False))
            
    except Exception as e:
        print(f"   ❌ Page access error: {e}")
        results.append(('Page Title', False))
        results.append(('Career Path Cards', False))
    
    return results

def test_career_path_filtering(page):
    """Test career path filtering and search functionality"""
    print("\n🔍 TESTING CAREER PATH FILTERING")
    print("=" * 50)
    
    results = []
    
    page.goto("http://localhost:3001/career-paths")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Test 1: Search functionality
    print("   🎯 Testing search functionality...")
    try:
        search_input = page.query_selector('input[placeholder*="search"], input[name*="search"], [data-testid="search-input"]')
        
        if search_input:
            # Test search
            search_input.fill("developer")
            page.wait_for_timeout(2000)
            
            # Check if results are filtered
            career_cards = page.query_selector_all('[data-testid="career-path-card"], .career-path-card, .career-card')
            if len(career_cards) > 0:
                print("   ✅ Search functionality working")
                results.append(('Search Functionality', True))
            else:
                print("   ⚠️ Search executed but no results shown")
                results.append(('Search Functionality', False))
        else:
            print("   ❌ Search input not found")
            results.append(('Search Functionality', False))
    except Exception as e:
        print(f"   ❌ Search functionality error: {e}")
        results.append(('Search Functionality', False))
    
    # Test 2: Category filtering
    print("   🎯 Testing category filtering...")
    try:
        # Look for category filters
        category_filters = page.query_selector_all('button[data-category], .category-filter, select[name*="category"]')
        category_dropdown = page.query_selector('select[name*="category"], [data-testid="category-select"]')
        
        if len(category_filters) > 0 or category_dropdown:
            if category_dropdown:
                # Test dropdown selection
                category_dropdown.select_option(index=1)  # Select second option
                page.wait_for_timeout(2000)
            elif len(category_filters) > 0:
                # Test filter buttons
                category_filters[0].click()
                page.wait_for_timeout(2000)
            
            print("   ✅ Category filtering available")
            results.append(('Category Filtering', True))
        else:
            print("   ❌ Category filtering not found")
            results.append(('Category Filtering', False))
    except Exception as e:
        print(f"   ❌ Category filtering error: {e}")
        results.append(('Category Filtering', False))
    
    # Test 3: Salary range filtering
    print("   🎯 Testing salary range filtering...")
    try:
        salary_filter = page.query_selector('input[name*="salary"], [data-testid="salary-filter"], .salary-range')
        
        if salary_filter:
            print("   ✅ Salary filtering available")
            results.append(('Salary Filtering', True))
        else:
            print("   ❌ Salary filtering not found")
            results.append(('Salary Filtering', False))
    except Exception as e:
        print(f"   ❌ Salary filtering error: {e}")
        results.append(('Salary Filtering', False))
    
    return results

def test_career_path_details(page):
    """Test career path detail pages"""
    print("\n📋 TESTING CAREER PATH DETAILS")
    print("=" * 50)
    
    results = []
    
    page.goto("http://localhost:3001/career-paths")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Test 1: Click on career path card
    print("   🎯 Testing career path detail navigation...")
    try:
        career_cards = page.query_selector_all('[data-testid="career-path-card"], .career-path-card, .career-card')
        
        if len(career_cards) > 0:
            # Click on first career path
            career_cards[0].click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Check if we're on a detail page
            current_url = page.url
            if '/career-paths/' in current_url and current_url != 'http://localhost:3001/career-paths':
                print("   ✅ Career path detail navigation working")
                results.append(('Detail Navigation', True))
            else:
                print("   ❌ Career path detail navigation failed")
                results.append(('Detail Navigation', False))
        else:
            print("   ❌ No career path cards to click")
            results.append(('Detail Navigation', False))
    except Exception as e:
        print(f"   ❌ Detail navigation error: {e}")
        results.append(('Detail Navigation', False))
    
    # Test 2: Detail page content
    print("   🎯 Testing detail page content...")
    try:
        # Look for detailed information
        description = page.query_selector('[data-testid="description"], .description, .career-description')
        salary_info = page.query_selector('[data-testid="salary"], .salary-info, text*="$"')
        requirements = page.query_selector('[data-testid="requirements"], .requirements, .skills-required')
        
        content_found = 0
        if description:
            content_found += 1
        if salary_info:
            content_found += 1
        if requirements:
            content_found += 1
        
        if content_found >= 2:
            print("   ✅ Detail page has comprehensive content")
            results.append(('Detail Content', True))
        else:
            print("   ❌ Detail page content incomplete")
            results.append(('Detail Content', False))
    except Exception as e:
        print(f"   ❌ Detail content error: {e}")
        results.append(('Detail Content', False))
    
    return results

def test_career_path_bookmarking(page):
    """Test career path bookmarking functionality"""
    print("\n🔖 TESTING CAREER PATH BOOKMARKING")
    print("=" * 50)
    
    results = []
    
    # Test bookmarking from list view
    print("   🎯 Testing bookmarking from list view...")
    try:
        page.goto("http://localhost:3001/career-paths")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Look for bookmark buttons
        bookmark_btns = page.query_selector_all('button[data-testid="bookmark"], .bookmark-btn, button:has-text("Bookmark")')
        
        if len(bookmark_btns) > 0:
            # Try to bookmark first career path
            bookmark_btns[0].click()
            page.wait_for_timeout(2000)
            
            print("   ✅ Bookmark functionality available")
            results.append(('Bookmark from List', True))
        else:
            print("   ❌ Bookmark buttons not found")
            results.append(('Bookmark from List', False))
    except Exception as e:
        print(f"   ❌ Bookmarking error: {e}")
        results.append(('Bookmark from List', False))
    
    # Test viewing bookmarked paths
    print("   🎯 Testing bookmarked paths view...")
    try:
        # Look for bookmarks page or filter
        bookmarks_link = page.query_selector('a[href*="bookmark"], button:has-text("Bookmarked"), [data-testid="bookmarks-link"]')
        
        if bookmarks_link:
            bookmarks_link.click()
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            print("   ✅ Bookmarked paths view available")
            results.append(('View Bookmarks', True))
        else:
            print("   ❌ Bookmarked paths view not found")
            results.append(('View Bookmarks', False))
    except Exception as e:
        print(f"   ❌ View bookmarks error: {e}")
        results.append(('View Bookmarks', False))
    
    return results

def test_career_path_recommendations(page):
    """Test career path recommendations and related features"""
    print("\n💡 TESTING CAREER PATH RECOMMENDATIONS")
    print("=" * 50)
    
    results = []
    
    # Test 1: Recommended paths section
    print("   🎯 Testing recommended paths...")
    try:
        page.goto("http://localhost:3001/career-paths")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Look for recommendations section
        recommendations = page.query_selector('[data-testid="recommendations"], .recommendations, .recommended-paths')
        recommended_text = page.query_selector('text*="Recommended", text*="For You"')
        
        if recommendations or recommended_text:
            print("   ✅ Career path recommendations available")
            results.append(('Recommendations', True))
        else:
            print("   ❌ Career path recommendations not found")
            results.append(('Recommendations', False))
    except Exception as e:
        print(f"   ❌ Recommendations error: {e}")
        results.append(('Recommendations', False))
    
    # Test 2: Related paths
    print("   🎯 Testing related paths...")
    try:
        # Navigate to a career path detail page first
        career_cards = page.query_selector_all('[data-testid="career-path-card"], .career-path-card, .career-card')
        
        if len(career_cards) > 0:
            career_cards[0].click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Look for related paths section
            related_paths = page.query_selector('[data-testid="related-paths"], .related-paths, text*="Related"')
            
            if related_paths:
                print("   ✅ Related paths available")
                results.append(('Related Paths', True))
            else:
                print("   ❌ Related paths not found")
                results.append(('Related Paths', False))
        else:
            print("   ❌ Cannot test related paths - no career cards")
            results.append(('Related Paths', False))
    except Exception as e:
        print(f"   ❌ Related paths error: {e}")
        results.append(('Related Paths', False))
    
    return results

def main():
    print("🧪 COMPREHENSIVE CAREER PATHS TESTING")
    print("=" * 70)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        all_results = []
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run all test suites
            access_results = test_career_paths_page_access(page)
            filtering_results = test_career_path_filtering(page)
            details_results = test_career_path_details(page)
            bookmark_results = test_career_path_bookmarking(page)
            recommendation_results = test_career_path_recommendations(page)
            
            all_results.extend(access_results)
            all_results.extend(filtering_results)
            all_results.extend(details_results)
            all_results.extend(bookmark_results)
            all_results.extend(recommendation_results)
            
            # Generate report
            print("\n" + "=" * 70)
            print("📊 CAREER PATHS TEST RESULTS")
            print("=" * 70)
            
            passed = sum(1 for _, success in all_results if success)
            total = len(all_results)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 OVERALL SUMMARY:")
            print(f"   Total Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {total - passed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for test_name, success in all_results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {status}: {test_name}")
            
            print(f"\n🎯 FINAL ASSESSMENT:")
            if success_rate >= 90:
                print("🎉 EXCELLENT: Career Paths feature working perfectly!")
            elif success_rate >= 80:
                print("✅ GOOD: Most functionality working, minor issues to address")
            elif success_rate >= 70:
                print("⚠️ FAIR: Core functionality working, some improvements needed")
            else:
                print("❌ POOR: Significant issues need immediate attention")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 15 seconds for inspection...")
            time.sleep(15)
            browser.close()

if __name__ == "__main__":
    main()
