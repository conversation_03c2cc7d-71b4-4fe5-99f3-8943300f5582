#!/usr/bin/env python3
"""
Test our authentication fixes
"""

from playwright.sync_api import sync_playwright
import time

def test_auth_fix():
    """Test if our authentication selector fixes work"""
    
    print("🔍 Testing Authentication Fix...")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Navigate to login page
            print("1️⃣ Navigating to login page...")
            page.goto("http://localhost:3001/login")
            page.wait_for_load_state('networkidle')
            print(f"   Current URL: {page.url}")
            
            # Find form elements
            print("2️⃣ Finding form elements...")
            email_input = page.query_selector("input[id='email']")
            password_input = page.query_selector("input[id='password']")
            submit_btn = page.query_selector("button[type='submit']")
            
            print(f"   Email input found: {bool(email_input)}")
            print(f"   Password input found: {bool(password_input)}")
            print(f"   Submit button found: {bool(submit_btn)}")
            
            if not all([email_input, password_input, submit_btn]):
                print("❌ Form elements not found!")
                return False
            
            # Fill credentials
            print("3️⃣ Filling credentials...")
            email_input.fill("<EMAIL>")
            password_input.fill("testpassword")
            
            # Submit form
            print("4️⃣ Submitting form...")
            submit_btn.click()
            page.wait_for_load_state('networkidle')
            
            # Wait for authentication to complete (our fix)
            print("5️⃣ Waiting for authentication to complete...")
            time.sleep(3)
            
            print(f"   Current URL after login: {page.url}")
            
            # Check for authentication indicators (our improved selectors)
            print("6️⃣ Checking for authentication indicators...")
            
            # Original selectors (should fail)
            old_auth_indicator = page.query_selector('.user-name, .username, [data-testid="user-indicator"]')
            old_user_menu = page.query_selector('.user-menu, .profile-menu, [data-testid="user-menu"]')
            
            # Our improved selectors (should work)
            profile_link = page.query_selector('[aria-label="View your profile"]')
            signout_btn = page.query_selector('[aria-label="Sign out of your account"]')
            dashboard_link = page.query_selector('[aria-label="Go to your dashboard"]')
            
            print(f"   Old auth indicator: {bool(old_auth_indicator)}")
            print(f"   Old user menu: {bool(old_user_menu)}")
            print(f"   Profile link (new): {bool(profile_link)}")
            print(f"   Sign out button (new): {bool(signout_btn)}")
            print(f"   Dashboard link (new): {bool(dashboard_link)}")
            
            # Check if we're authenticated
            is_authenticated = any([profile_link, signout_btn, dashboard_link])
            
            if is_authenticated:
                print("✅ AUTHENTICATION SUCCESSFUL!")
                print("✅ Our selector improvements are working!")
                
                # Test navigation to dashboard
                print("7️⃣ Testing dashboard access...")
                page.goto("http://localhost:3001/dashboard")
                page.wait_for_load_state('networkidle')
                time.sleep(2)
                
                if "/dashboard" in page.url:
                    print("✅ Dashboard access successful!")
                else:
                    print(f"❌ Dashboard access failed - redirected to: {page.url}")
                
                return True
            else:
                print("❌ AUTHENTICATION FAILED!")
                print("❌ No authentication indicators found")
                return False
                
        except Exception as e:
            print(f"❌ Error during test: {e}")
            return False
        finally:
            print("🔍 Keeping browser open for 5 seconds for inspection...")
            time.sleep(5)
            browser.close()

if __name__ == "__main__":
    success = test_auth_fix()
    if success:
        print("\n🎉 AUTHENTICATION FIX TEST PASSED!")
    else:
        print("\n💥 AUTHENTICATION FIX TEST FAILED!")
