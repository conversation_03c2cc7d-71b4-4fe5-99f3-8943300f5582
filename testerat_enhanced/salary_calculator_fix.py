#!/usr/bin/env python3
"""
Salary Calculator Fix
Targeted fix for the salary calculator calculate button issue
"""

from playwright.sync_api import sync_playwright
import time

def test_salary_calculator_detailed():
    """Detailed salary calculator testing to identify the calculate button issue"""
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Navigate to login first
            page.goto("http://localhost:3001/login")
            page.wait_for_load_state('networkidle')
            
            # Login
            email_input = page.query_selector("input[id='email']")
            password_input = page.query_selector("input[id='password']")
            submit_btn = page.query_selector("button[type='submit']")
            
            if all([email_input, password_input, submit_btn]):
                email_input.fill("<EMAIL>")
                password_input.fill("testpassword")
                submit_btn.click()
                page.wait_for_load_state('networkidle')
                time.sleep(3)
            
            # Navigate to salary calculator
            page.goto("http://localhost:3001/tools/salary-calculator")
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            print("🔍 DETAILED SALARY CALCULATOR ANALYSIS")
            print("=" * 50)
            
            # Check initial state
            calculate_btn = page.query_selector('button:has-text("Calculate Salary")')
            if calculate_btn:
                is_disabled = calculate_btn.get_attribute('disabled')
                print(f"Initial button state - Disabled: {is_disabled}")
                
                # Check what might be causing the button to be disabled
                button_text = calculate_btn.text_content()
                print(f"Button text: '{button_text}'")
                
                # Check for loading states
                loading_indicators = page.query_selector_all('[class*="loading"], [class*="spin"]')
                print(f"Loading indicators found: {len(loading_indicators)}")
                
                # Check CSRF token
                csrf_elements = page.query_selector_all('[name*="csrf"], [data-csrf]')
                print(f"CSRF elements found: {len(csrf_elements)}")
                
                # Check form validation
                form_errors = page.query_selector_all('.error, [class*="error"], [role="alert"]')
                print(f"Form errors found: {len(form_errors)}")
                
                # Fill out the form step by step and check button state
                print("\n📝 FILLING FORM STEP BY STEP:")
                
                # Step 1: Career Path
                print("1️⃣ Selecting Career Path...")
                career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
                if career_trigger:
                    career_trigger.click(force=True)
                    page.wait_for_timeout(1000)
                    
                    data_scientist = page.query_selector('text="Data Scientist"')
                    if data_scientist:
                        data_scientist.click(force=True)
                        page.wait_for_timeout(1000)
                        
                        # Check button state after career path
                        is_disabled = calculate_btn.get_attribute('disabled')
                        print(f"   After career path - Disabled: {is_disabled}")
                
                # Step 2: Experience Level
                print("2️⃣ Selecting Experience Level...")
                exp_trigger = page.query_selector('[data-testid="experience-trigger"]')
                if exp_trigger:
                    exp_trigger.click(force=True)
                    page.wait_for_timeout(1000)
                    
                    mid_level = page.query_selector('text="Mid Level"')
                    if mid_level:
                        mid_level.click(force=True)
                        page.wait_for_timeout(1000)
                        
                        # Check button state after experience
                        is_disabled = calculate_btn.get_attribute('disabled')
                        print(f"   After experience - Disabled: {is_disabled}")
                
                # Step 3: Location
                print("3️⃣ Selecting Location...")
                loc_trigger = page.query_selector('[data-testid="location-trigger"]')
                if loc_trigger:
                    loc_trigger.click(force=True)
                    page.wait_for_timeout(1000)
                    
                    sf_option = page.query_selector('text="San Francisco"')
                    if sf_option:
                        sf_option.click(force=True)
                        page.wait_for_timeout(1000)
                        
                        # Check button state after location
                        is_disabled = calculate_btn.get_attribute('disabled')
                        print(f"   After location - Disabled: {is_disabled}")
                
                # Step 4: Skills
                print("4️⃣ Adding Skills...")
                skills_input = page.query_selector('input[placeholder*="skill"]')
                if skills_input:
                    skills_input.fill("Python")
                    page.keyboard.press('Enter')
                    page.wait_for_timeout(1000)
                    
                    # Check button state after skills
                    is_disabled = calculate_btn.get_attribute('disabled')
                    print(f"   After skills - Disabled: {is_disabled}")
                
                # Step 5: Wait a bit more and check again
                print("5️⃣ Waiting for any async operations...")
                page.wait_for_timeout(3000)
                
                is_disabled = calculate_btn.get_attribute('disabled')
                print(f"   Final check - Disabled: {is_disabled}")
                
                # Check what's in the disabled attribute
                if is_disabled is not None:
                    print(f"   Disabled attribute value: '{is_disabled}'")
                
                # Try to get more info about why it's disabled
                button_classes = calculate_btn.get_attribute('class')
                print(f"   Button classes: {button_classes}")
                
                # Check if there are any validation errors
                all_errors = page.query_selector_all('.text-red-600, .text-red-500, [class*="error"]')
                if all_errors:
                    print(f"   Validation errors found: {len(all_errors)}")
                    for i, error in enumerate(all_errors[:3]):  # Show first 3 errors
                        error_text = error.text_content()
                        print(f"     Error {i+1}: {error_text}")
                
                # Check form data
                print("\n📊 FORM DATA CHECK:")
                career_path_value = page.evaluate("""
                    () => {
                        const trigger = document.querySelector('[data-testid="career-path-trigger"]');
                        return trigger ? trigger.textContent : 'Not found';
                    }
                """)
                print(f"   Career Path Value: {career_path_value}")
                
                # Try clicking anyway with force
                print("\n🚀 ATTEMPTING FORCE CLICK:")
                try:
                    calculate_btn.click(force=True)
                    page.wait_for_timeout(5000)
                    
                    # Check for results
                    results = page.query_selector('text="Salary Estimate"')
                    if results:
                        print("   ✅ Force click worked! Results appeared.")
                    else:
                        print("   ❌ Force click didn't produce results.")
                        
                        # Check for any error messages
                        error_messages = page.query_selector_all('[role="alert"], .alert, [class*="error"]')
                        if error_messages:
                            print(f"   Found {len(error_messages)} error messages:")
                            for error in error_messages:
                                print(f"     - {error.text_content()}")
                        
                except Exception as e:
                    print(f"   ❌ Force click failed: {e}")
            
            else:
                print("❌ Calculate button not found")
            
        except Exception as e:
            print(f"❌ CRITICAL ERROR: {e}")
        finally:
            print("\n🔍 Keeping browser open for inspection...")
            time.sleep(20)
            browser.close()

if __name__ == "__main__":
    test_salary_calculator_detailed()
