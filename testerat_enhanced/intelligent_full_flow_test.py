#!/usr/bin/env python3
"""
INTELLIGENT FULL FLOW TESTING
Complete end-to-end testing of FAAFO Career Platform
Tests: Login → Dashboard → Assessment → Salary Calculator → Resume Builder → Interview Practice → Career Paths
"""

from playwright.sync_api import sync_playwright
import time
import json

class IntelligentFullFlowTester:
    def __init__(self):
        self.current_state = "unknown"
        self.authentication_status = "unknown"
        self.current_url = ""
        self.test_results = []
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpassword'
        }
        
        # Test data for different features
        self.assessment_data = {
            'experience_level': 'intermediate',
            'preferred_role': 'software_engineer',
            'skills': ['JavaScript', 'Python', 'React']
        }
        
        self.salary_data = {
            'job_title': 'Software Engineer',
            'location': 'San Francisco, CA',
            'experience_years': '3'
        }
        
        self.resume_data = {
            'firstName': 'John',
            'lastName': 'Doe',
            'email': '<EMAIL>',
            'phone': '+****************'
        }

    def detect_current_state(self, page):
        """Intelligently detect current application state"""
        self.current_url = page.url
        print(f"🔍 Current URL: {self.current_url}")
        
        # Detect authentication status
        self.authentication_status = self.detect_authentication_status(page)
        
        # Detect specific page states
        if "/login" in self.current_url:
            self.current_state = "login_page"
        elif "/dashboard" in self.current_url:
            self.current_state = "dashboard"
        elif "/assessment" in self.current_url:
            self.current_state = "assessment"
        elif "/salary-calculator" in self.current_url:
            self.current_state = "salary_calculator"
        elif "/resume-builder" in self.current_url:
            self.current_state = "resume_builder"
        elif "/interview-practice" in self.current_url:
            self.current_state = "interview_practice"
        elif "/career-paths" in self.current_url:
            self.current_state = "career_paths"
        elif "/tools" in self.current_url:
            self.current_state = "tools_page"
        elif self.current_url.endswith("/") or "/home" in self.current_url:
            self.current_state = "home_page"
        else:
            self.current_state = "unknown_page"
            
        print(f"   🎯 State: {self.current_state} | Auth: {self.authentication_status}")
        return self.current_state

    def detect_authentication_status(self, page):
        """Detect authentication status"""
        auth_indicators = [
            '[aria-label="Sign out of your account"]',
            '[aria-label="View your profile"]',
            'button:has-text("Sign out")'
        ]
        
        unauth_indicators = [
            '[aria-label="Log in to your account"]',
            'button:has-text("Sign in")',
            'a[href*="login"]'
        ]
        
        if any(page.query_selector(selector) for selector in auth_indicators):
            return "authenticated"
        elif any(page.query_selector(selector) for selector in unauth_indicators):
            return "unauthenticated"
        else:
            return "unknown"

    def ensure_authenticated(self, page):
        """Ensure user is authenticated"""
        if self.authentication_status == "authenticated":
            return True
        
        print("🔐 Authenticating user...")
        
        # Navigate to login
        page.goto("http://localhost:3002/login")
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        # Fill login form
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        if all([email_input, password_input, submit_btn]):
            email_input.fill(self.user_data['email'])
            password_input.fill(self.user_data['password'])
            submit_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            self.detect_current_state(page)
            return self.authentication_status == "authenticated"
        
        return False

    def navigate_to_feature(self, page, feature_name):
        """Intelligently navigate to a specific feature"""
        print(f"🧭 Navigating to {feature_name}...")
        
        # Ensure authenticated first
        if not self.ensure_authenticated(page):
            return False
        
        # Feature URL mapping
        feature_urls = {
            'dashboard': '/dashboard',
            'assessment': '/assessment',
            'salary_calculator': '/tools/salary-calculator',
            'resume_builder': '/resume-builder',
            'interview_practice': '/tools/interview-practice',
            'career_paths': '/career-paths',
            'tools': '/tools'
        }
        
        if feature_name in feature_urls:
            page.goto(f"http://localhost:3002{feature_urls[feature_name]}")
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            self.detect_current_state(page)
            return feature_name in self.current_state or feature_urls[feature_name] in self.current_url
        
        return False

    def test_dashboard_functionality(self, page):
        """Test dashboard functionality"""
        print("📊 Testing Dashboard...")
        
        if not self.navigate_to_feature(page, 'dashboard'):
            self.add_test_result("dashboard_navigation", "FAIL", "Could not navigate to dashboard")
            return False
        
        # Test dashboard elements
        dashboard_elements = {
            'welcome_message': 'h1, h2, .welcome',
            'navigation_cards': '.card, .dashboard-card, [data-testid*="card"]',
            'quick_actions': 'button, .action-btn, .quick-action',
            'progress_indicators': '.progress, .chart, .metric'
        }
        
        found_elements = 0
        for element_name, selector in dashboard_elements.items():
            elements = page.query_selector_all(selector)
            if elements:
                found_elements += 1
                print(f"   ✅ {element_name}: {len(elements)} found")
            else:
                print(f"   ❌ {element_name}: not found")
        
        success = found_elements >= 2  # At least 2 dashboard elements
        self.add_test_result("dashboard_functionality", "PASS" if success else "FAIL", 
                           f"Found {found_elements}/{len(dashboard_elements)} dashboard elements")
        return success

    def test_assessment_functionality(self, page):
        """Test assessment functionality"""
        print("📝 Testing Assessment...")
        
        if not self.navigate_to_feature(page, 'assessment'):
            self.add_test_result("assessment_navigation", "FAIL", "Could not navigate to assessment")
            return False
        
        # Look for assessment elements
        assessment_elements = [
            'form', 'input', 'select', 'textarea',
            'button:has-text("Start")', 'button:has-text("Begin")',
            '.question', '.assessment-item'
        ]
        
        found_assessment = any(page.query_selector(selector) for selector in assessment_elements)
        
        if found_assessment:
            print("   ✅ Assessment interface found")
            
            # Try to interact with assessment
            start_btn = page.query_selector('button:has-text("Start"), button:has-text("Begin"), button[type="submit"]')
            if start_btn:
                start_btn.click()
                page.wait_for_timeout(2000)
                print("   ✅ Assessment started")
                
                # Look for questions
                questions = page.query_selector_all('.question, input[type="radio"], select')
                if questions:
                    print(f"   ✅ Found {len(questions)} assessment elements")
                    self.add_test_result("assessment_functionality", "PASS", f"Assessment working with {len(questions)} elements")
                    return True
            
            self.add_test_result("assessment_functionality", "PARTIAL", "Assessment interface found but couldn't start")
            return True
        else:
            print("   ❌ No assessment interface found")
            self.add_test_result("assessment_functionality", "FAIL", "No assessment interface found")
            return False

    def test_salary_calculator_functionality(self, page):
        """Test salary calculator functionality"""
        print("💰 Testing Salary Calculator...")
        
        if not self.navigate_to_feature(page, 'salary_calculator'):
            self.add_test_result("salary_calculator_navigation", "FAIL", "Could not navigate to salary calculator")
            return False
        
        # Look for salary calculator elements
        calculator_elements = {
            'job_title_input': 'input[name*="title"], input[placeholder*="title"], input[id*="title"]',
            'location_input': 'input[name*="location"], input[placeholder*="location"], input[id*="location"]',
            'experience_input': 'input[name*="experience"], select[name*="experience"], input[id*="experience"]',
            'calculate_button': 'button:has-text("Calculate"), button[type="submit"]'
        }
        
        filled_fields = 0
        for field_name, selector in calculator_elements.items():
            field = page.query_selector(selector)
            if field:
                try:
                    if field_name == 'job_title_input':
                        field.fill(self.salary_data['job_title'])
                    elif field_name == 'location_input':
                        field.fill(self.salary_data['location'])
                    elif field_name == 'experience_input':
                        if field.tag_name.lower() == 'select':
                            field.select_option(self.salary_data['experience_years'])
                        else:
                            field.fill(self.salary_data['experience_years'])
                    elif field_name == 'calculate_button':
                        field.click()
                        page.wait_for_timeout(3000)
                    
                    filled_fields += 1
                    print(f"   ✅ {field_name}: success")
                except Exception as e:
                    print(f"   ❌ {field_name}: failed - {e}")
            else:
                print(f"   ❌ {field_name}: not found")
        
        # Check for results
        result_indicators = [
            '.salary-result', '.result', '.calculation-result',
            'text="$"', '[data-testid*="result"]'
        ]
        
        results_found = any(page.query_selector(selector) for selector in result_indicators)
        
        success = filled_fields >= 3 or results_found
        status = "PASS" if success else "FAIL"
        details = f"Filled {filled_fields}/4 fields, Results: {'Yes' if results_found else 'No'}"
        
        self.add_test_result("salary_calculator_functionality", status, details)
        return success

    def test_resume_builder_functionality(self, page):
        """Test resume builder functionality"""
        print("📄 Testing Resume Builder...")
        
        if not self.navigate_to_feature(page, 'resume_builder'):
            self.add_test_result("resume_builder_navigation", "FAIL", "Could not navigate to resume builder")
            return False
        
        # Click create if available
        create_btn = page.query_selector('button:has-text("Create"), button:has-text("New")')
        if create_btn:
            create_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(2)
        
        # Test form filling
        filled_fields = 0
        for field_name, field_value in self.resume_data.items():
            selectors = [
                f'input[name="{field_name}"]',
                f'input[id="{field_name}"]',
                f'input[placeholder*="{field_name}"]'
            ]
            
            for selector in selectors:
                field = page.query_selector(selector)
                if field:
                    try:
                        field.fill(field_value)
                        filled_fields += 1
                        print(f"   ✅ {field_name}: filled")
                        break
                    except:
                        continue
        
        # Test save
        save_btn = page.query_selector('button:has-text("Save")')
        save_success = False
        if save_btn:
            save_btn.click()
            page.wait_for_timeout(2000)
            save_success = True
            print("   ✅ Save button clicked")
        
        success = filled_fields >= 2 and save_success
        self.add_test_result("resume_builder_functionality", "PASS" if success else "FAIL",
                           f"Filled {filled_fields}/4 fields, Save: {'Yes' if save_success else 'No'}")
        return success

    def add_test_result(self, test_name, status, details):
        """Add test result to results list"""
        self.test_results.append({
            'test': test_name,
            'status': status,
            'details': details,
            'timestamp': time.time()
        })

    def run_full_flow_test(self):
        """Run complete full flow test"""
        print("🚀 INTELLIGENT FULL FLOW TESTING - FAAFO CAREER PLATFORM")
        print("=" * 70)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            page = browser.new_page()
            
            try:
                # Phase 1: Initial Setup
                print("\n1️⃣ INITIAL SETUP")
                page.goto("http://localhost:3002")
                page.wait_for_load_state('networkidle')
                self.detect_current_state(page)
                
                # Phase 2: Authentication
                print("\n2️⃣ AUTHENTICATION TEST")
                auth_success = self.ensure_authenticated(page)
                self.add_test_result("authentication", "PASS" if auth_success else "FAIL",
                                   "Authentication successful" if auth_success else "Authentication failed")
                
                if not auth_success:
                    print("❌ Cannot proceed without authentication")
                    return
                
                # Phase 3: Dashboard
                print("\n3️⃣ DASHBOARD TEST")
                self.test_dashboard_functionality(page)
                
                # Phase 4: Assessment
                print("\n4️⃣ ASSESSMENT TEST")
                self.test_assessment_functionality(page)
                
                # Phase 5: Salary Calculator
                print("\n5️⃣ SALARY CALCULATOR TEST")
                self.test_salary_calculator_functionality(page)
                
                # Phase 6: Resume Builder
                print("\n6️⃣ RESUME BUILDER TEST")
                self.test_resume_builder_functionality(page)

                # Phase 7: Interview Practice
                print("\n7️⃣ INTERVIEW PRACTICE TEST")
                self.test_interview_practice_functionality(page)

                # Phase 8: Career Paths
                print("\n8️⃣ CAREER PATHS TEST")
                self.test_career_paths_functionality(page)

                # Phase 9: Tools Page
                print("\n9️⃣ TOOLS PAGE TEST")
                self.test_tools_page_functionality(page)

                # Phase 10: Navigation Test
                print("\n🔟 NAVIGATION FLOW TEST")
                self.test_navigation_flow(page)

                # Phase 11: Generate Report
                print("\n📊 GENERATING COMPREHENSIVE REPORT")
                self.generate_full_flow_report()

            except Exception as e:
                print(f"❌ CRITICAL ERROR: {e}")
                self.add_test_result("critical_error", "FAIL", str(e))
            finally:
                print("\n🔍 Keeping browser open for final inspection...")
                time.sleep(15)
                browser.close()

    def test_interview_practice_functionality(self, page):
        """Test interview practice functionality"""
        print("🎤 Testing Interview Practice...")

        if not self.navigate_to_feature(page, 'interview_practice'):
            self.add_test_result("interview_practice_navigation", "FAIL", "Could not navigate to interview practice")
            return False

        # Look for interview practice elements
        practice_elements = [
            'button:has-text("Start")', 'button:has-text("Practice")',
            '.question', '.interview-question',
            'select[name*="type"], select[name*="category"]',
            'button:has-text("Generate")'
        ]

        found_elements = 0
        for selector in practice_elements:
            if page.query_selector(selector):
                found_elements += 1

        # Try to start practice session
        start_btn = page.query_selector('button:has-text("Start"), button:has-text("Practice"), button:has-text("Generate")')
        practice_started = False

        if start_btn:
            try:
                start_btn.click()
                page.wait_for_timeout(3000)

                # Look for questions or practice interface
                questions = page.query_selector_all('.question, .interview-question, textarea, .practice-area')
                if questions:
                    practice_started = True
                    print(f"   ✅ Practice started with {len(questions)} elements")
                else:
                    print("   ⚠️ Practice button clicked but no questions found")
            except Exception as e:
                print(f"   ❌ Error starting practice: {e}")

        success = found_elements >= 2 or practice_started
        self.add_test_result("interview_practice_functionality", "PASS" if success else "FAIL",
                           f"Found {found_elements} elements, Practice started: {practice_started}")
        return success

    def test_career_paths_functionality(self, page):
        """Test career paths functionality"""
        print("🛤️ Testing Career Paths...")

        if not self.navigate_to_feature(page, 'career_paths'):
            self.add_test_result("career_paths_navigation", "FAIL", "Could not navigate to career paths")
            return False

        # Look for career path elements
        career_elements = {
            'path_cards': '.career-path, .path-card, .card',
            'search_filter': 'input[type="search"], input[placeholder*="search"]',
            'category_filter': 'select, .filter, .category',
            'path_details': '.description, .requirements, .skills'
        }

        found_elements = 0
        for element_name, selector in career_elements.items():
            elements = page.query_selector_all(selector)
            if elements:
                found_elements += 1
                print(f"   ✅ {element_name}: {len(elements)} found")
            else:
                print(f"   ❌ {element_name}: not found")

        # Try to interact with a career path
        path_card = page.query_selector('.career-path, .path-card, .card')
        interaction_success = False

        if path_card:
            try:
                path_card.click()
                page.wait_for_timeout(2000)

                # Check if details loaded
                details = page.query_selector('.description, .requirements, .details')
                if details:
                    interaction_success = True
                    print("   ✅ Career path interaction successful")
            except Exception as e:
                print(f"   ❌ Career path interaction failed: {e}")

        success = found_elements >= 2
        self.add_test_result("career_paths_functionality", "PASS" if success else "FAIL",
                           f"Found {found_elements}/4 element types, Interaction: {interaction_success}")
        return success

    def test_tools_page_functionality(self, page):
        """Test tools page functionality"""
        print("🛠️ Testing Tools Page...")

        if not self.navigate_to_feature(page, 'tools'):
            self.add_test_result("tools_page_navigation", "FAIL", "Could not navigate to tools page")
            return False

        # Look for tool links/cards
        tool_elements = [
            'a[href*="salary-calculator"]',
            'a[href*="interview-practice"]',
            'a[href*="resume-builder"]',
            '.tool-card', '.tool-link',
            'button:has-text("Calculator")',
            'button:has-text("Practice")',
            'button:has-text("Resume")'
        ]

        found_tools = 0
        for selector in tool_elements:
            if page.query_selector(selector):
                found_tools += 1

        # Try to click on a tool
        tool_link = page.query_selector('a[href*="salary-calculator"], a[href*="interview-practice"]')
        tool_navigation = False

        if tool_link:
            try:
                original_url = page.url
                tool_link.click()
                page.wait_for_timeout(2000)

                if page.url != original_url:
                    tool_navigation = True
                    print("   ✅ Tool navigation successful")

                    # Navigate back to tools
                    page.goto("http://localhost:3002/tools")
                    page.wait_for_timeout(1000)
            except Exception as e:
                print(f"   ❌ Tool navigation failed: {e}")

        success = found_tools >= 2
        self.add_test_result("tools_page_functionality", "PASS" if success else "FAIL",
                           f"Found {found_tools} tool elements, Navigation: {tool_navigation}")
        return success

    def test_navigation_flow(self, page):
        """Test navigation flow between different sections"""
        print("🧭 Testing Navigation Flow...")

        navigation_tests = [
            ('dashboard', '/dashboard'),
            ('tools', '/tools'),
            ('career_paths', '/career-paths'),
            ('resume_builder', '/resume-builder')
        ]

        successful_navigations = 0

        for feature_name, expected_path in navigation_tests:
            try:
                if self.navigate_to_feature(page, feature_name):
                    if expected_path in page.url:
                        successful_navigations += 1
                        print(f"   ✅ {feature_name}: navigation successful")
                    else:
                        print(f"   ❌ {feature_name}: wrong URL - {page.url}")
                else:
                    print(f"   ❌ {feature_name}: navigation failed")

                time.sleep(1)  # Brief pause between navigations
            except Exception as e:
                print(f"   ❌ {feature_name}: error - {e}")

        success = successful_navigations >= len(navigation_tests) * 0.75  # 75% success rate
        self.add_test_result("navigation_flow", "PASS" if success else "FAIL",
                           f"Successfully navigated to {successful_navigations}/{len(navigation_tests)} sections")
        return success

    def generate_full_flow_report(self):
        """Generate comprehensive full flow test report"""
        print("\n" + "=" * 70)
        print("📊 COMPREHENSIVE FULL FLOW TEST REPORT")
        print("=" * 70)

        # Calculate overall statistics
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        partial_tests = len([r for r in self.test_results if r['status'] == 'PARTIAL'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"\n📈 OVERALL STATISTICS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ⚠️ Partial: {partial_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📊 Success Rate: {success_rate:.1f}%")

        # Categorize results by feature
        feature_categories = {
            'Authentication': ['authentication'],
            'Dashboard': ['dashboard_navigation', 'dashboard_functionality'],
            'Assessment': ['assessment_navigation', 'assessment_functionality'],
            'Salary Calculator': ['salary_calculator_navigation', 'salary_calculator_functionality'],
            'Resume Builder': ['resume_builder_navigation', 'resume_builder_functionality'],
            'Interview Practice': ['interview_practice_navigation', 'interview_practice_functionality'],
            'Career Paths': ['career_paths_navigation', 'career_paths_functionality'],
            'Tools Page': ['tools_page_navigation', 'tools_page_functionality'],
            'Navigation': ['navigation_flow']
        }

        print(f"\n🎯 FEATURE-BY-FEATURE RESULTS:")
        for category, test_names in feature_categories.items():
            category_results = [r for r in self.test_results if r['test'] in test_names]
            if category_results:
                category_passed = len([r for r in category_results if r['status'] == 'PASS'])
                category_total = len(category_results)
                category_rate = (category_passed / category_total * 100) if category_total > 0 else 0

                status_icon = "✅" if category_rate >= 80 else "⚠️" if category_rate >= 50 else "❌"
                print(f"   {status_icon} {category}: {category_passed}/{category_total} ({category_rate:.1f}%)")

        print(f"\n📋 DETAILED TEST RESULTS:")
        for result in self.test_results:
            status_icon = {"PASS": "✅", "PARTIAL": "⚠️", "FAIL": "❌"}.get(result['status'], "❓")
            print(f"   {status_icon} {result['test']}: {result['details']}")

        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if success_rate >= 90:
            print("🎉 EXCELLENT: FAAFO Career Platform is highly functional across all features!")
        elif success_rate >= 75:
            print("✅ VERY GOOD: Most features working well with minor issues")
        elif success_rate >= 60:
            print("👍 GOOD: Core functionality working, some features need attention")
        elif success_rate >= 40:
            print("⚠️ FAIR: Basic functionality present but significant improvements needed")
        else:
            print("❌ POOR: Major functionality issues detected")

        print(f"\nFinal State: {self.current_state}")
        print(f"Authentication Status: {self.authentication_status}")
        print(f"Current URL: {self.current_url}")

if __name__ == "__main__":
    tester = IntelligentFullFlowTester()
    tester.run_full_flow_test()
