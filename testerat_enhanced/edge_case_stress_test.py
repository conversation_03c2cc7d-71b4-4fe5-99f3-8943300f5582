#!/usr/bin/env python3
"""
Edge Case & Stress Testing
Tests unusual scenarios and edge cases for dashboard and CSRF fixes
"""

from playwright.sync_api import sync_playwright
import time

def test_login(page):
    """Helper function to login"""
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        return True
    return False

def test_edge_cases(page):
    """Test various edge cases"""
    print("\n🔍 EDGE CASE TESTING")
    print("=" * 40)
    
    results = []
    
    # Edge Case 1: Rapid tab switching
    print("   🎯 Testing rapid tab switching...")
    try:
        page.goto("http://localhost:3001/dashboard")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        tabs = ['Overview', 'Progress', 'Goals', 'Achievements', 'Analytics']
        
        # Rapidly switch between tabs
        for i in range(3):  # Do 3 cycles
            for tab_name in tabs:
                tab_button = page.query_selector(f'button:has-text("{tab_name}")')
                if tab_button:
                    tab_button.click()
                    page.wait_for_timeout(200)  # Very short wait
        
        # Check if final tab is still working
        analytics_tab = page.query_selector('button:has-text("Analytics")')
        if analytics_tab:
            analytics_tab.click()
            page.wait_for_timeout(1000)
            
            active_content = page.query_selector('[data-state="active"]')
            if active_content:
                print("   ✅ Rapid tab switching handled correctly")
                results.append(('Rapid Tab Switching', True))
            else:
                print("   ❌ Rapid tab switching broke functionality")
                results.append(('Rapid Tab Switching', False))
        else:
            print("   ❌ Tab not found after rapid switching")
            results.append(('Rapid Tab Switching', False))
    except Exception as e:
        print(f"   ❌ Rapid tab switching test failed: {e}")
        results.append(('Rapid Tab Switching', False))
    
    # Edge Case 2: Invalid URL parameters
    print("   🎯 Testing invalid URL parameters...")
    try:
        invalid_urls = [
            "http://localhost:3001/dashboard?tab=invalid",
            "http://localhost:3001/dashboard?tab=",
            "http://localhost:3001/dashboard?tab=123",
            "http://localhost:3001/dashboard?tab=overview&extra=param"
        ]
        
        all_handled = True
        for url in invalid_urls:
            page.goto(url)
            page.wait_for_load_state('networkidle')
            time.sleep(1)
            
            # Should default to overview or handle gracefully
            active_content = page.query_selector('[data-state="active"]')
            if not active_content:
                all_handled = False
                break
        
        if all_handled:
            print("   ✅ Invalid URL parameters handled gracefully")
            results.append(('Invalid URL Parameters', True))
        else:
            print("   ❌ Invalid URL parameters not handled properly")
            results.append(('Invalid URL Parameters', False))
    except Exception as e:
        print(f"   ❌ Invalid URL parameter test failed: {e}")
        results.append(('Invalid URL Parameters', False))
    
    # Edge Case 3: Browser back/forward navigation
    print("   🎯 Testing browser navigation...")
    try:
        page.goto("http://localhost:3001/dashboard?tab=overview")
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        page.goto("http://localhost:3001/dashboard?tab=analytics")
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        # Go back
        page.go_back()
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        # Check if overview is active
        current_url = page.url
        if 'tab=overview' in current_url:
            print("   ✅ Browser back navigation working")
            results.append(('Browser Navigation', True))
        else:
            print("   ❌ Browser back navigation not working")
            results.append(('Browser Navigation', False))
    except Exception as e:
        print(f"   ❌ Browser navigation test failed: {e}")
        results.append(('Browser Navigation', False))
    
    # Edge Case 4: Multiple window/tab handling
    print("   🎯 Testing multiple browser tabs...")
    try:
        # Open new tab
        new_page = page.context.new_page()
        new_page.goto("http://localhost:3001/dashboard?tab=goals")
        new_page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        # Switch back to original tab
        page.bring_to_front()
        page.goto("http://localhost:3001/dashboard?tab=progress")
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        # Check both tabs
        original_url = page.url
        new_url = new_page.url
        
        if 'tab=progress' in original_url and 'tab=goals' in new_url:
            print("   ✅ Multiple browser tabs handled correctly")
            results.append(('Multiple Tabs', True))
        else:
            print("   ❌ Multiple browser tabs not handled properly")
            results.append(('Multiple Tabs', False))
        
        new_page.close()
    except Exception as e:
        print(f"   ❌ Multiple tabs test failed: {e}")
        results.append(('Multiple Tabs', False))
    
    return results

def test_csrf_stress(page):
    """Stress test CSRF functionality"""
    print("\n💰 CSRF STRESS TESTING")
    print("=" * 40)
    
    results = []
    
    # Stress Test 1: Rapid form interactions
    print("   🎯 Testing rapid form interactions...")
    try:
        page.goto("http://localhost:3001/tools/salary-calculator")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Rapidly interact with dropdowns
        for i in range(5):
            career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
            if career_trigger:
                career_trigger.click()
                page.wait_for_timeout(300)
                
                # Select random option
                options = page.query_selector_all('[role="option"]')
                if options:
                    options[i % len(options)].click()
                    page.wait_for_timeout(200)
        
        # Check if form is still functional
        career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
        if career_trigger:
            career_trigger.click()
            page.wait_for_timeout(500)
            
            options = page.query_selector_all('[role="option"]')
            if len(options) > 0:
                print("   ✅ Rapid form interactions handled correctly")
                results.append(('Rapid Form Interactions', True))
            else:
                print("   ❌ Rapid form interactions broke functionality")
                results.append(('Rapid Form Interactions', False))
        else:
            print("   ❌ Form elements not found after stress test")
            results.append(('Rapid Form Interactions', False))
    except Exception as e:
        print(f"   ❌ Rapid form interaction test failed: {e}")
        results.append(('Rapid Form Interactions', False))
    
    # Stress Test 2: Page reload during form interaction
    print("   🎯 Testing page reload during form interaction...")
    try:
        page.goto("http://localhost:3001/tools/salary-calculator")
        page.wait_for_load_state('networkidle')
        time.sleep(1)
        
        # Start filling form
        career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
        if career_trigger:
            career_trigger.click()
            page.wait_for_timeout(500)
            
            # Reload page while dropdown is open
            page.reload()
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            # Check if form is still functional
            career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
            if career_trigger:
                career_trigger.click()
                page.wait_for_timeout(500)
                
                options = page.query_selector_all('[role="option"]')
                if len(options) > 0:
                    print("   ✅ Page reload during interaction handled correctly")
                    results.append(('Page Reload Stress', True))
                else:
                    print("   ❌ Page reload broke form functionality")
                    results.append(('Page Reload Stress', False))
            else:
                print("   ❌ Form not found after page reload")
                results.append(('Page Reload Stress', False))
        else:
            print("   ❌ Initial form elements not found")
            results.append(('Page Reload Stress', False))
    except Exception as e:
        print(f"   ❌ Page reload stress test failed: {e}")
        results.append(('Page Reload Stress', False))
    
    return results

def test_accessibility_edge_cases(page):
    """Test accessibility and keyboard navigation edge cases"""
    print("\n♿ ACCESSIBILITY EDGE CASES")
    print("=" * 40)
    
    results = []
    
    # Test keyboard navigation
    print("   🎯 Testing keyboard navigation...")
    try:
        page.goto("http://localhost:3001/dashboard")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Use Tab key to navigate to tabs
        page.keyboard.press('Tab')
        page.wait_for_timeout(200)
        
        # Use arrow keys to navigate tabs
        page.keyboard.press('ArrowRight')
        page.wait_for_timeout(500)
        page.keyboard.press('ArrowRight')
        page.wait_for_timeout(500)
        
        # Press Enter to activate tab
        page.keyboard.press('Enter')
        page.wait_for_timeout(1000)
        
        # Check if tab changed
        active_content = page.query_selector('[data-state="active"]')
        if active_content:
            print("   ✅ Keyboard navigation working")
            results.append(('Keyboard Navigation', True))
        else:
            print("   ❌ Keyboard navigation not working")
            results.append(('Keyboard Navigation', False))
    except Exception as e:
        print(f"   ❌ Keyboard navigation test failed: {e}")
        results.append(('Keyboard Navigation', False))
    
    return results

def main():
    print("🧪 EDGE CASE & STRESS TESTING")
    print("=" * 50)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        all_results = []
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run stress tests
            edge_results = test_edge_cases(page)
            csrf_results = test_csrf_stress(page)
            accessibility_results = test_accessibility_edge_cases(page)
            
            all_results.extend(edge_results)
            all_results.extend(csrf_results)
            all_results.extend(accessibility_results)
            
            # Generate report
            print("\n" + "=" * 50)
            print("📊 EDGE CASE TEST RESULTS")
            print("=" * 50)
            
            passed = sum(1 for _, success in all_results if success)
            total = len(all_results)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 STRESS TEST SUMMARY:")
            print(f"   Total Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {total - passed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for test_name, success in all_results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {status}: {test_name}")
            
            if success_rate >= 90:
                print("\n🎉 EXCELLENT: System handles edge cases very well!")
            elif success_rate >= 80:
                print("\n✅ GOOD: Most edge cases handled properly")
            elif success_rate >= 70:
                print("\n⚠️ FAIR: Some edge cases need attention")
            else:
                print("\n❌ POOR: Significant edge case issues found")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 10 seconds for inspection...")
            time.sleep(10)
            browser.close()

if __name__ == "__main__":
    main()
