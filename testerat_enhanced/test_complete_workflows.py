#!/usr/bin/env python3
"""
Complete Workflow Testing
Tests end-to-end workflows for major features
"""

from playwright.sync_api import sync_playwright
import time

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        return True
    return False

def test_interview_practice_workflow(page):
    """Test complete interview practice workflow"""
    print("\n🎤 TESTING COMPLETE INTERVIEW PRACTICE WORKFLOW")
    print("=" * 60)
    
    results = []
    
    # Step 1: Navigate to interview practice
    page.goto("http://localhost:3001/interview-practice")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Step 2: Start new practice session
    print("   🎯 Starting new practice session...")
    try:
        start_button = page.query_selector('button:has-text("Start New Practice")')
        if start_button:
            start_button.click()
            page.wait_for_timeout(3000)
            
            # Check if configuration wizard opened
            cancel_button = page.query_selector('button:has-text("Cancel")')
            next_button = page.query_selector('button:has-text("Next")')
            
            if cancel_button and next_button:
                print("   ✅ Configuration wizard opened successfully")
                results.append(('Interview Configuration Wizard', True))
                
                # Step 3: Try to proceed through configuration
                print("   🎯 Proceeding through configuration...")
                next_button.click()
                page.wait_for_timeout(2000)
                
                # Look for session interface or further configuration
                session_indicators = page.query_selector_all('button, input, select')
                if len(session_indicators) > 0:
                    print("   ✅ Configuration proceeding successfully")
                    results.append(('Interview Configuration Progress', True))
                else:
                    print("   ❌ Configuration not progressing")
                    results.append(('Interview Configuration Progress', False))
            else:
                print("   ❌ Configuration wizard not opened")
                results.append(('Interview Configuration Wizard', False))
                results.append(('Interview Configuration Progress', False))
        else:
            print("   ❌ Start New Practice button not found")
            results.append(('Interview Configuration Wizard', False))
            results.append(('Interview Configuration Progress', False))
    except Exception as e:
        print(f"   ❌ Interview practice workflow error: {e}")
        results.append(('Interview Configuration Wizard', False))
        results.append(('Interview Configuration Progress', False))
    
    return results

def test_resume_builder_workflow(page):
    """Test complete resume builder workflow"""
    print("\n📄 TESTING COMPLETE RESUME BUILDER WORKFLOW")
    print("=" * 60)
    
    results = []
    
    # Step 1: Navigate to resume builder
    page.goto("http://localhost:3001/resume-builder")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Step 2: Create new resume
    print("   🎯 Creating new resume...")
    try:
        create_button = page.query_selector('button:has-text("Create New Resume")')
        if create_button:
            create_button.click()
            page.wait_for_timeout(3000)
            
            # Check for form inputs
            inputs = page.query_selector_all('input, textarea, select')
            if len(inputs) >= 5:  # Should have multiple form fields
                print(f"   ✅ Resume form opened with {len(inputs)} inputs")
                results.append(('Resume Form Creation', True))
                
                # Step 3: Fill out basic information
                print("   🎯 Filling out basic information...")
                
                # Try to fill name field
                name_input = page.query_selector('input[placeholder*="name"], input[name*="name"]')
                if name_input:
                    name_input.fill("John Doe")
                    print("   ✅ Name field filled")
                
                # Try to fill email field
                email_input = page.query_selector('input[placeholder*="email"], input[name*="email"], input[type="email"]')
                if email_input:
                    email_input.fill("<EMAIL>")
                    print("   ✅ Email field filled")
                
                # Try to fill phone field
                phone_input = page.query_selector('input[placeholder*="phone"], input[name*="phone"], input[type="tel"]')
                if phone_input:
                    phone_input.fill("(*************")
                    print("   ✅ Phone field filled")
                
                results.append(('Resume Form Filling', True))
                
                # Step 4: Look for save/preview functionality
                print("   🎯 Looking for save/preview functionality...")
                save_button = page.query_selector('button:has-text("Save"), button:has-text("Preview")')
                if save_button:
                    print("   ✅ Save/Preview functionality available")
                    results.append(('Resume Save/Preview', True))
                else:
                    print("   ❌ Save/Preview functionality not found")
                    results.append(('Resume Save/Preview', False))
                    
            else:
                print(f"   ❌ Resume form incomplete - only {len(inputs)} inputs found")
                results.append(('Resume Form Creation', False))
                results.append(('Resume Form Filling', False))
                results.append(('Resume Save/Preview', False))
        else:
            print("   ❌ Create New Resume button not found")
            results.append(('Resume Form Creation', False))
            results.append(('Resume Form Filling', False))
            results.append(('Resume Save/Preview', False))
    except Exception as e:
        print(f"   ❌ Resume builder workflow error: {e}")
        results.append(('Resume Form Creation', False))
        results.append(('Resume Form Filling', False))
        results.append(('Resume Save/Preview', False))
    
    return results

def test_career_paths_workflow(page):
    """Test complete career paths workflow"""
    print("\n🛤️ TESTING COMPLETE CAREER PATHS WORKFLOW")
    print("=" * 60)
    
    results = []
    
    # Step 1: Navigate to career paths
    page.goto("http://localhost:3001/career-paths")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Step 2: Browse career paths
    print("   🎯 Browsing career paths...")
    try:
        # Look for career path cards
        career_cards = page.query_selector_all('div[class*="card"], .card, article')
        
        if len(career_cards) >= 3:
            print(f"   ✅ Found {len(career_cards)} career path cards")
            results.append(('Career Path Browsing', True))
            
            # Step 3: Click on a career path
            print("   🎯 Clicking on first career path...")
            career_cards[0].click()
            page.wait_for_timeout(3000)
            
            # Check if we navigated to a detail page
            current_url = page.url
            if '/career-paths/' in current_url and current_url != 'http://localhost:3001/career-paths':
                print("   ✅ Successfully navigated to career path detail")
                results.append(('Career Path Detail Navigation', True))
                
                # Step 4: Look for detailed information
                print("   🎯 Checking career path details...")
                page_text = page.query_selector('body').text_content()
                
                detail_indicators = ['salary', 'description', 'requirements', 'skills', 'experience']
                found_details = sum(1 for indicator in detail_indicators if indicator in page_text.lower())
                
                if found_details >= 2:
                    print(f"   ✅ Found {found_details} detail sections")
                    results.append(('Career Path Details', True))
                else:
                    print(f"   ❌ Only found {found_details} detail sections")
                    results.append(('Career Path Details', False))
            else:
                print("   ❌ Did not navigate to detail page")
                results.append(('Career Path Detail Navigation', False))
                results.append(('Career Path Details', False))
        else:
            print(f"   ❌ Only found {len(career_cards)} career path cards")
            results.append(('Career Path Browsing', False))
            results.append(('Career Path Detail Navigation', False))
            results.append(('Career Path Details', False))
    except Exception as e:
        print(f"   ❌ Career paths workflow error: {e}")
        results.append(('Career Path Browsing', False))
        results.append(('Career Path Detail Navigation', False))
        results.append(('Career Path Details', False))
    
    return results

def main():
    print("🧪 COMPLETE WORKFLOW TESTING")
    print("=" * 70)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        all_results = []
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run complete workflow tests
            interview_results = test_interview_practice_workflow(page)
            resume_results = test_resume_builder_workflow(page)
            career_results = test_career_paths_workflow(page)
            
            all_results.extend(interview_results)
            all_results.extend(resume_results)
            all_results.extend(career_results)
            
            # Generate report
            print("\n" + "=" * 70)
            print("📊 COMPLETE WORKFLOW TEST RESULTS")
            print("=" * 70)
            
            passed = sum(1 for _, success in all_results if success)
            total = len(all_results)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 OVERALL SUMMARY:")
            print(f"   Total Workflow Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {total - passed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for test_name, success in all_results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {status}: {test_name}")
            
            print(f"\n🎯 FINAL ASSESSMENT:")
            if success_rate >= 90:
                print("🎉 EXCELLENT: All workflows working perfectly!")
            elif success_rate >= 80:
                print("✅ GOOD: Most workflows working, minor issues to address")
            elif success_rate >= 70:
                print("⚠️ FAIR: Core workflows working, some improvements needed")
            else:
                print("❌ POOR: Significant workflow issues found")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 20 seconds for inspection...")
            time.sleep(20)
            browser.close()

if __name__ == "__main__":
    main()
