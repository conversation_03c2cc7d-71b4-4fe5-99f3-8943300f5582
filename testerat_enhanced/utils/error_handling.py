"""
Enhanced Error Handling Utilities for testerat Enhanced

Provides standardized error handling patterns to ensure consistent
error management across all testing engines and components.
"""

import logging
import traceback
from typing import Optional, Any, Callable, Dict, List
from functools import wraps
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels for consistent error classification"""
    CRITICAL = "CRITICAL"  # System-breaking errors
    HIGH = "HIGH"          # Feature-breaking errors
    MEDIUM = "MEDIUM"      # Degraded functionality
    LOW = "LOW"            # Minor issues
    DEBUG = "DEBUG"        # Debug information


class TestingError(Exception):
    """Base exception for testerat Enhanced testing errors"""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.severity = severity
        self.context = context or {}
        self.timestamp = None


class AuthenticationError(TestingError):
    """Authentication-related testing errors"""
    pass


class WorkflowError(TestingError):
    """Workflow testing errors"""
    pass


class APIError(TestingError):
    """API testing errors"""
    pass


class FrameworkDetectionError(TestingError):
    """Framework detection errors"""
    pass


class ErrorHandler:
    """
    Centralized error handling for testerat Enhanced
    
    Provides consistent error logging, classification, and recovery patterns
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_counts = {severity: 0 for severity in ErrorSeverity}
        self.error_history: List[Dict[str, Any]] = []
    
    def handle_error(self, error: Exception, context: str = "", 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    suppress: bool = False) -> Optional[Exception]:
        """
        Handle an error with consistent logging and classification
        
        Args:
            error: The exception that occurred
            context: Additional context about where the error occurred
            severity: The severity level of the error
            suppress: Whether to suppress the error (return None) or re-raise
            
        Returns:
            None if suppressed, otherwise re-raises the exception
        """
        # Record error statistics
        self.error_counts[severity] += 1
        
        # Create error record
        error_record = {
            'type': type(error).__name__,
            'message': str(error),
            'context': context,
            'severity': severity.value,
            'traceback': traceback.format_exc() if severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH] else None
        }
        
        self.error_history.append(error_record)
        
        # Log based on severity
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"CRITICAL ERROR in {context}: {error}")
            self.logger.critical(f"Traceback: {traceback.format_exc()}")
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(f"HIGH SEVERITY ERROR in {context}: {error}")
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"MEDIUM ERROR in {context}: {error}")
        elif severity == ErrorSeverity.LOW:
            self.logger.info(f"LOW SEVERITY ERROR in {context}: {error}")
        else:  # DEBUG
            self.logger.debug(f"DEBUG ERROR in {context}: {error}")
        
        # Handle suppression
        if suppress:
            return None
        else:
            raise error
    
    def safe_execute(self, func: Callable, *args, context: str = "", 
                    default_return: Any = None, severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    **kwargs) -> Any:
        """
        Safely execute a function with error handling
        
        Args:
            func: Function to execute
            *args: Arguments for the function
            context: Context description for error logging
            default_return: Value to return if function fails
            severity: Error severity level
            **kwargs: Keyword arguments for the function
            
        Returns:
            Function result or default_return if error occurs
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_error(e, context=context, severity=severity, suppress=True)
            return default_return
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors encountered"""
        return {
            'total_errors': sum(self.error_counts.values()),
            'by_severity': dict(self.error_counts),
            'recent_errors': self.error_history[-10:] if self.error_history else [],
            'critical_errors': [e for e in self.error_history if e['severity'] == ErrorSeverity.CRITICAL.value]
        }


def error_handler_decorator(context: str = "", severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                          suppress: bool = False, default_return: Any = None):
    """
    Decorator for consistent error handling in methods
    
    Args:
        context: Context description for error logging
        severity: Error severity level
        suppress: Whether to suppress errors
        default_return: Value to return if error occurs and suppress=True
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get error handler from self if available
            error_handler = getattr(self, 'error_handler', None)
            if not error_handler:
                # Create a basic error handler if none exists
                logger = getattr(self, 'logger', logging.getLogger(__name__))
                error_handler = ErrorHandler(logger)
            
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                if suppress:
                    error_handler.handle_error(e, context=context or func.__name__, 
                                             severity=severity, suppress=True)
                    return default_return
                else:
                    error_handler.handle_error(e, context=context or func.__name__, 
                                             severity=severity, suppress=False)
        
        return wrapper
    return decorator


def safe_dom_operation(func: Callable, page, selector: str = "", 
                      context: str = "", logger: Optional[logging.Logger] = None) -> Any:
    """
    Safely perform DOM operations with consistent error handling
    
    Args:
        func: Function to execute (usually a page method)
        page: Playwright page object
        selector: CSS selector being operated on
        context: Context description
        logger: Logger instance
        
    Returns:
        Function result or None if error occurs
    """
    if not logger:
        logger = logging.getLogger(__name__)
    
    try:
        return func()
    except Exception as e:
        error_context = f"{context} (selector: {selector})" if selector else context
        logger.debug(f"DOM operation failed in {error_context}: {e}")
        return None


def safe_javascript_execution(page, script: str, context: str = "", 
                            logger: Optional[logging.Logger] = None) -> Any:
    """
    Safely execute JavaScript with error handling
    
    Args:
        page: Playwright page object
        script: JavaScript code to execute
        context: Context description
        logger: Logger instance
        
    Returns:
        Script result or None if error occurs
    """
    if not logger:
        logger = logging.getLogger(__name__)
    
    try:
        return page.evaluate(script)
    except Exception as e:
        logger.debug(f"JavaScript execution failed in {context}: {e}")
        return None


# Global error handler instance for framework-wide use
_global_error_handler = None


def get_global_error_handler() -> ErrorHandler:
    """Get or create the global error handler instance"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


def reset_global_error_handler():
    """Reset the global error handler (useful for testing)"""
    global _global_error_handler
    _global_error_handler = None
