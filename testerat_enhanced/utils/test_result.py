"""
Universal Test Result Classes for testerat Enhanced

Standardized test result representation that works across all testing engines.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from enum import Enum
from datetime import datetime
import json


class TestStatus(Enum):
    """Universal test status"""
    PASSED = "PASSED"
    FAILED = "FAILED"
    WARNING = "WARNING"
    ERROR = "ERROR"
    SKIPPED = "SKIPPED"
    CRITICAL = "CRITICAL"


class TestSeverity(Enum):
    """Universal test severity levels"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


class TestCategory(Enum):
    """Universal test categories"""
    AUTHENTICATION = "authentication"
    WORKFLOW = "workflow"
    API = "api"
    SECURITY = "security"
    ACCESSIBILITY = "accessibility"
    PERFORMANCE = "performance"
    UI_UX = "ui_ux"
    COMPATIBILITY = "compatibility"


@dataclass
class TestResult:
    """
    Universal test result representation
    
    Works across all testing engines and web application frameworks.
    """
    test_name: str
    status: str  # TestStatus enum value as string
    details: str
    severity: str = "LOW"  # TestSeverity enum value as string
    recommendations: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    screenshot_path: Optional[str] = None
    category: str = "general"  # TestCategory enum value as string
    
    # Universal metadata
    timestamp: datetime = field(default_factory=datetime.now)
    framework_detected: Optional[str] = None
    auth_method_detected: Optional[str] = None
    
    # Technical details for debugging
    error_details: Optional[Dict[str, Any]] = None
    code_location: Optional[str] = None  # File and line number if applicable
    fix_examples: List[str] = field(default_factory=list)
    
    # Performance metrics
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    
    # Security findings
    security_issues: List[str] = field(default_factory=list)
    vulnerability_type: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'test_name': self.test_name,
            'status': self.status,
            'details': self.details,
            'severity': self.severity,
            'recommendations': self.recommendations,
            'execution_time': self.execution_time,
            'screenshot_path': self.screenshot_path,
            'category': self.category,
            'timestamp': self.timestamp.isoformat(),
            'framework_detected': self.framework_detected,
            'auth_method_detected': self.auth_method_detected,
            'error_details': self.error_details,
            'code_location': self.code_location,
            'fix_examples': self.fix_examples,
            'performance_metrics': self.performance_metrics,
            'security_issues': self.security_issues,
            'vulnerability_type': self.vulnerability_type
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2, default=str)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestResult':
        """Create TestResult from dictionary"""
        # Handle timestamp conversion
        if isinstance(data.get('timestamp'), str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return cls(**data)
    
    def is_critical(self) -> bool:
        """Check if this is a critical issue"""
        return self.severity == TestSeverity.CRITICAL.value or self.status == TestStatus.CRITICAL.value
    
    def is_failure(self) -> bool:
        """Check if this represents a test failure"""
        return self.status in [TestStatus.FAILED.value, TestStatus.ERROR.value, TestStatus.CRITICAL.value]
    
    def add_recommendation(self, recommendation: str):
        """Add a recommendation"""
        if recommendation not in self.recommendations:
            self.recommendations.append(recommendation)
    
    def add_fix_example(self, fix_example: str):
        """Add a fix example"""
        if fix_example not in self.fix_examples:
            self.fix_examples.append(fix_example)
    
    def add_security_issue(self, security_issue: str):
        """Add a security issue"""
        if security_issue not in self.security_issues:
            self.security_issues.append(security_issue)
    
    def set_code_location(self, file_path: str, line_number: Optional[int] = None):
        """Set code location for debugging"""
        if line_number:
            self.code_location = f"{file_path}:{line_number}"
        else:
            self.code_location = file_path


@dataclass
class TestSuite:
    """Collection of test results with summary statistics"""
    name: str
    results: List[TestResult] = field(default_factory=list)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_execution_time: float = 0.0
    
    def add_result(self, result: TestResult):
        """Add a test result"""
        self.results.append(result)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get test suite summary"""
        total_tests = len(self.results)
        passed = len([r for r in self.results if r.status == TestStatus.PASSED.value])
        failed = len([r for r in self.results if r.is_failure()])
        critical = len([r for r in self.results if r.is_critical()])
        
        return {
            'suite_name': self.name,
            'total_tests': total_tests,
            'passed': passed,
            'failed': failed,
            'critical_issues': critical,
            'success_rate': (passed / total_tests * 100) if total_tests > 0 else 0,
            'total_execution_time': self.total_execution_time,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None
        }
    
    def get_critical_issues(self) -> List[TestResult]:
        """Get all critical issues"""
        return [r for r in self.results if r.is_critical()]
    
    def get_failures(self) -> List[TestResult]:
        """Get all failures"""
        return [r for r in self.results if r.is_failure()]
    
    def get_by_category(self, category: TestCategory) -> List[TestResult]:
        """Get results by category"""
        return [r for r in self.results if r.category == category.value]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'summary': self.get_summary(),
            'results': [r.to_dict() for r in self.results]
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2, default=str)


def create_auth_result(test_name: str, status: TestStatus, details: str, 
                      severity: TestSeverity = TestSeverity.LOW,
                      recommendations: List[str] = None) -> TestResult:
    """Helper function to create authentication test results"""
    return TestResult(
        test_name=test_name,
        status=status.value,
        details=details,
        severity=severity.value,
        recommendations=recommendations or [],
        category=TestCategory.AUTHENTICATION.value
    )


def create_workflow_result(test_name: str, status: TestStatus, details: str,
                          severity: TestSeverity = TestSeverity.LOW,
                          recommendations: List[str] = None) -> TestResult:
    """Helper function to create workflow test results"""
    return TestResult(
        test_name=test_name,
        status=status.value,
        details=details,
        severity=severity.value,
        recommendations=recommendations or [],
        category=TestCategory.WORKFLOW.value
    )


def create_api_result(test_name: str, status: TestStatus, details: str,
                     severity: TestSeverity = TestSeverity.LOW,
                     recommendations: List[str] = None) -> TestResult:
    """Helper function to create API test results"""
    return TestResult(
        test_name=test_name,
        status=status.value,
        details=details,
        severity=severity.value,
        recommendations=recommendations or [],
        category=TestCategory.API.value
    )
