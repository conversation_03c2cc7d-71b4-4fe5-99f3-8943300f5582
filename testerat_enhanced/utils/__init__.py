"""
Enhanced Testerat Utils Module
"""

from .error_handling import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rrorSeverity,
    TestingError,
    AuthenticationError,
    WorkflowError,
    APIError,
    FrameworkDetectionError,
    error_handler_decorator,
    safe_dom_operation,
    safe_javascript_execution
)
from .test_result import (
    TestResult,
    TestSuite,
    TestCategory,
    TestSeverity,
    TestStatus
)

__all__ = [
    'ErrorHandler',
    'ErrorSeverity', 
    'TestingError',
    'AuthenticationError',
    'WorkflowError',
    'APIError',
    'FrameworkDetectionError',
    'error_handler_decorator',
    'safe_dom_operation',
    'safe_javascript_execution',
    'TestResult',
    'TestSuite',
    'TestCategory',
    'TestSeverity',
    'TestStatus'
]
