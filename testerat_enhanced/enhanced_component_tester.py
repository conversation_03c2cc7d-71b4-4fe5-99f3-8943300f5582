#!/usr/bin/env python3
"""
Enhanced Component Tester
Fixes the identified issues by properly interacting with React components
"""

from playwright.sync_api import sync_playwright
import time

class EnhancedComponentTester:
    def __init__(self):
        self.test_results = []
        
    def add_result(self, test_name, status, details):
        """Add test result"""
        self.test_results.append({
            'test': test_name,
            'status': status,
            'details': details
        })

    def test_salary_calculator_enhanced(self, page):
        """Enhanced salary calculator testing with proper React component interaction"""
        print("💰 ENHANCED SALARY CALCULATOR TESTING")
        print("-" * 50)
        
        # Navigate to salary calculator
        page.goto("http://localhost:3002/tools/salary-calculator")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Wait for React components to load
        page.wait_for_selector('[data-testid="career-path-trigger"]', timeout=10000)
        
        try:
            # Test Career Path Selection (React Select component)
            print("1️⃣ Testing Career Path Selection...")
            career_path_trigger = page.query_selector('[data-testid="career-path-trigger"]')
            if career_path_trigger:
                career_path_trigger.click()
                page.wait_for_timeout(1000)
                
                # Look for dropdown options
                software_dev_option = page.query_selector('text="Software Developer"')
                if software_dev_option:
                    software_dev_option.click()
                    page.wait_for_timeout(500)
                    print("   ✅ Career path selected: Software Developer")
                    self.add_result("salary_calc_career_path", "PASS", "Career path selection working")
                else:
                    print("   ❌ Career path options not found")
                    self.add_result("salary_calc_career_path", "FAIL", "Career path options not found")
            else:
                print("   ❌ Career path trigger not found")
                self.add_result("salary_calc_career_path", "FAIL", "Career path trigger not found")
            
            # Test Experience Level Selection
            print("2️⃣ Testing Experience Level Selection...")
            experience_trigger = page.query_selector('[data-testid="experience-trigger"]')
            if experience_trigger:
                experience_trigger.click()
                page.wait_for_timeout(1000)
                
                mid_level_option = page.query_selector('text="Mid Level (3-5 years)"')
                if mid_level_option:
                    mid_level_option.click()
                    page.wait_for_timeout(500)
                    print("   ✅ Experience level selected: Mid Level")
                    self.add_result("salary_calc_experience", "PASS", "Experience level selection working")
                else:
                    print("   ❌ Experience level options not found")
                    self.add_result("salary_calc_experience", "FAIL", "Experience level options not found")
            else:
                print("   ❌ Experience trigger not found")
                self.add_result("salary_calc_experience", "FAIL", "Experience trigger not found")
            
            # Test Location Selection
            print("3️⃣ Testing Location Selection...")
            location_trigger = page.query_selector('[data-testid="location-trigger"]')
            if location_trigger:
                location_trigger.click()
                page.wait_for_timeout(1000)
                
                sf_option = page.query_selector('text="San Francisco, CA"')
                if sf_option:
                    sf_option.click()
                    page.wait_for_timeout(500)
                    print("   ✅ Location selected: San Francisco, CA")
                    self.add_result("salary_calc_location", "PASS", "Location selection working")
                else:
                    print("   ❌ Location options not found")
                    self.add_result("salary_calc_location", "FAIL", "Location options not found")
            else:
                print("   ❌ Location trigger not found")
                self.add_result("salary_calc_location", "FAIL", "Location trigger not found")
            
            # Test Skills Input
            print("4️⃣ Testing Skills Input...")
            skills_input = page.query_selector('input[placeholder*="skill"]')
            if skills_input:
                skills_input.fill("JavaScript")
                page.wait_for_timeout(500)
                
                # Click add skill button
                add_skill_btn = page.query_selector('button:has(svg)')  # Button with Plus icon
                if add_skill_btn:
                    add_skill_btn.click()
                    page.wait_for_timeout(500)
                    
                    # Check if skill was added
                    skill_badge = page.query_selector('text="JavaScript"')
                    if skill_badge:
                        print("   ✅ Skill added: JavaScript")
                        self.add_result("salary_calc_skills", "PASS", "Skills input working")
                    else:
                        print("   ❌ Skill not added")
                        self.add_result("salary_calc_skills", "FAIL", "Skill not added")
                else:
                    print("   ❌ Add skill button not found")
                    self.add_result("salary_calc_skills", "FAIL", "Add skill button not found")
            else:
                print("   ❌ Skills input not found")
                self.add_result("salary_calc_skills", "FAIL", "Skills input not found")
            
            # Test Calculate Button
            print("5️⃣ Testing Calculate Button...")
            calculate_btn = page.query_selector('button:has-text("Calculate Salary")')
            if calculate_btn:
                # Check if button is enabled
                is_disabled = calculate_btn.get_attribute('disabled')
                if not is_disabled:
                    calculate_btn.click()
                    page.wait_for_timeout(5000)  # Wait for calculation
                    
                    # Check for results
                    results = page.query_selector('text="Salary Estimate"')
                    if results:
                        print("   ✅ Calculation successful - results displayed")
                        self.add_result("salary_calc_calculate", "PASS", "Calculation working with results")
                    else:
                        print("   ⚠️ Calculation triggered but no results shown")
                        self.add_result("salary_calc_calculate", "PARTIAL", "Calculation triggered but no results")
                else:
                    print("   ❌ Calculate button is disabled")
                    self.add_result("salary_calc_calculate", "FAIL", "Calculate button disabled")
            else:
                print("   ❌ Calculate button not found")
                self.add_result("salary_calc_calculate", "FAIL", "Calculate button not found")
                
        except Exception as e:
            print(f"   ❌ Error during salary calculator testing: {e}")
            self.add_result("salary_calc_error", "FAIL", f"Error: {str(e)}")

    def test_assessment_enhanced(self, page):
        """Enhanced assessment testing"""
        print("\n📝 ENHANCED ASSESSMENT TESTING")
        print("-" * 50)
        
        # Navigate to assessment
        page.goto("http://localhost:3002/assessment")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        try:
            # Check if assessment form is loaded
            print("1️⃣ Checking assessment form...")
            
            # Look for assessment questions or form elements
            form_elements = page.query_selector_all('form, input, select, textarea, button[type="submit"]')
            if form_elements:
                print(f"   ✅ Found {len(form_elements)} form elements")
                
                # Look for specific assessment elements
                questions = page.query_selector_all('[class*="question"], [data-testid*="question"]')
                if questions:
                    print(f"   ✅ Found {len(questions)} question elements")
                    self.add_result("assessment_questions", "PASS", f"Found {len(questions)} questions")
                else:
                    print("   ⚠️ No question elements found")
                    self.add_result("assessment_questions", "PARTIAL", "Form elements found but no questions")
                
                # Look for submit/next buttons
                action_buttons = page.query_selector_all('button:has-text("Next"), button:has-text("Submit"), button:has-text("Continue")')
                if action_buttons:
                    print(f"   ✅ Found {len(action_buttons)} action buttons")
                    
                    # Try to interact with first button
                    first_button = action_buttons[0]
                    button_text = first_button.text_content()
                    
                    # Check if button is enabled
                    is_disabled = first_button.get_attribute('disabled')
                    if not is_disabled:
                        print(f"   ✅ Action button '{button_text}' is enabled")
                        self.add_result("assessment_interaction", "PASS", f"Action button '{button_text}' available and enabled")
                    else:
                        print(f"   ⚠️ Action button '{button_text}' is disabled")
                        self.add_result("assessment_interaction", "PARTIAL", f"Action button '{button_text}' found but disabled")
                else:
                    print("   ❌ No action buttons found")
                    self.add_result("assessment_interaction", "FAIL", "No action buttons found")
                    
            else:
                print("   ❌ No form elements found")
                self.add_result("assessment_form", "FAIL", "No form elements found")
                
        except Exception as e:
            print(f"   ❌ Error during assessment testing: {e}")
            self.add_result("assessment_error", "FAIL", f"Error: {str(e)}")

    def test_interview_practice_enhanced(self, page):
        """Enhanced interview practice testing"""
        print("\n🎤 ENHANCED INTERVIEW PRACTICE TESTING")
        print("-" * 50)
        
        # Navigate to interview practice
        page.goto("http://localhost:3002/tools/interview-practice")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        try:
            # Check for interview practice interface
            print("1️⃣ Checking interview practice interface...")
            
            # Look for configuration or start elements
            config_elements = page.query_selector_all('button:has-text("Start"), button:has-text("Begin"), button:has-text("Practice")')
            if config_elements:
                print(f"   ✅ Found {len(config_elements)} start/practice buttons")
                
                # Try to start a practice session
                start_button = config_elements[0]
                button_text = start_button.text_content()
                
                start_button.click()
                page.wait_for_timeout(3000)
                
                # Check if practice interface loaded
                practice_elements = page.query_selector_all('.question, textarea, input[type="text"]')
                if practice_elements:
                    print(f"   ✅ Practice session started - found {len(practice_elements)} practice elements")
                    self.add_result("interview_practice_start", "PASS", f"Practice session started with {len(practice_elements)} elements")
                else:
                    print("   ⚠️ Practice button clicked but no practice interface loaded")
                    self.add_result("interview_practice_start", "PARTIAL", "Practice button clicked but no interface")
                    
            else:
                # Look for configuration wizard
                wizard_elements = page.query_selector_all('select, input[type="radio"], .wizard, .configuration')
                if wizard_elements:
                    print(f"   ✅ Found {len(wizard_elements)} configuration elements")
                    self.add_result("interview_practice_config", "PASS", f"Configuration interface found with {len(wizard_elements)} elements")
                else:
                    print("   ❌ No interview practice interface found")
                    self.add_result("interview_practice_interface", "FAIL", "No interview practice interface found")
                    
        except Exception as e:
            print(f"   ❌ Error during interview practice testing: {e}")
            self.add_result("interview_practice_error", "FAIL", f"Error: {str(e)}")

    def test_career_paths_enhanced(self, page):
        """Enhanced career paths testing"""
        print("\n🛤️ ENHANCED CAREER PATHS TESTING")
        print("-" * 50)
        
        # Navigate to career paths
        page.goto("http://localhost:3002/career-paths")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        try:
            # Check for career path content
            print("1️⃣ Checking career paths content...")
            
            # Look for career path cards or content
            path_cards = page.query_selector_all('.career-path, .path-card, [class*="card"]')
            if path_cards:
                print(f"   ✅ Found {len(path_cards)} career path cards")
                
                # Try to interact with first card
                first_card = path_cards[0]
                first_card.click()
                page.wait_for_timeout(2000)
                
                # Check if details loaded
                details = page.query_selector_all('.description, .overview, .pros, .cons')
                if details:
                    print(f"   ✅ Career path interaction successful - found {len(details)} detail elements")
                    self.add_result("career_paths_interaction", "PASS", f"Career path cards working with {len(details)} details")
                else:
                    print("   ⚠️ Career path clicked but no details loaded")
                    self.add_result("career_paths_interaction", "PARTIAL", "Career path cards found but no details")
                    
            else:
                # Check for empty state or loading message
                empty_state = page.query_selector('text="No career paths available"')
                loading_state = page.query_selector('text="Loading"')
                
                if empty_state:
                    print("   ⚠️ Career paths page shows 'No career paths available'")
                    self.add_result("career_paths_content", "PARTIAL", "Page loads but no career paths available")
                elif loading_state:
                    print("   ⚠️ Career paths page stuck in loading state")
                    self.add_result("career_paths_content", "PARTIAL", "Page stuck in loading state")
                else:
                    print("   ❌ No career path content found")
                    self.add_result("career_paths_content", "FAIL", "No career path content found")
                    
        except Exception as e:
            print(f"   ❌ Error during career paths testing: {e}")
            self.add_result("career_paths_error", "FAIL", f"Error: {str(e)}")

    def run_enhanced_tests(self):
        """Run all enhanced tests"""
        print("🔧 ENHANCED COMPONENT TESTING - FIXING IDENTIFIED ISSUES")
        print("=" * 70)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            page = browser.new_page()
            
            try:
                # Ensure authentication first
                page.goto("http://localhost:3002/login")
                page.wait_for_load_state('networkidle')
                
                email_input = page.query_selector("input[id='email']")
                password_input = page.query_selector("input[id='password']")
                submit_btn = page.query_selector("button[type='submit']")
                
                if all([email_input, password_input, submit_btn]):
                    email_input.fill("<EMAIL>")
                    password_input.fill("testpassword")
                    submit_btn.click()
                    page.wait_for_load_state('networkidle')
                    time.sleep(3)
                
                # Run enhanced tests
                self.test_salary_calculator_enhanced(page)
                self.test_assessment_enhanced(page)
                self.test_interview_practice_enhanced(page)
                self.test_career_paths_enhanced(page)
                
                # Generate report
                self.generate_enhanced_report()
                
            except Exception as e:
                print(f"❌ CRITICAL ERROR: {e}")
            finally:
                print("\n🔍 Keeping browser open for inspection...")
                time.sleep(15)
                browser.close()

    def generate_enhanced_report(self):
        """Generate enhanced test report"""
        print("\n" + "=" * 70)
        print("📊 ENHANCED TESTING REPORT - COMPONENT FIXES")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        partial_tests = len([r for r in self.test_results if r['status'] == 'PARTIAL'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📈 RESULTS:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ⚠️ Partial: {partial_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📊 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📋 DETAILED RESULTS:")
        for result in self.test_results:
            status_icon = {"PASS": "✅", "PARTIAL": "⚠️", "FAIL": "❌"}.get(result['status'], "❓")
            print(f"   {status_icon} {result['test']}: {result['details']}")

if __name__ == "__main__":
    tester = EnhancedComponentTester()
    tester.run_enhanced_tests()
