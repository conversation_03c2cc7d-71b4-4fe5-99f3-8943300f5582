#!/usr/bin/env python3
"""
Setup script for Enhanced Testerat
Universal Web Testing Framework
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="testerat-enhanced",
    version="2.0.0",
    author="Enhanced Testerat Team",
    author_email="<EMAIL>",
    description="Universal Web Testing Framework - Comprehensive testing for any web application",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/testerat/enhanced",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Testing",
        "Topic :: Software Development :: Quality Assurance",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Framework :: Playwright",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-playwright>=0.4.0",
            "mypy>=1.7.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "isort>=5.12.0",
        ],
        "full": [
            "selenium>=4.15.0",
            "requests-html>=0.10.0",
            "pandas>=2.1.0",
            "matplotlib>=3.8.0",
            "reportlab>=4.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "testerat=testerat_enhanced.cli:main",
            "testerat-enhanced=testerat_enhanced.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "testerat_enhanced": [
            "config/*.json",
            "templates/*.html",
            "templates/*.css",
            "examples/*.py",
            "docs/*.md",
        ],
    },
    keywords=[
        "testing", "web-testing", "automation", "playwright", "selenium",
        "authentication", "workflows", "api-testing", "security", "accessibility",
        "react", "vue", "angular", "nextjs", "nuxtjs", "svelte",
        "universal", "framework-agnostic", "comprehensive"
    ],
    project_urls={
        "Bug Reports": "https://github.com/testerat/enhanced/issues",
        "Source": "https://github.com/testerat/enhanced",
        "Documentation": "https://testerat.dev/docs",
        "Changelog": "https://github.com/testerat/enhanced/blob/main/CHANGELOG.md",
    },
)
