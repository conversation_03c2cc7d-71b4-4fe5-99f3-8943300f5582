#!/usr/bin/env python3
"""
Modal and Popup Handling Engine for Enhanced Testerat

This engine provides comprehensive modal and popup testing capabilities including:
- Detection of various modal types (confirmation, form, alert, custom)
- Interaction testing (open, close, submit, cancel)
- Accessibility testing for modals
- Keyboard navigation testing
- Focus management validation
- Overlay and backdrop testing
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from playwright.sync_api import Page, ElementHandle, Locator


class ModalType(Enum):
    """Types of modals and popups"""
    ALERT = "alert"
    CONFIRM = "confirm"
    FORM = "form"
    CUSTOM = "custom"
    TOOLTIP = "tooltip"
    DROPDOWN = "dropdown"
    OVERLAY = "overlay"


@dataclass
class ModalConfig:
    """Configuration for modal and popup testing"""
    test_modal_types: List[ModalType] = None
    test_keyboard_navigation: bool = True
    test_focus_management: bool = True
    test_accessibility: bool = True
    test_backdrop_click: bool = True
    test_escape_key: bool = True
    custom_modal_selectors: List[str] = None
    
    def __post_init__(self):
        if self.test_modal_types is None:
            self.test_modal_types = [ModalType.ALERT, ModalType.CONFIRM, ModalType.FORM, ModalType.CUSTOM]
        if self.custom_modal_selectors is None:
            self.custom_modal_selectors = []


class ModalPopupEngine:
    """
    Comprehensive modal and popup testing engine
    
    Tests various modal scenarios including:
    - Modal detection and classification
    - Open/close functionality
    - Form submission within modals
    - Keyboard navigation and accessibility
    - Focus management
    - Backdrop and overlay behavior
    """
    
    def __init__(self, config: ModalConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        
        # Common modal selectors
        self.modal_selectors = {
            'generic': [
                '[role="dialog"]',
                '[role="alertdialog"]',
                '.modal',
                '.popup',
                '.dialog',
                '.overlay',
                '[class*="modal"]',
                '[class*="popup"]',
                '[class*="dialog"]',
                '[id*="modal"]',
                '[id*="popup"]',
                '[id*="dialog"]'
            ],
            'bootstrap': [
                '.modal',
                '.modal-dialog',
                '.modal-content'
            ],
            'material': [
                '.mat-dialog-container',
                '.cdk-overlay-pane',
                'mat-dialog-container'
            ],
            'custom': self.config.custom_modal_selectors
        }
        
        # Modal trigger selectors
        self.trigger_selectors = [
            '[data-toggle="modal"]',
            '[data-target*="modal"]',
            '[onclick*="modal"]',
            '[onclick*="popup"]',
            '[onclick*="dialog"]',
            'button[class*="modal"]',
            'button[class*="popup"]',
            'a[class*="modal"]',
            'a[class*="popup"]'
        ]
    
    def test_modals_and_popups(self, page: Page, base_url: str) -> Dict[str, Any]:
        """
        Run comprehensive modal and popup tests
        
        Returns detailed test results including:
        - Modal detection and classification
        - Interaction functionality
        - Accessibility compliance
        - Keyboard navigation
        - Focus management
        """
        self.logger.info("🔄 Starting comprehensive modal and popup testing")
        
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'modals_found': 0,
            'triggers_found': 0,
            'modal_types': {},
            'test_details': [],
            'critical_issues': [],
            'recommendations': []
        }
        
        try:
            # Discover modal elements and triggers
            modal_elements = self._discover_modal_elements(page)
            trigger_elements = self._discover_modal_triggers(page)
            
            results['modals_found'] = len(modal_elements)
            results['triggers_found'] = len(trigger_elements)
            
            if not modal_elements and not trigger_elements:
                self.logger.info("ℹ️ No modal or popup elements found on the page")
                results['recommendations'].append("Consider adding modal functionality if user interactions require it")
                return results
            
            # Test existing modals (if any are visible)
            for i, modal in enumerate(modal_elements):
                self.logger.info(f"🔍 Testing existing modal {i+1}/{len(modal_elements)}")
                modal_results = self._test_existing_modal(page, modal)
                results['test_details'].extend(modal_results['tests'])
                results['total_tests'] += modal_results['total_tests']
                results['passed'] += modal_results['passed']
                results['failed'] += modal_results['failed']
                results['critical_issues'].extend(modal_results['critical_issues'])
            
            # Test modal triggers
            for i, trigger in enumerate(trigger_elements):
                self.logger.info(f"🔍 Testing modal trigger {i+1}/{len(trigger_elements)}")
                trigger_results = self._test_modal_trigger(page, trigger)
                results['test_details'].extend(trigger_results['tests'])
                results['total_tests'] += trigger_results['total_tests']
                results['passed'] += trigger_results['passed']
                results['failed'] += trigger_results['failed']
                results['critical_issues'].extend(trigger_results['critical_issues'])
            
            # Test browser dialogs (alert, confirm, prompt)
            dialog_results = self._test_browser_dialogs(page)
            results['test_details'].extend(dialog_results['tests'])
            results['total_tests'] += dialog_results['total_tests']
            results['passed'] += dialog_results['passed']
            results['failed'] += dialog_results['failed']
            results['critical_issues'].extend(dialog_results['critical_issues'])
            
            # Generate recommendations
            self._generate_modal_recommendations(results)
            
        except Exception as e:
            self.logger.error(f"❌ Modal and popup testing failed: {str(e)}")
            results['critical_issues'].append({
                'test_name': 'modal_popup_testing',
                'severity': 'HIGH',
                'details': f"Modal and popup testing failed: {str(e)}",
                'recommendations': ['Fix modal and popup testing infrastructure']
            })
        
        self.logger.info(f"✅ Modal and popup testing completed: {results['passed']}/{results['total_tests']} passed")
        return results
    
    def _discover_modal_elements(self, page: Page) -> List[ElementHandle]:
        """Discover all modal elements currently on the page"""
        modal_elements = []
        
        try:
            # Check all modal selector categories
            for category, selectors in self.modal_selectors.items():
                for selector in selectors:
                    try:
                        elements = page.query_selector_all(selector)
                        for element in elements:
                            # Check if element is visible or has modal-like properties
                            if self._is_modal_element(element):
                                modal_elements.append(element)
                    except Exception as e:
                        self.logger.debug(f"Selector '{selector}' failed: {str(e)}")
                        continue
            
            # Remove duplicates
            unique_modals = []
            for modal in modal_elements:
                if modal not in unique_modals:
                    unique_modals.append(modal)
            
            self.logger.info(f"📋 Found {len(unique_modals)} modal elements")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to discover modal elements: {str(e)}")
        
        return unique_modals
    
    def _discover_modal_triggers(self, page: Page) -> List[ElementHandle]:
        """Discover elements that might trigger modals"""
        trigger_elements = []
        
        try:
            for selector in self.trigger_selectors:
                try:
                    elements = page.query_selector_all(selector)
                    trigger_elements.extend(elements)
                except Exception as e:
                    self.logger.debug(f"Trigger selector '{selector}' failed: {str(e)}")
                    continue
            
            # Also look for buttons/links with modal-related text
            text_triggers = page.query_selector_all('button, a, [role="button"]')
            for element in text_triggers:
                try:
                    text = element.inner_text().lower()
                    if any(keyword in text for keyword in ['open', 'show', 'popup', 'modal', 'dialog']):
                        trigger_elements.append(element)
                except:
                    continue
            
            # Remove duplicates
            unique_triggers = []
            for trigger in trigger_elements:
                if trigger not in unique_triggers:
                    unique_triggers.append(trigger)
            
            self.logger.info(f"🎯 Found {len(unique_triggers)} potential modal triggers")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to discover modal triggers: {str(e)}")
        
        return unique_triggers
    
    def _is_modal_element(self, element: ElementHandle) -> bool:
        """Check if an element is likely a modal"""
        try:
            # Check visibility
            if not element.is_visible():
                return False
            
            # Check for modal-specific attributes
            role = element.get_attribute('role')
            if role in ['dialog', 'alertdialog']:
                return True
            
            # Check for modal-specific classes
            class_name = element.get_attribute('class') or ''
            if any(modal_class in class_name.lower() for modal_class in ['modal', 'dialog', 'popup']):
                return True
            
            # Check for overlay-like positioning
            computed_style = element.evaluate('el => window.getComputedStyle(el)')
            position = computed_style.get('position', '')
            z_index = computed_style.get('z-index', '0')
            
            if position in ['fixed', 'absolute'] and z_index != 'auto' and int(z_index or 0) > 100:
                return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"Error checking modal element: {str(e)}")
            return False

    def _test_existing_modal(self, page: Page, modal: ElementHandle) -> Dict[str, Any]:
        """Test an existing modal element"""
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'tests': [],
            'critical_issues': []
        }

        try:
            # Test modal accessibility
            if self.config.test_accessibility:
                test_result = self._test_modal_accessibility(page, modal)
                results['tests'].append(test_result)
                results['total_tests'] += 1
                if test_result['passed']:
                    results['passed'] += 1
                else:
                    results['failed'] += 1
                    if test_result['severity'] == 'HIGH':
                        results['critical_issues'].append(test_result)

            # Test close functionality
            test_result = self._test_modal_close_functionality(page, modal)
            results['tests'].append(test_result)
            results['total_tests'] += 1
            if test_result['passed']:
                results['passed'] += 1
            else:
                results['failed'] += 1
                if test_result['severity'] == 'HIGH':
                    results['critical_issues'].append(test_result)

        except Exception as e:
            self.logger.error(f"❌ Existing modal testing failed: {str(e)}")
            results['critical_issues'].append({
                'test_name': 'existing_modal_testing',
                'severity': 'HIGH',
                'details': f"Existing modal testing failed: {str(e)}",
                'recommendations': ['Fix modal testing implementation'],
                'passed': False
            })

        return results

    def _test_modal_trigger(self, page: Page, trigger: ElementHandle) -> Dict[str, Any]:
        """Test a modal trigger element"""
        results = {
            'total_tests': 1,
            'passed': 0,
            'failed': 0,
            'tests': [],
            'critical_issues': []
        }

        test_result = {
            'test_name': 'modal_trigger_functionality',
            'description': 'Test modal trigger opens modal',
            'passed': False,
            'severity': 'MEDIUM',
            'details': '',
            'recommendations': []
        }

        try:
            # Get initial modal count
            initial_modals = len(self._discover_modal_elements(page))

            # Click the trigger
            trigger.click()

            # Wait for potential modal to appear
            page.wait_for_timeout(1000)

            # Check if new modal appeared
            final_modals = len(self._discover_modal_elements(page))

            if final_modals > initial_modals:
                test_result['passed'] = True
                test_result['details'] = f"Modal trigger successfully opened modal"
            else:
                test_result['details'] = "Modal trigger may not have opened a modal"
                test_result['recommendations'].append("Verify modal trigger functionality")

        except Exception as e:
            test_result['details'] = f"Modal trigger test failed: {str(e)}"
            test_result['recommendations'].append("Fix modal trigger functionality")
            test_result['severity'] = 'HIGH'

        results['tests'].append(test_result)
        if test_result['passed']:
            results['passed'] = 1
        else:
            results['failed'] = 1
            if test_result['severity'] == 'HIGH':
                results['critical_issues'].append(test_result)

        return results

    def _test_browser_dialogs(self, page: Page) -> Dict[str, Any]:
        """Test browser native dialogs (alert, confirm, prompt)"""
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'tests': [],
            'critical_issues': []
        }

        # Look for elements that might trigger browser dialogs
        dialog_triggers = page.query_selector_all('[onclick*="alert"], [onclick*="confirm"], [onclick*="prompt"]')

        for trigger in dialog_triggers:
            test_result = {
                'test_name': 'browser_dialog_handling',
                'description': 'Test browser dialog handling',
                'passed': False,
                'severity': 'MEDIUM',
                'details': '',
                'recommendations': []
            }

            try:
                # Set up dialog handler
                dialog_handled = False

                def handle_dialog(dialog):
                    nonlocal dialog_handled
                    dialog_handled = True
                    dialog.accept()  # Accept the dialog

                page.on('dialog', handle_dialog)

                # Click the trigger
                trigger.click()

                # Wait for potential dialog
                page.wait_for_timeout(1000)

                if dialog_handled:
                    test_result['passed'] = True
                    test_result['details'] = "Browser dialog successfully handled"
                else:
                    test_result['details'] = "No browser dialog detected"
                    test_result['passed'] = True  # Not necessarily a failure

                # Remove dialog handler
                page.remove_listener('dialog', handle_dialog)

            except Exception as e:
                test_result['details'] = f"Browser dialog test failed: {str(e)}"
                test_result['recommendations'].append("Fix browser dialog handling")
                test_result['severity'] = 'HIGH'

            results['tests'].append(test_result)
            results['total_tests'] += 1
            if test_result['passed']:
                results['passed'] += 1
            else:
                results['failed'] += 1
                if test_result['severity'] == 'HIGH':
                    results['critical_issues'].append(test_result)

        return results

    def _test_modal_accessibility(self, page: Page, modal: ElementHandle) -> Dict[str, Any]:
        """Test modal accessibility features"""
        test_result = {
            'test_name': 'modal_accessibility',
            'description': 'Test modal accessibility compliance',
            'passed': False,
            'severity': 'HIGH',
            'details': '',
            'recommendations': []
        }

        try:
            accessibility_issues = []

            # Check for proper role
            role = modal.get_attribute('role')
            if role not in ['dialog', 'alertdialog']:
                accessibility_issues.append("Modal missing proper role attribute")

            # Check for aria-labelledby or aria-label
            aria_labelledby = modal.get_attribute('aria-labelledby')
            aria_label = modal.get_attribute('aria-label')
            if not aria_labelledby and not aria_label:
                accessibility_issues.append("Modal missing aria-labelledby or aria-label")

            # Check for aria-describedby
            aria_describedby = modal.get_attribute('aria-describedby')
            if not aria_describedby:
                accessibility_issues.append("Modal missing aria-describedby")

            # Check for focusable elements
            focusable_elements = modal.query_selector_all('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')
            if not focusable_elements:
                accessibility_issues.append("Modal has no focusable elements")

            if not accessibility_issues:
                test_result['passed'] = True
                test_result['details'] = "Modal passes accessibility checks"
            else:
                test_result['details'] = f"Accessibility issues found: {', '.join(accessibility_issues)}"
                test_result['recommendations'] = [f"Fix: {issue}" for issue in accessibility_issues]

        except Exception as e:
            test_result['details'] = f"Accessibility test failed: {str(e)}"
            test_result['recommendations'].append("Fix modal accessibility testing")

        return test_result

    def _test_modal_close_functionality(self, page: Page, modal: ElementHandle) -> Dict[str, Any]:
        """Test modal close functionality"""
        test_result = {
            'test_name': 'modal_close_functionality',
            'description': 'Test modal can be closed',
            'passed': False,
            'severity': 'MEDIUM',
            'details': '',
            'recommendations': []
        }

        try:
            # Look for close buttons
            close_selectors = [
                'button[class*="close"]',
                '[data-dismiss="modal"]',
                '.modal-close',
                '[aria-label*="close"]',
                '[title*="close"]'
            ]

            close_button = None
            for selector in close_selectors:
                try:
                    close_button = modal.query_selector(selector)
                    if close_button:
                        break
                except:
                    continue

            if close_button:
                # Try to close the modal
                close_button.click()
                page.wait_for_timeout(1000)

                # Check if modal is still visible
                if not modal.is_visible():
                    test_result['passed'] = True
                    test_result['details'] = "Modal successfully closed with close button"
                else:
                    test_result['details'] = "Modal close button did not close modal"
                    test_result['recommendations'].append("Fix modal close functionality")
            else:
                test_result['details'] = "No close button found in modal"
                test_result['recommendations'].append("Add close button to modal")

        except Exception as e:
            test_result['details'] = f"Modal close test failed: {str(e)}"
            test_result['recommendations'].append("Fix modal close functionality")

        return test_result

    def _generate_modal_recommendations(self, results: Dict[str, Any]):
        """Generate recommendations based on modal test results"""
        if results['modals_found'] == 0 and results['triggers_found'] == 0:
            results['recommendations'].append("Consider adding modal functionality for better user interactions")

        if results['failed'] > results['passed'] * 0.5:
            results['recommendations'].append("Modal functionality needs significant improvements")

        accessibility_issues = sum(1 for test in results['test_details'] if 'accessibility' in test['test_name'] and not test['passed'])
        if accessibility_issues > 0:
            results['recommendations'].append("Improve modal accessibility compliance")

        close_issues = sum(1 for test in results['test_details'] if 'close' in test['test_name'] and not test['passed'])
        if close_issues > 0:
            results['recommendations'].append("Fix modal close functionality")
