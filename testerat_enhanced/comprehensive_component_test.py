#!/usr/bin/env python3
"""
Comprehensive Component Test
Test all major components after CSRF fix
"""

from playwright.sync_api import sync_playwright
import time

def test_all_components():
    print("🧪 COMPREHENSIVE COMPONENT TEST")
    print("=" * 60)
    
    results = []
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Login first
            print("🔐 Authenticating...")
            page.goto("http://localhost:3001/login")
            page.wait_for_load_state('networkidle')
            
            email_input = page.query_selector("input[id='email']")
            password_input = page.query_selector("input[id='password']")
            submit_btn = page.query_selector("button[type='submit']")
            
            if all([email_input, password_input, submit_btn]):
                email_input.fill("<EMAIL>")
                password_input.fill("testpassword")
                submit_btn.click()
                page.wait_for_load_state('networkidle')
                time.sleep(2)
                print("✅ Authentication successful")
            else:
                print("❌ Authentication failed")
                return
            
            # Test 1: Salary Calculator
            print("\n💰 TESTING SALARY CALCULATOR")
            print("-" * 40)
            
            page.goto("http://localhost:3001/tools/salary-calculator")
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            try:
                # Test career path dropdown
                career_trigger = page.wait_for_selector('[data-testid="career-path-trigger"]', timeout=5000)
                career_trigger.click()
                time.sleep(1)
                
                # Select AI/Machine Learning Engineer
                ai_option = page.query_selector('text="AI/Machine Learning Engineer"')
                if ai_option:
                    ai_option.click()
                    print("✅ Career path selection: AI/Machine Learning Engineer")
                    results.append("salary_calc_career_path: PASS")
                else:
                    print("❌ Career path selection failed")
                    results.append("salary_calc_career_path: FAIL")
                
                time.sleep(1)
                
                # Test experience level
                exp_trigger = page.query_selector('[data-testid="experience-trigger"]')
                if exp_trigger:
                    exp_trigger.click()
                    time.sleep(1)
                    
                    # Look for "Mid Level" option
                    mid_option = page.query_selector('text*="Mid Level"')
                    if mid_option:
                        mid_option.click()
                        print("✅ Experience level selection: Mid Level")
                        results.append("salary_calc_experience: PASS")
                    else:
                        print("❌ Experience level selection failed")
                        results.append("salary_calc_experience: FAIL")
                else:
                    print("❌ Experience trigger not found")
                    results.append("salary_calc_experience: FAIL")
                
                time.sleep(1)
                
                # Test location
                location_trigger = page.query_selector('[data-testid="location-trigger"]')
                if location_trigger:
                    location_trigger.click()
                    time.sleep(1)
                    
                    sf_option = page.query_selector('text*="San Francisco"')
                    if sf_option:
                        sf_option.click()
                        print("✅ Location selection: San Francisco")
                        results.append("salary_calc_location: PASS")
                    else:
                        print("❌ Location selection failed")
                        results.append("salary_calc_location: FAIL")
                else:
                    print("❌ Location trigger not found")
                    results.append("salary_calc_location: FAIL")
                
                time.sleep(1)
                
                # Test skills input
                skills_input = page.query_selector('input[placeholder*="skill"]')
                if skills_input:
                    skills_input.fill("Python")
                    page.keyboard.press('Enter')
                    time.sleep(1)
                    
                    python_badge = page.query_selector('text="Python"')
                    if python_badge:
                        print("✅ Skills input: Python added")
                        results.append("salary_calc_skills: PASS")
                    else:
                        print("❌ Skills input failed")
                        results.append("salary_calc_skills: FAIL")
                else:
                    print("❌ Skills input not found")
                    results.append("salary_calc_skills: FAIL")
                
                # Test calculate button
                calculate_btn = page.query_selector('button:has-text("Calculate Salary")')
                if calculate_btn:
                    is_disabled = calculate_btn.get_attribute('disabled')
                    if is_disabled is None:
                        calculate_btn.click()
                        print("✅ Calculate button clicked")
                        
                        # Wait for results
                        time.sleep(5)
                        
                        # Check for results
                        salary_estimate = page.query_selector('text="Salary Estimate"')
                        if salary_estimate:
                            print("✅ Calculation results displayed")
                            results.append("salary_calc_calculate: PASS")
                        else:
                            print("❌ No calculation results")
                            results.append("salary_calc_calculate: FAIL")
                    else:
                        print("❌ Calculate button is disabled")
                        results.append("salary_calc_calculate: FAIL")
                else:
                    print("❌ Calculate button not found")
                    results.append("salary_calc_calculate: FAIL")
                    
            except Exception as e:
                print(f"❌ Salary calculator error: {e}")
                results.append("salary_calc_error: FAIL")
            
            # Test 2: Assessment
            print("\n📝 TESTING ASSESSMENT")
            print("-" * 40)
            
            page.goto("http://localhost:3001/assessment")
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            try:
                # Check for assessment content
                form_elements = page.query_selector_all('form, input, button, select')
                if len(form_elements) > 0:
                    print(f"✅ Assessment page loaded with {len(form_elements)} interactive elements")
                    results.append("assessment_load: PASS")
                    
                    # Look for action buttons
                    action_buttons = page.query_selector_all('button:has-text("Next"), button:has-text("Start"), button:has-text("Continue")')
                    if action_buttons:
                        print(f"✅ Found {len(action_buttons)} action buttons")
                        results.append("assessment_buttons: PASS")
                    else:
                        print("❌ No action buttons found")
                        results.append("assessment_buttons: FAIL")
                else:
                    print("❌ No assessment elements found")
                    results.append("assessment_load: FAIL")
                    
            except Exception as e:
                print(f"❌ Assessment error: {e}")
                results.append("assessment_error: FAIL")
            
            # Test 3: Interview Practice
            print("\n🎤 TESTING INTERVIEW PRACTICE")
            print("-" * 40)
            
            page.goto("http://localhost:3001/tools/interview-practice")
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            try:
                # Check for interview practice content
                buttons = page.query_selector_all('button')
                links = page.query_selector_all('a')
                
                total_elements = len(buttons) + len(links)
                
                if total_elements > 0:
                    print(f"✅ Interview practice loaded with {total_elements} interactive elements")
                    results.append("interview_practice_load: PASS")
                    
                    # Look for practice-related buttons
                    practice_buttons = []
                    for button in buttons:
                        text = button.text_content().lower()
                        if any(word in text for word in ['start', 'practice', 'begin', 'create']):
                            practice_buttons.append(button)
                    
                    if practice_buttons:
                        print(f"✅ Found {len(practice_buttons)} practice buttons")
                        results.append("interview_practice_buttons: PASS")
                    else:
                        print("❌ No practice buttons found")
                        results.append("interview_practice_buttons: FAIL")
                else:
                    print("❌ No interview practice elements found")
                    results.append("interview_practice_load: FAIL")
                    
            except Exception as e:
                print(f"❌ Interview practice error: {e}")
                results.append("interview_practice_error: FAIL")
            
            # Test 4: Career Paths
            print("\n🛤️ TESTING CAREER PATHS")
            print("-" * 40)
            
            page.goto("http://localhost:3001/career-paths")
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            try:
                # Check for career paths content
                cards = page.query_selector_all('[class*="card"]')
                career_elements = page.query_selector_all('[class*="career"], [class*="path"]')
                
                total_content = len(cards) + len(career_elements)
                
                if total_content > 0:
                    print(f"✅ Career paths loaded with {total_content} content elements")
                    results.append("career_paths_load: PASS")
                    
                    # Try clicking first card if available
                    if cards:
                        try:
                            cards[0].click()
                            time.sleep(2)
                            current_url = page.url
                            if '/career-paths/' in current_url:
                                print("✅ Career path navigation successful")
                                results.append("career_paths_navigation: PASS")
                            else:
                                print("❌ Career path navigation failed")
                                results.append("career_paths_navigation: FAIL")
                        except:
                            print("❌ Career path click failed")
                            results.append("career_paths_navigation: FAIL")
                    else:
                        print("❌ No career path cards found")
                        results.append("career_paths_navigation: FAIL")
                else:
                    print("❌ No career paths content found")
                    results.append("career_paths_load: FAIL")
                    
            except Exception as e:
                print(f"❌ Career paths error: {e}")
                results.append("career_paths_error: FAIL")
            
            # Generate final report
            print("\n" + "=" * 60)
            print("📊 COMPREHENSIVE TEST RESULTS")
            print("=" * 60)
            
            passed = len([r for r in results if 'PASS' in r])
            failed = len([r for r in results if 'FAIL' in r])
            total = len(results)
            
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 OVERALL RESULTS:")
            print(f"   Total Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {failed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for result in results:
                status_icon = "✅" if "PASS" in result else "❌"
                print(f"   {status_icon} {result}")
            
            print(f"\n🎯 ASSESSMENT:")
            if success_rate >= 80:
                print("🎉 EXCELLENT: Components are working well!")
            elif success_rate >= 60:
                print("✅ GOOD: Most components working, minor issues remain")
            elif success_rate >= 40:
                print("⚠️ FAIR: Some components working, needs improvement")
            else:
                print("❌ POOR: Major component issues need attention")
            
        except Exception as e:
            print(f"❌ Critical test error: {e}")
        finally:
            print("\n🔍 Test completed. Browser will close in 10 seconds...")
            time.sleep(10)
            browser.close()

if __name__ == "__main__":
    test_all_components()
