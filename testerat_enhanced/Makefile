# Enhanced Testerat Makefile
# Universal Web Testing Framework

.PHONY: help install install-dev install-browsers test test-integration validate clean lint format docs

# Default target
help:
	@echo "🎯 Enhanced Testerat - Universal Web Testing Framework"
	@echo "Available commands:"
	@echo ""
	@echo "📦 Installation:"
	@echo "  make install          Install Enhanced Testerat"
	@echo "  make install-dev      Install with development dependencies"
	@echo "  make install-browsers Install Playwright browsers"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  make test            Run all tests"
	@echo "  make test-integration Run integration tests"
	@echo "  make validate        Validate fixes and implementation"
	@echo ""
	@echo "🔧 Development:"
	@echo "  make lint            Run code linting"
	@echo "  make format          Format code"
	@echo "  make clean           Clean build artifacts"
	@echo ""
	@echo "📖 Documentation:"
	@echo "  make docs            Generate documentation"
	@echo ""
	@echo "🚀 Quick Start:"
	@echo "  make install-all     Install everything (recommended)"
	@echo "  make test-all        Run all tests and validation"

# Installation targets
install:
	@echo "📦 Installing Enhanced Testerat..."
	pip install -r requirements.txt
	pip install -e .
	@echo "✅ Installation complete!"

install-dev:
	@echo "📦 Installing Enhanced Testerat with development dependencies..."
	pip install -r requirements.txt
	pip install -e ".[dev]"
	@echo "✅ Development installation complete!"

install-browsers:
	@echo "🌐 Installing Playwright browsers..."
	playwright install
	@echo "✅ Browsers installed!"

install-all: install install-browsers
	@echo "🎉 Complete installation finished!"
	@echo "Run 'make test-integration' to verify everything works."

# Testing targets
test:
	@echo "🧪 Running Enhanced Testerat tests..."
	python -m pytest tests/ -v
	@echo "✅ Tests complete!"

test-integration:
	@echo "🧪 Running integration tests..."
	python test_integration.py
	@echo "✅ Integration tests complete!"

validate:
	@echo "🔍 Validating fixes and implementation..."
	python validate_fixes.py
	@echo "✅ Validation complete!"

test-all: test test-integration validate
	@echo "🎉 All tests and validation complete!"

# Development targets
lint:
	@echo "🔍 Running code linting..."
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
	@echo "✅ Linting complete!"

format:
	@echo "🎨 Formatting code..."
	black . --line-length=127
	isort . --profile black
	@echo "✅ Formatting complete!"

clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf __pycache__/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	@echo "✅ Cleanup complete!"

# Documentation targets
docs:
	@echo "📖 Generating documentation..."
	@echo "Documentation files:"
	@echo "  - README.md"
	@echo "  - INSTALLATION.md"
	@echo "  - ARCHITECTURAL_FIXES_SUMMARY.md"
	@echo "  - IMPLEMENTATION_STATUS.md"
	@echo "  - DEVELOPMENT_COMPLETION_REPORT.md"
	@echo "✅ Documentation ready!"

# Quick demo
demo:
	@echo "🚀 Running Enhanced Testerat demo..."
	python -m testerat_enhanced.cli https://httpbin.org/html "Demo Test" --verbose
	@echo "✅ Demo complete!"

# Verify installation
verify:
	@echo "🔍 Verifying Enhanced Testerat installation..."
	@echo "Python version:"
	python --version
	@echo ""
	@echo "Enhanced Testerat version:"
	python -m testerat_enhanced.cli --version
	@echo ""
	@echo "Testing basic import:"
	python -c "from testerat_enhanced import EnhancedTesterat; print('✅ Import successful')"
	@echo ""
	@echo "Testing Playwright:"
	python -c "from playwright.sync_api import sync_playwright; print('✅ Playwright available')"
	@echo "✅ Verification complete!"

# Development workflow
dev-setup: install-dev install-browsers
	@echo "🔧 Setting up development environment..."
	pre-commit install || echo "⚠️  pre-commit not available (optional)"
	@echo "✅ Development environment ready!"

# Release preparation
release-check: clean lint test-all
	@echo "🚀 Release readiness check..."
	@echo "✅ All checks passed - ready for release!"

# Help for specific commands
help-install:
	@echo "📦 Installation Help:"
	@echo ""
	@echo "1. Basic installation:"
	@echo "   make install"
	@echo ""
	@echo "2. Install with browsers:"
	@echo "   make install-all"
	@echo ""
	@echo "3. Development installation:"
	@echo "   make install-dev"
	@echo ""
	@echo "4. Verify installation:"
	@echo "   make verify"

help-testing:
	@echo "🧪 Testing Help:"
	@echo ""
	@echo "1. Run all tests:"
	@echo "   make test-all"
	@echo ""
	@echo "2. Integration tests only:"
	@echo "   make test-integration"
	@echo ""
	@echo "3. Validate fixes:"
	@echo "   make validate"
	@echo ""
	@echo "4. Quick demo:"
	@echo "   make demo"

# System requirements check
check-system:
	@echo "🔍 Checking system requirements..."
	@echo "Python version:"
	python --version
	@echo ""
	@echo "Pip version:"
	pip --version
	@echo ""
	@echo "Node.js version (for Playwright):"
	node --version || echo "⚠️  Node.js not found (required for Playwright)"
	@echo ""
	@echo "Git version:"
	git --version || echo "⚠️  Git not found (optional)"
	@echo ""
	@echo "Available disk space:"
	df -h . | tail -1 || echo "⚠️  Cannot check disk space"
	@echo "✅ System check complete!"
