Metadata-Version: 2.1
Name: testerat-enhanced
Version: 2.0.0
Summary: Universal Web Testing Framework - Comprehensive testing for any web application
Home-page: https://github.com/testerat/enhanced
Author: Enhanced Testerat Team
Author-email: <EMAIL>
License: UNKNOWN
Project-URL: Bug Reports, https://github.com/testerat/enhanced/issues
Project-URL: Source, https://github.com/testerat/enhanced
Project-URL: Documentation, https://testerat.dev/docs
Project-URL: Changelog, https://github.com/testerat/enhanced/blob/main/CHANGELOG.md
Keywords: testing,web-testing,automation,playwright,selenium,authentication,workflows,api-testing,security,accessibility,react,vue,angular,nextjs,nuxtjs,svelte,universal,framework-agnostic,comprehensive
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Testing
Classifier: Topic :: Software Development :: Quality Assurance
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Operating System :: OS Independent
Classifier: Framework :: Playwright
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Provides-Extra: dev
Provides-Extra: full

# Testerat Enhanced

A comprehensive testing framework for web applications with enhanced capabilities for testing complex user workflows.

## Features

- Universal authentication testing
- Comprehensive workflow testing
- API endpoint validation
- Real-time reporting
- Multi-framework support

## Installation

```bash
pip install -e .
```

## Usage

```python
from testerat_enhanced import TestRunner
runner = TestRunner()
runner.run_comprehensive_tests()
```


