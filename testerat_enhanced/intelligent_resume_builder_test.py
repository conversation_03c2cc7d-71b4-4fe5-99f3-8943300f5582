#!/usr/bin/env python3
"""
INTELLIGENT Resume Builder Testing
State-aware testing that understands the application context
"""

from playwright.sync_api import sync_playwright
import time

class IntelligentResumeBuilderTester:
    def __init__(self):
        self.current_state = "unknown"
        self.authentication_status = "unknown"
        self.current_url = ""
        self.test_results = []
        
    def detect_current_state(self, page):
        """Intelligently detect the current application state"""
        self.current_url = page.url
        
        print(f"🔍 Detecting current state at: {self.current_url}")
        
        # Check authentication status first
        self.authentication_status = self.detect_authentication_status(page)
        print(f"   🔐 Authentication: {self.authentication_status}")
        
        # Detect page state
        if "/login" in self.current_url:
            self.current_state = "login_page"
        elif "/dashboard" in self.current_url:
            self.current_state = "dashboard"
        elif "/resume-builder" in self.current_url:
            self.current_state = self.detect_resume_builder_state(page)
        elif self.current_url.endswith("/") or "/home" in self.current_url:
            self.current_state = "home_page"
        else:
            self.current_state = "unknown_page"
            
        print(f"   📍 Current state: {self.current_state}")
        return self.current_state
    
    def detect_authentication_status(self, page):
        """Detect if user is authenticated"""
        auth_indicators = [
            '[aria-label="Sign out of your account"]',
            '[aria-label="View your profile"]',
            'button:has-text("Sign out")',
            'a:has-text("Profile")'
        ]
        
        unauth_indicators = [
            '[aria-label="Log in to your account"]',
            '[aria-label="Create a new account"]',
            'button:has-text("Sign in")',
            'a:has-text("Login")'
        ]
        
        is_authenticated = any(page.query_selector(selector) for selector in auth_indicators)
        is_unauthenticated = any(page.query_selector(selector) for selector in unauth_indicators)
        
        if is_authenticated:
            return "authenticated"
        elif is_unauthenticated:
            return "unauthenticated"
        else:
            return "unknown"
    
    def detect_resume_builder_state(self, page):
        """Detect specific resume builder state"""
        # Check if we're in the resume list
        if page.query_selector('button:has-text("Create"), button:has-text("New Resume")'):
            return "resume_list"
        
        # Check if we're in the resume editor
        if page.query_selector('input[name="firstName"], input[id="firstName"]'):
            return "resume_editor"
        
        # Check if we're in template selection
        if page.query_selector('.template, [data-template]'):
            return "template_selection"
        
        return "resume_builder_unknown"
    
    def navigate_to_target_state(self, page, target_state):
        """Intelligently navigate to the target state"""
        print(f"🎯 Navigating from '{self.current_state}' to '{target_state}'")
        
        if target_state == "authenticated":
            return self.ensure_authenticated(page)
        elif target_state == "resume_builder":
            return self.navigate_to_resume_builder(page)
        elif target_state == "resume_editor":
            return self.navigate_to_resume_editor(page)
        else:
            print(f"❌ Don't know how to navigate to: {target_state}")
            return False
    
    def ensure_authenticated(self, page):
        """Ensure user is authenticated"""
        if self.authentication_status == "authenticated":
            print("   ✅ Already authenticated")
            return True
        
        print("   🔐 Need to authenticate...")
        
        # Navigate to login if not already there
        if self.current_state != "login_page":
            page.goto("http://localhost:3002/login")
            page.wait_for_load_state('networkidle')
            time.sleep(1)
        
        # Fill login form
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        if all([email_input, password_input, submit_btn]):
            email_input.fill("<EMAIL>")
            password_input.fill("testpassword")
            submit_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Re-detect authentication status
            self.detect_current_state(page)
            
            if self.authentication_status == "authenticated":
                print("   ✅ Authentication successful")
                return True
            else:
                print("   ❌ Authentication failed")
                return False
        else:
            print("   ❌ Login form not found")
            return False
    
    def navigate_to_resume_builder(self, page):
        """Navigate to resume builder intelligently"""
        # First ensure we're authenticated
        if not self.ensure_authenticated(page):
            return False
        
        print("   🧭 Navigating to resume builder...")
        
        # Try direct navigation first
        page.goto("http://localhost:3002/resume-builder")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        self.detect_current_state(page)
        
        if "resume" in self.current_state:
            print("   ✅ Successfully reached resume builder")
            return True
        else:
            print(f"   ❌ Failed to reach resume builder, current state: {self.current_state}")
            return False
    
    def navigate_to_resume_editor(self, page):
        """Navigate to resume editor intelligently"""
        # First get to resume builder
        if not self.navigate_to_resume_builder(page):
            return False
        
        print("   📝 Navigating to resume editor...")
        
        # If we're in resume list, click create
        if self.current_state == "resume_list":
            create_btn = page.query_selector('button:has-text("Create"), button:has-text("New")')
            if create_btn:
                create_btn.click()
                page.wait_for_load_state('networkidle')
                time.sleep(2)
                
                self.detect_current_state(page)
                
                if self.current_state == "resume_editor":
                    print("   ✅ Successfully reached resume editor")
                    return True
        
        # If we're already in editor, we're good
        elif self.current_state == "resume_editor":
            print("   ✅ Already in resume editor")
            return True
        
        print(f"   ❌ Failed to reach resume editor, current state: {self.current_state}")
        return False
    
    def test_resume_builder_intelligently(self):
        """Run intelligent resume builder tests"""
        print("🧠 INTELLIGENT RESUME BUILDER TESTING")
        print("=" * 50)
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            page = browser.new_page()
            
            try:
                # Phase 1: Detect initial state
                print("\n1️⃣ INITIAL STATE DETECTION")
                page.goto("http://localhost:3002")
                page.wait_for_load_state('networkidle')
                self.detect_current_state(page)
                
                # Phase 2: Ensure authentication
                print("\n2️⃣ AUTHENTICATION PHASE")
                if not self.ensure_authenticated(page):
                    print("❌ Cannot proceed without authentication")
                    return False
                
                # Phase 3: Navigate to resume builder
                print("\n3️⃣ RESUME BUILDER NAVIGATION")
                if not self.navigate_to_resume_builder(page):
                    print("❌ Cannot access resume builder")
                    return False
                
                # Phase 4: Test resume builder functionality
                print("\n4️⃣ RESUME BUILDER FUNCTIONALITY TESTING")
                self.test_resume_builder_functionality(page)
                
                # Phase 5: Test form filling
                print("\n5️⃣ INTELLIGENT FORM TESTING")
                if self.navigate_to_resume_editor(page):
                    self.test_form_filling_intelligently(page)
                
                # Phase 6: Test save functionality
                print("\n6️⃣ SAVE FUNCTIONALITY TESTING")
                self.test_save_functionality_intelligently(page)
                
                # Generate results
                self.generate_intelligent_report()
                
            except Exception as e:
                print(f"❌ CRITICAL ERROR: {e}")
                return False
            finally:
                print("\n🔍 Keeping browser open for inspection...")
                time.sleep(10)
                browser.close()
    
    def test_resume_builder_functionality(self, page):
        """Test resume builder functionality based on current state"""
        self.detect_current_state(page)
        
        if self.current_state == "resume_list":
            print("   📋 Testing resume list functionality...")
            
            # Test create button
            create_btn = page.query_selector('button:has-text("Create"), button:has-text("New")')
            if create_btn:
                self.test_results.append({"test": "create_button", "status": "PASS", "details": "Create button found"})
                print("   ✅ Create button available")
            else:
                self.test_results.append({"test": "create_button", "status": "FAIL", "details": "Create button not found"})
                print("   ❌ Create button not found")
        
        elif self.current_state == "resume_editor":
            print("   📝 Testing resume editor functionality...")
            
            # Test form elements
            form_elements = page.query_selector_all('input, textarea, select')
            if len(form_elements) > 0:
                self.test_results.append({"test": "form_elements", "status": "PASS", "details": f"Found {len(form_elements)} form elements"})
                print(f"   ✅ Found {len(form_elements)} form elements")
            else:
                self.test_results.append({"test": "form_elements", "status": "FAIL", "details": "No form elements found"})
                print("   ❌ No form elements found")
    
    def test_form_filling_intelligently(self, page):
        """Intelligently test form filling"""
        print("   📝 Testing intelligent form filling...")
        
        test_data = {
            'firstName': 'John',
            'lastName': 'Doe',
            'email': '<EMAIL>',
            'phone': '+****************'
        }
        
        filled_fields = 0
        
        for field_name, field_value in test_data.items():
            # Try multiple intelligent selectors
            selectors = [
                f'input[name="{field_name}"]',
                f'input[id="{field_name}"]',
                f'input[placeholder*="{field_name}"]',
                f'input[aria-label*="{field_name}"]'
            ]
            
            field_found = False
            for selector in selectors:
                try:
                    field = page.query_selector(selector)
                    if field:
                        field.fill(field_value)
                        filled_fields += 1
                        field_found = True
                        print(f"   ✅ Filled {field_name}: {field_value}")
                        break
                except:
                    continue
            
            if not field_found:
                print(f"   ❌ Could not find field: {field_name}")
        
        success_rate = (filled_fields / len(test_data)) * 100
        self.test_results.append({
            "test": "form_filling", 
            "status": "PASS" if filled_fields >= len(test_data) * 0.7 else "FAIL",
            "details": f"Filled {filled_fields}/{len(test_data)} fields ({success_rate:.1f}%)"
        })
    
    def test_save_functionality_intelligently(self, page):
        """Intelligently test save functionality"""
        print("   💾 Testing save functionality...")
        
        save_btn = page.query_selector('button:has-text("Save")')
        if save_btn:
            save_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Check for success indicators
            success_indicators = [
                '.success', '.saved', '[role="alert"]:has-text("saved")', 'text="saved"'
            ]
            
            save_success = any(page.query_selector(selector) for selector in success_indicators)
            
            if save_success:
                self.test_results.append({"test": "save_functionality", "status": "PASS", "details": "Save successful with confirmation"})
                print("   ✅ Save successful with confirmation")
            else:
                self.test_results.append({"test": "save_functionality", "status": "PASS", "details": "Save completed (no confirmation shown)"})
                print("   ✅ Save completed (no confirmation shown)")
        else:
            self.test_results.append({"test": "save_functionality", "status": "FAIL", "details": "Save button not found"})
            print("   ❌ Save button not found")
    
    def generate_intelligent_report(self):
        """Generate intelligent test report"""
        print("\n📊 INTELLIGENT TEST REPORT")
        print("=" * 40)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "No tests run")
        
        print("\nDetailed Results:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"  {status_icon} {result['test']}: {result['details']}")
        
        print(f"\nFinal State: {self.current_state}")
        print(f"Authentication: {self.authentication_status}")

if __name__ == "__main__":
    tester = IntelligentResumeBuilderTester()
    tester.test_resume_builder_intelligently()
