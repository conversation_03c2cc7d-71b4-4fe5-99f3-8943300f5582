#!/usr/bin/env python3
"""
Comprehensive test suite to validate all critical fixes for Enhanced Testerat Framework

This script validates the fixes for:
1. Framework Detection Issues (UNKNOWN fallback)
2. Hardcoded Route Assumptions (dynamic discovery)
3. CLI Timeout Issues (proper error handling)
4. Authentication System Detection (adaptive testing)

Tests against real websites to ensure production readiness.
"""

import sys
import time
import logging
from typing import List, Dict, Any

# Setup path for imports
sys.path.insert(0, '/Users/<USER>/faafo/faafo')

from testerat_enhanced.core.testerat_enhanced import EnhancedTesterat
from testerat_enhanced.config.test_config import UniversalTestConfig, FrameworkType
from testerat_enhanced.engines.authentication import AuthenticationEngine
from testerat_enhanced.config.test_config import AuthConfig

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveFixValidator:
    """Validates all critical fixes with real-world testing"""
    
    def __init__(self):
        self.test_sites = [
            {
                'url': 'https://example.com',
                'name': 'Example.com',
                'expected_framework': 'unknown',
                'has_auth': False,
                'description': 'Simple static site'
            },
            {
                'url': 'https://httpbin.org',
                'name': 'HTTPBin',
                'expected_framework': 'unknown', 
                'has_auth': False,
                'description': 'HTTP testing service'
            }
        ]
        
        self.results = []
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run comprehensive validation of all fixes"""
        logger.info("🎯 Starting Comprehensive Fix Validation")
        logger.info("=" * 60)
        
        # Test 1: Framework Detection Robustness
        framework_results = self.test_framework_detection_robustness()
        
        # Test 2: Route Discovery System
        route_results = self.test_route_discovery_system()
        
        # Test 3: Authentication Detection
        auth_results = self.test_authentication_detection()
        
        # Test 4: Error Handling and Timeouts
        error_results = self.test_error_handling()
        
        # Test 5: End-to-End Integration
        integration_results = self.test_end_to_end_integration()
        
        # Compile final results
        all_results = {
            'framework_detection': framework_results,
            'route_discovery': route_results,
            'authentication_detection': auth_results,
            'error_handling': error_results,
            'integration': integration_results
        }
        
        return self.generate_final_report(all_results)
    
    def test_framework_detection_robustness(self) -> Dict[str, Any]:
        """Test that framework detection handles unknown frameworks gracefully"""
        logger.info("\n🔍 Testing Framework Detection Robustness...")
        
        results = {'passed': 0, 'total': 0, 'details': []}
        
        for site in self.test_sites:
            results['total'] += 1
            
            try:
                config = UniversalTestConfig()
                config.headless = True
                config.test_authentication = False
                config.test_workflows = False
                config.test_api_interactions = False
                
                testerat = EnhancedTesterat(config)
                test_results = testerat.run_comprehensive_test(site['url'], f"Framework Test - {site['name']}")
                
                detected_framework = test_results['session']['framework']
                
                # Should not crash and should return a valid framework type
                if detected_framework in ['unknown', 'vanilla_js', 'react', 'vue', 'angular', 'nextjs', 'nuxtjs', 'svelte']:
                    results['passed'] += 1
                    results['details'].append(f"✅ {site['name']}: {detected_framework}")
                    logger.info(f"✅ {site['name']}: Framework detection successful ({detected_framework})")
                else:
                    results['details'].append(f"❌ {site['name']}: Invalid framework type: {detected_framework}")
                    logger.error(f"❌ {site['name']}: Invalid framework type: {detected_framework}")
                
            except Exception as e:
                results['details'].append(f"❌ {site['name']}: Exception: {str(e)}")
                logger.error(f"❌ {site['name']}: Framework detection failed: {e}")
        
        return results
    
    def test_route_discovery_system(self) -> Dict[str, Any]:
        """Test that route discovery works without hardcoded assumptions"""
        logger.info("\n🗺️  Testing Route Discovery System...")
        
        results = {'passed': 0, 'total': 0, 'details': []}
        
        # Test route discovery configuration
        auth_config = AuthConfig()
        
        # Should start with empty routes and discovery enabled
        if len(auth_config.protected_routes) == 0 and auth_config.discover_routes:
            results['passed'] += 1
            results['details'].append("✅ Route discovery configuration correct")
            logger.info("✅ Route discovery configuration correct")
        else:
            results['details'].append("❌ Route discovery configuration incorrect")
            logger.error("❌ Route discovery configuration incorrect")
        
        results['total'] += 1
        
        # Test route discovery engine
        try:
            auth_engine = AuthenticationEngine(auth_config)
            
            # Should not crash when no routes are configured
            results['passed'] += 1
            results['details'].append("✅ Authentication engine handles empty routes")
            logger.info("✅ Authentication engine handles empty routes")
            
        except Exception as e:
            results['details'].append(f"❌ Authentication engine failed: {str(e)}")
            logger.error(f"❌ Authentication engine failed: {e}")
        
        results['total'] += 1
        
        return results
    
    def test_authentication_detection(self) -> Dict[str, Any]:
        """Test that authentication detection works adaptively"""
        logger.info("\n🔐 Testing Authentication Detection...")
        
        results = {'passed': 0, 'total': 0, 'details': []}
        
        for site in self.test_sites:
            results['total'] += 1
            
            try:
                config = UniversalTestConfig()
                config.headless = True
                config.test_workflows = False
                config.test_api_interactions = False
                
                testerat = EnhancedTesterat(config)
                test_results = testerat.run_comprehensive_test(site['url'], f"Auth Test - {site['name']}")
                
                # Should complete without crashing
                auth_tests = [r for r in test_results['all_results'] if 'auth' in r['test_name'].lower()]
                
                if auth_tests:
                    # Check if auth tests were skipped appropriately for non-auth sites
                    skipped_appropriately = any(r['status'] == 'SKIPPED' for r in auth_tests if not site['has_auth'])
                    
                    if not site['has_auth'] and skipped_appropriately:
                        results['passed'] += 1
                        results['details'].append(f"✅ {site['name']}: Auth tests skipped appropriately")
                        logger.info(f"✅ {site['name']}: Auth tests skipped appropriately")
                    elif site['has_auth']:
                        results['passed'] += 1
                        results['details'].append(f"✅ {site['name']}: Auth tests ran")
                        logger.info(f"✅ {site['name']}: Auth tests ran")
                    else:
                        results['details'].append(f"❌ {site['name']}: Auth test behavior incorrect")
                        logger.error(f"❌ {site['name']}: Auth test behavior incorrect")
                else:
                    results['details'].append(f"❌ {site['name']}: No auth tests found")
                    logger.error(f"❌ {site['name']}: No auth tests found")
                
            except Exception as e:
                results['details'].append(f"❌ {site['name']}: Exception: {str(e)}")
                logger.error(f"❌ {site['name']}: Auth detection failed: {e}")
        
        return results
    
    def test_error_handling(self) -> Dict[str, Any]:
        """Test that error handling works properly"""
        logger.info("\n⚠️  Testing Error Handling...")
        
        results = {'passed': 0, 'total': 0, 'details': []}
        
        # Test with unreachable site
        results['total'] += 1
        
        try:
            config = UniversalTestConfig()
            config.headless = True
            config.timeout = 5000  # Short timeout
            
            testerat = EnhancedTesterat(config)
            
            start_time = time.time()
            test_results = testerat.run_comprehensive_test("http://nonexistent-site-12345.com", "Error Test")
            end_time = time.time()
            
            # Should return error results instead of crashing
            if test_results and 'summary' in test_results:
                if test_results['summary']['failed'] > 0 and end_time - start_time < 30:
                    results['passed'] += 1
                    results['details'].append("✅ Error handling works - returned error results")
                    logger.info("✅ Error handling works - returned error results")
                else:
                    results['details'].append("❌ Error handling incorrect behavior")
                    logger.error("❌ Error handling incorrect behavior")
            else:
                results['details'].append("❌ Error handling failed - no results returned")
                logger.error("❌ Error handling failed - no results returned")
                
        except Exception as e:
            results['details'].append(f"❌ Error handling failed - should not raise exception: {str(e)}")
            logger.error(f"❌ Error handling failed - should not raise exception: {e}")
        
        return results
    
    def test_end_to_end_integration(self) -> Dict[str, Any]:
        """Test end-to-end integration with real sites"""
        logger.info("\n🔄 Testing End-to-End Integration...")
        
        results = {'passed': 0, 'total': 0, 'details': []}
        
        for site in self.test_sites:
            results['total'] += 1
            
            try:
                config = UniversalTestConfig()
                config.headless = True
                
                testerat = EnhancedTesterat(config)
                
                start_time = time.time()
                test_results = testerat.run_comprehensive_test(site['url'], f"Integration Test - {site['name']}")
                end_time = time.time()
                
                # Should complete successfully
                if test_results and test_results['summary']['success_rate'] >= 0:
                    execution_time = end_time - start_time
                    
                    if execution_time < 60:  # Should complete within 1 minute
                        results['passed'] += 1
                        results['details'].append(f"✅ {site['name']}: Completed in {execution_time:.1f}s, {test_results['summary']['success_rate']:.1f}% success")
                        logger.info(f"✅ {site['name']}: Integration test successful")
                    else:
                        results['details'].append(f"❌ {site['name']}: Too slow ({execution_time:.1f}s)")
                        logger.error(f"❌ {site['name']}: Integration test too slow")
                else:
                    results['details'].append(f"❌ {site['name']}: Invalid results")
                    logger.error(f"❌ {site['name']}: Integration test returned invalid results")
                
            except Exception as e:
                results['details'].append(f"❌ {site['name']}: Exception: {str(e)}")
                logger.error(f"❌ {site['name']}: Integration test failed: {e}")
        
        return results
    
    def generate_final_report(self, all_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final validation report"""
        logger.info("\n📊 COMPREHENSIVE VALIDATION REPORT")
        logger.info("=" * 60)
        
        total_passed = 0
        total_tests = 0
        
        for category, results in all_results.items():
            passed = results['passed']
            total = results['total']
            total_passed += passed
            total_tests += total
            
            logger.info(f"{category.replace('_', ' ').title()}: {passed}/{total} passed")
            for detail in results['details']:
                logger.info(f"  {detail}")
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\n🎯 OVERALL RESULTS: {total_passed}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.info("🎉 VALIDATION SUCCESSFUL - Framework ready for production!")
            status = "SUCCESS"
        else:
            logger.info("⚠️  VALIDATION INCOMPLETE - Some fixes need additional work")
            status = "NEEDS_WORK"
        
        return {
            'status': status,
            'success_rate': success_rate,
            'total_passed': total_passed,
            'total_tests': total_tests,
            'details': all_results
        }

def main():
    """Run comprehensive validation"""
    validator = ComprehensiveFixValidator()
    results = validator.run_all_tests()
    
    return 0 if results['status'] == 'SUCCESS' else 1

if __name__ == "__main__":
    sys.exit(main())
