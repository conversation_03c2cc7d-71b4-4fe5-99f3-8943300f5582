#!/usr/bin/env python3
"""
Test that all parts of testerat use consistent credentials
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
from testerat_enhanced import UniversalTestConfig, AuthenticationEngine

def test_credentials_consistency():
    """Test that all testerat components use the same credentials"""
    
    print("🔍 TESTING CREDENTIALS CONSISTENCY")
    print("=" * 50)
    
    # Test 1: Main config
    config = UniversalTestConfig()
    
    # Apply same updates as run_resume_builder_tests.py
    config.auth_config.test_users = {
        "standard": {"email": "<EMAIL>", "password": "testpassword"},
        "admin": {"email": "<EMAIL>", "password": "adminpassword"}
    }
    
    print("1️⃣ Main config credentials:")
    print(f"   Email: {config.auth_config.test_users['standard']['email']}")
    print(f"   Password: {config.auth_config.test_users['standard']['password']}")
    
    # Test 2: Authentication engine
    auth_engine = AuthenticationEngine(config.auth_config)
    
    print("2️⃣ Auth engine credentials:")
    print(f"   Email: {auth_engine.config.test_users['standard']['email']}")
    print(f"   Password: {auth_engine.config.test_users['standard']['password']}")
    
    # Test 3: Actual login test
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        
        print("3️⃣ Testing actual login...")
        page.goto("http://localhost:3001/login")
        page.wait_for_load_state('networkidle')
        
        # Test the authentication flow
        try:
            result = auth_engine.test_authentication_flow(page, "http://localhost:3001")
            print(f"   Authentication result: {result.status}")
            print(f"   Details: {result.details}")
            
            if result.status == "PASSED":
                print("   ✅ Authentication successful!")
            else:
                print("   ❌ Authentication failed!")
                print(f"   Issues: {result.recommendations}")
                
        except Exception as e:
            print(f"   ❌ Authentication error: {e}")
        
        browser.close()
    
    # Check consistency
    main_email = config.auth_config.test_users['standard']['email']
    auth_email = auth_engine.config.test_users['standard']['email']
    
    if main_email == auth_email == "<EMAIL>":
        print("\n✅ ALL CREDENTIALS ARE CONSISTENT!")
        return True
    else:
        print(f"\n❌ CREDENTIALS INCONSISTENT!")
        print(f"   Main config: {main_email}")
        print(f"   Auth engine: {auth_email}")
        return False

if __name__ == "__main__":
    success = test_credentials_consistency()
    sys.exit(0 if success else 1)
