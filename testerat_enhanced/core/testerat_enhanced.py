"""
Enhanced Testerat - Universal Web Testing Framework

Main orchestrator that integrates all testing engines to provide comprehensive
web application testing across any framework and technology stack.

Universal Support:
- Any web application framework (React, Vue, Angular, vanilla JS)
- Any authentication system (NextAuth, Auth0, custom, etc.)
- Any API backend (Node.js, Python, PHP, etc.)
- Any deployment environment (local, staging, production)

Key Features:
- Authentication Engine: Tests authenticated user experiences
- Workflow Engine: Tests complete user journeys and multi-step flows
- API Engine: Tests real form submissions and API interactions
- Enhanced Reporting: Actionable insights with fix recommendations
"""

import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from playwright.sync_api import sync_playwright, <PERSON>, Browser, BrowserContext

from ..config.test_config import UniversalTestConfig, FrameworkType, create_config_for_framework
from ..engines.authentication import AuthenticationEngine
from ..engines.workflow import WorkflowEngine
from ..engines.api import APIEngine
from ..engines.file_upload import FileUploadEngine, FileUploadConfig
from ..engines.modal_popup import Modal<PERSON>opup<PERSON>ng<PERSON>, ModalConfig
from ..reporting.enhanced_reporter import <PERSON>hanced<PERSON><PERSON>orter, ReportConfig
from ..utils.test_result import TestSuite, TestResult, TestCategory


@dataclass
class TestSession:
    """Universal test session information"""
    url: str
    framework: FrameworkType = FrameworkType.AUTO_DETECT
    app_name: str = "Unknown Application"
    test_description: str = "Comprehensive Testing"
    start_time: float = 0.0
    end_time: float = 0.0


class EnhancedTesterat:
    """
    Enhanced Testerat - Universal Web Testing Framework
    
    Main orchestrator that runs comprehensive testing across any web application.
    Integrates authentication, workflow, and API testing engines with enhanced reporting.
    """
    
    def __init__(self, config: UniversalTestConfig = None):
        self.config = config or UniversalTestConfig()
        self.logger = self._setup_logging()
        
        # Initialize Playwright
        self.playwright = None
        self.browser = None
        self.context = None
        
        # Initialize testing engines
        self.auth_engine = AuthenticationEngine(self.config.auth_config, self.logger)
        self.workflow_engine = WorkflowEngine(self.config.workflow_config, self.logger)
        self.api_engine = APIEngine(self.config.api_config, self.logger)

        # Initialize new enhanced engines
        file_upload_config = FileUploadConfig()
        self.file_upload_engine = FileUploadEngine(file_upload_config, self.logger)

        modal_config = ModalConfig()
        self.modal_popup_engine = ModalPopupEngine(modal_config, self.logger)
        
        # Initialize reporter
        report_config = ReportConfig()
        self.reporter = EnhancedReporter(report_config, self.logger)
        
        # Test session tracking
        self.current_session: Optional[TestSession] = None
        self.test_suite: Optional[TestSuite] = None
    
    def run_comprehensive_test(self, url: str, test_description: str = "Comprehensive Testing") -> Dict[str, Any]:
        """
        Run comprehensive testing suite on any web application
        
        Universal testing approach that works with any framework:
        1. Framework detection and optimization
        2. Authentication testing
        3. Workflow testing
        4. API testing
        5. Enhanced reporting
        
        Returns comprehensive test results and report file paths
        """
        self.logger.info(f"🎯 Starting Enhanced Testerat for: {url}")
        
        # Initialize test session
        self.current_session = TestSession(
            url=url,
            test_description=test_description,
            start_time=time.time()
        )
        
        self.test_suite = TestSuite(name=f"Enhanced Testerat - {url}")
        
        try:
            # Setup browser environment
            self._setup_browser()
            
            # Create page and navigate
            page = self.context.new_page()
            try:
                page.goto(url, wait_until='networkidle', timeout=self.config.timeout)
            except Exception as e:
                # Handle network failures gracefully
                if "ERR_CONNECTION_REFUSED" in str(e) or "ERR_NAME_NOT_RESOLVED" in str(e):
                    self.logger.error(f"Network connection failed: {str(e)}")
                    raise Exception(f"Unable to connect to {url}. Please check the URL and network connection.")
                else:
                    raise
            
            # Phase 1: Framework Detection and Optimization
            framework_info = self._detect_and_optimize_framework(page)
            self.current_session.framework = framework_info['framework']
            self.current_session.app_name = framework_info.get('app_name', 'Unknown Application')
            
            # Phase 2: Authentication Testing
            if self.config.test_authentication:
                self.logger.info("🔐 Running Authentication Tests")
                auth_results = self.auth_engine.run_comprehensive_auth_tests(page, url)
                for result in auth_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Phase 3: Workflow Testing
            if self.config.test_workflows:
                self.logger.info("🔄 Running Workflow Tests")
                workflow_results = self.workflow_engine.run_comprehensive_workflow_tests(page)
                for result in workflow_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Phase 4: API Testing
            if self.config.test_api_interactions:
                self.logger.info("🌐 Running API Tests")
                api_results = self.api_engine.run_comprehensive_api_tests(page, url)
                for result in api_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)

            # Phase 5: File Upload Testing
            if self.config.test_file_uploads:
                self.logger.info("📁 Running File Upload Tests")
                file_upload_results = self.file_upload_engine.test_file_uploads(page, url)
                self._add_engine_results_to_suite(file_upload_results, framework_info['framework'].value)

            # Phase 6: Modal and Popup Testing
            if self.config.test_modals_popups:
                self.logger.info("🔲 Running Modal and Popup Tests")
                modal_results = self.modal_popup_engine.test_modals_and_popups(page, url)
                self._add_engine_results_to_suite(modal_results, framework_info['framework'].value)

            # Phase 7: Additional Testing (Security, Accessibility, Performance)
            if self.config.test_security or self.config.test_accessibility or self.config.test_performance:
                additional_results = self._run_additional_tests(page)
                for result in additional_results:
                    result.framework_detected = framework_info['framework'].value
                    self.test_suite.add_result(result)
            
            # Finalize session
            self.current_session.end_time = time.time()
            self.test_suite.end_time = datetime.now()
            self.test_suite.total_execution_time = self.current_session.end_time - self.current_session.start_time
            
            # Phase 6: Generate Enhanced Reports
            self.logger.info("📊 Generating Enhanced Reports")
            report_files = self.reporter.generate_comprehensive_report(
                self.test_suite,
                framework_info,
                {'name': self.current_session.app_name, 'url': url}
            )
            
            # Cleanup
            page.close()
            self._cleanup_browser()
            
            # Return comprehensive results
            return {
                'session': {
                    'url': url,
                    'framework': framework_info['framework'].value,
                    'app_name': self.current_session.app_name,
                    'execution_time': self.test_suite.total_execution_time
                },
                'summary': self.test_suite.get_summary(),
                'critical_issues': [result.to_dict() for result in self.test_suite.get_critical_issues()],
                'all_results': [result.to_dict() for result in self.test_suite.results],
                'report_files': report_files,
                'recommendations': self._get_top_recommendations()
            }
            
        except Exception as e:
            self.logger.error(f"Testing failed: {str(e)}")
            self._cleanup_browser()

            # Add error result to test suite for reporting
            error_result = TestResult(
                test_name="test_execution_error",
                status="FAILED",
                details=f"Test execution failed: {str(e)}",
                severity="CRITICAL",
                recommendations=["Check URL accessibility and network connectivity", "Verify site is responsive"],
                execution_time=0.0
            )
            if hasattr(self, 'test_suite') and self.test_suite:
                self.test_suite.add_result(error_result)

            # Return error results instead of raising
            return {
                'session': {
                    'url': url,
                    'framework': 'unknown',
                    'app_name': 'Failed to Load',
                    'execution_time': 0.0
                },
                'summary': {'total_tests': 1, 'passed': 0, 'failed': 1, 'success_rate': 0.0},
                'critical_issues': [error_result.to_dict()],
                'all_results': [error_result.to_dict()],
                'report_files': [],
                'recommendations': ["Check URL accessibility and network connectivity"]
            }
    
    def _setup_browser(self):
        """Setup Playwright browser environment"""
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(
            headless=self.config.headless,
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        self.context = self.browser.new_context(
            viewport={'width': self.config.viewport_width, 'height': self.config.viewport_height}
        )
    
    def _cleanup_browser(self):
        """Cleanup browser resources"""
        try:
            if self.context:
                self.context.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.debug(f"Error during browser cleanup: {e}")

    def _check_site_accessibility(self, url: str) -> bool:
        """
        Quick check if site is accessible before running full tests

        Returns True if site responds, False otherwise
        """
        try:
            import requests
            response = requests.get(url, timeout=10, allow_redirects=True)
            return response.status_code < 500
        except Exception as e:
            self.logger.debug(f"Site accessibility check failed: {e}")
            return False

    def _add_error_result(self, test_name: str, error_message: str):
        """Add an error result to the test suite"""
        error_result = TestResult(
            test_name=test_name,
            status="FAILED",
            details=f"Test failed: {error_message}",
            severity="MEDIUM",
            recommendations=["Review test configuration and site accessibility"],
            execution_time=0.0
        )
        if hasattr(self, 'test_suite') and self.test_suite:
            self.test_suite.add_result(error_result)
    
    def _detect_and_optimize_framework(self, page: Page) -> Dict[str, Any]:
        """
        Enhanced framework detection and optimization

        Comprehensive framework detection using multiple detection methods:
        1. DOM indicators and selectors
        2. JavaScript global variables
        3. Network requests and static assets
        4. Meta tags and build artifacts
        5. URL patterns and routing
        """
        self.logger.info("🔍 Detecting web framework with enhanced analysis")

        framework_info = {
            'framework': FrameworkType.VANILLA_JS,
            'app_name': 'Unknown Application',
            'version': None,
            'features': [],
            'confidence': 0.0,
            'detection_methods': []
        }

        try:
            # Get page content for analysis
            page_content = page.content()
            page_title = page.title()
            page_url = page.url

            # Method 1: DOM-based detection
            dom_framework = self._detect_framework_by_dom(page, page_content)
            if dom_framework:
                framework_info.update(dom_framework)
                framework_info['detection_methods'].append('DOM analysis')

            # Method 2: JavaScript globals detection
            js_framework = self._detect_framework_by_javascript(page)
            if js_framework and js_framework['confidence'] > framework_info['confidence']:
                framework_info.update(js_framework)
                framework_info['detection_methods'].append('JavaScript globals')

            # Method 3: Network requests and assets
            network_framework = self._detect_framework_by_network(page)
            if network_framework and network_framework['confidence'] > framework_info['confidence']:
                framework_info.update(network_framework)
                framework_info['detection_methods'].append('Network analysis')

            # Method 4: Meta tags and build info
            meta_framework = self._detect_framework_by_meta(page, page_content)
            if meta_framework and meta_framework['confidence'] > framework_info['confidence']:
                framework_info.update(meta_framework)
                framework_info['detection_methods'].append('Meta tags')

            # Extract app name from multiple sources
            app_name = self._extract_app_name(page, page_title)
            if app_name:
                framework_info['app_name'] = app_name

            # Detect comprehensive features
            features = self._detect_application_features(page)
            framework_info['features'] = features

            # Log detection results
            self.logger.info(f"Framework detected: {framework_info['framework'].value} "
                           f"(confidence: {framework_info['confidence']:.2f}, "
                           f"methods: {', '.join(framework_info['detection_methods'])})")

            # Optimize configuration for detected framework
            if framework_info['framework'] != FrameworkType.AUTO_DETECT:
                optimized_config = create_config_for_framework(
                    framework_info['framework'],
                    page.url
                )
                # Update current config with optimizations
                self.config.auth_config = optimized_config.auth_config
                self.config.workflow_config = optimized_config.workflow_config
                self.config.api_config = optimized_config.api_config

                self.logger.info(f"Applied {framework_info['framework'].value} optimizations")

        except Exception as e:
            self.logger.warning(f"Framework detection failed: {e}")

        return framework_info

    def _detect_framework_by_dom(self, page: Page, page_content: str) -> Optional[Dict[str, Any]]:
        """
        Detect framework using DOM indicators with robust fallback handling

        Enhanced detection that:
        1. Uses multiple detection methods per framework
        2. Handles false positives with confidence scoring
        3. Provides graceful fallback to UNKNOWN framework
        """
        framework_scores = {}

        # Enhanced DOM detection patterns with fallback patterns
        detection_patterns = {
            FrameworkType.REACT: [
                # High confidence patterns
                ("[data-reactroot]", 0.9),
                ("__REACT_DEVTOOLS_GLOBAL_HOOK__", 0.8),
                ("_reactInternalFiber", 0.8),
                # Medium confidence patterns
                ("react-dom", 0.6),
                ("React", 0.4),  # Lower confidence for generic text
            ],
            FrameworkType.VUE: [
                # High confidence patterns
                ("__VUE__", 0.9),
                ("[data-app]", 0.7),  # Lowered from 0.8 - too generic
                ("v-if", 0.7),
                ("v-for", 0.7),
                # Medium confidence patterns
                ("#app", 0.4),  # Very generic, lower confidence
                ("Vue", 0.3),
            ],
            FrameworkType.ANGULAR: [
                # High confidence patterns
                ("[ng-version]", 0.9),
                ("app-root", 0.8),
                ("ng-container", 0.7),
                # Medium confidence patterns
                ("ng-", 0.5),  # Lowered - could be false positive
                ("angular", 0.4),
            ],
            FrameworkType.NEXTJS: [
                # High confidence patterns
                ("__NEXT_DATA__", 0.9),
                ("#__next", 0.9),
                ("_next/static", 0.8),
                ("next/", 0.6),
            ],
            FrameworkType.NUXTJS: [
                # High confidence patterns
                ("__NUXT__", 0.9),
                ("#__nuxt", 0.9),
                ("_nuxt/", 0.8),
                ("nuxt", 0.5),
            ],
            FrameworkType.SVELTE: [
                # High confidence patterns
                ("[data-svelte]", 0.9),
                ("svelte-", 0.7),
                ("svelte", 0.5),
            ]
        }

        for framework, patterns in detection_patterns.items():
            score = 0.0
            detected_patterns = []

            for pattern, weight in patterns:
                pattern_found = False

                # Check if pattern is in page content first
                if pattern in page_content:
                    pattern_found = True
                else:
                    # Only try CSS selector if pattern looks like a valid selector
                    try:
                        if pattern.startswith('[') or pattern.startswith('#') or pattern.startswith('.'):
                            if page.query_selector(pattern):
                                pattern_found = True
                    except Exception:
                        # Skip invalid CSS selectors
                        pass

                if pattern_found:
                    score += weight
                    detected_patterns.append(pattern)

            if score > 0:
                framework_scores[framework] = {
                    'score': score,
                    'patterns': detected_patterns
                }

        if framework_scores:
            best_framework = max(framework_scores, key=lambda x: framework_scores[x]['score'])
            best_score = framework_scores[best_framework]['score']

            # Only return high-confidence detections
            if best_score >= 0.6:
                return {
                    'framework': best_framework,
                    'confidence': min(best_score, 1.0),
                    'detected_patterns': framework_scores[best_framework]['patterns']
                }

        # Return UNKNOWN framework with low confidence instead of None
        return {
            'framework': FrameworkType.UNKNOWN,
            'confidence': 0.1,
            'detected_patterns': []
        }

    def _detect_framework_by_javascript(self, page: Page) -> Optional[Dict[str, Any]]:
        """
        Detect framework using JavaScript globals with robust error handling

        Enhanced detection that:
        1. Handles JavaScript execution failures gracefully
        2. Uses multiple detection methods per framework
        3. Returns UNKNOWN instead of None for failed detection
        """
        try:
            js_detection = page.evaluate("""
                () => {
                    try {
                        const detections = [];
                        const detected_globals = [];

                        // React detection - multiple methods
                        if (window.React) {
                            detections.push({framework: 'react', confidence: 0.9, method: 'React global'});
                            detected_globals.push('React');
                        } else if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
                            detections.push({framework: 'react', confidence: 0.8, method: 'React DevTools'});
                            detected_globals.push('__REACT_DEVTOOLS_GLOBAL_HOOK__');
                        }

                        // Vue detection - multiple methods
                        if (window.Vue) {
                            detections.push({framework: 'vue', confidence: 0.9, method: 'Vue global'});
                            detected_globals.push('Vue');
                        } else if (window.__VUE__) {
                            detections.push({framework: 'vue', confidence: 0.8, method: '__VUE__'});
                            detected_globals.push('__VUE__');
                        }

                        // Angular detection - multiple methods
                        if (window.ng) {
                            detections.push({framework: 'angular', confidence: 0.9, method: 'ng global'});
                            detected_globals.push('ng');
                        } else if (window.angular) {
                            detections.push({framework: 'angular', confidence: 0.8, method: 'angular global'});
                            detected_globals.push('angular');
                        } else if (window.getAllAngularRootElements) {
                            detections.push({framework: 'angular', confidence: 0.7, method: 'getAllAngularRootElements'});
                            detected_globals.push('getAllAngularRootElements');
                        }

                        // Next.js detection - multiple methods
                        if (window.__NEXT_DATA__) {
                            detections.push({framework: 'nextjs', confidence: 0.9, method: '__NEXT_DATA__'});
                            detected_globals.push('__NEXT_DATA__');
                        } else if (window.next) {
                            detections.push({framework: 'nextjs', confidence: 0.7, method: 'next global'});
                            detected_globals.push('next');
                        }

                        // Nuxt.js detection - multiple methods
                        if (window.__NUXT__) {
                            detections.push({framework: 'nuxtjs', confidence: 0.9, method: '__NUXT__'});
                            detected_globals.push('__NUXT__');
                        } else if (window.$nuxt) {
                            detections.push({framework: 'nuxtjs', confidence: 0.8, method: '$nuxt'});
                            detected_globals.push('$nuxt');
                        }

                        // Svelte detection - multiple methods
                        if (window.__SVELTE__) {
                            detections.push({framework: 'svelte', confidence: 0.8, method: '__SVELTE__'});
                            detected_globals.push('__SVELTE__');
                        }

                        // Return best detection or null
                        if (detections.length > 0) {
                            const best = detections.reduce((a, b) => a.confidence > b.confidence ? a : b);
                            best.detected_globals = detected_globals;
                            return best;
                        }

                        return null;
                    } catch (error) {
                        return {error: error.message};
                    }
                }
            """)

            if js_detection:
                if js_detection.get('error'):
                    self.logger.debug(f"JavaScript detection error: {js_detection['error']}")
                    return {
                        'framework': FrameworkType.UNKNOWN,
                        'confidence': 0.1,
                        'detection_method': 'javascript_failed'
                    }

                framework_map = {
                    'react': FrameworkType.REACT,
                    'vue': FrameworkType.VUE,
                    'angular': FrameworkType.ANGULAR,
                    'nextjs': FrameworkType.NEXTJS,
                    'nuxtjs': FrameworkType.NUXTJS,
                    'svelte': FrameworkType.SVELTE
                }

                framework = framework_map.get(js_detection['framework'])
                if framework:
                    return {
                        'framework': framework,
                        'confidence': js_detection['confidence'],
                        'detection_method': js_detection.get('method', 'javascript'),
                        'detected_globals': js_detection.get('detected_globals', [])
                    }

        except Exception as e:
            self.logger.debug(f"JavaScript detection failed: {e}")

        # Return UNKNOWN instead of None
        return {
            'framework': FrameworkType.UNKNOWN,
            'confidence': 0.1,
            'detection_method': 'javascript_unavailable'
        }

    def _detect_framework_by_network(self, page: Page) -> Optional[Dict[str, Any]]:
        """Detect framework using network requests"""
        # This would require monitoring network requests during page load
        # For now, return None - could be enhanced with request monitoring
        return None

    def _detect_framework_by_meta(self, page: Page, page_content: str) -> Optional[Dict[str, Any]]:
        """Detect framework using meta tags and build info"""
        try:
            # Check for framework-specific meta tags
            meta_generator = page.query_selector('meta[name="generator"]')
            if meta_generator:
                content = meta_generator.get_attribute('content') or ""
                content_lower = content.lower()

                if 'next.js' in content_lower:
                    return {'framework': FrameworkType.NEXTJS, 'confidence': 0.8}
                elif 'nuxt' in content_lower:
                    return {'framework': FrameworkType.NUXTJS, 'confidence': 0.8}
                elif 'angular' in content_lower:
                    return {'framework': FrameworkType.ANGULAR, 'confidence': 0.7}

            # Check for build artifacts in HTML comments
            if '<!-- Built with Next.js -->' in page_content:
                return {'framework': FrameworkType.NEXTJS, 'confidence': 0.9}
            elif '<!-- Generated by Nuxt.js -->' in page_content:
                return {'framework': FrameworkType.NUXTJS, 'confidence': 0.9}

        except Exception as e:
            self.logger.debug(f"Meta detection failed: {e}")

        return None

    def _extract_app_name(self, page: Page, page_title: str) -> str:
        """Extract application name from multiple sources"""
        # Try page title first
        if page_title and page_title != "":
            return page_title

        # Try meta tags
        try:
            meta_title = page.query_selector('meta[property="og:title"]')
            if meta_title:
                title = meta_title.get_attribute('content')
                if title:
                    return title

            meta_app_name = page.query_selector('meta[name="application-name"]')
            if meta_app_name:
                app_name = meta_app_name.get_attribute('content')
                if app_name:
                    return app_name

        except Exception:
            pass

        return "Unknown Application"

    def _detect_application_features(self, page: Page) -> List[str]:
        """Detect comprehensive application features"""
        features = []

        try:
            # Testing features
            if page.query_selector('[data-testid]'):
                features.append('test-ids')
            if page.query_selector('[data-cy]'):
                features.append('cypress-testing')

            # UI features
            if page.query_selector('.loading, .spinner, [data-loading]'):
                features.append('loading-states')
            if page.query_selector('form'):
                features.append('forms')
            if page.query_selector('[role="button"], button'):
                features.append('interactive-elements')
            if page.query_selector('.modal, [role="dialog"]'):
                features.append('modals')

            # Authentication features
            if page.query_selector('.login, .signin, [data-auth]'):
                features.append('authentication')
            if page.query_selector('.user-menu, .profile'):
                features.append('user-management')

            # Navigation features
            if page.query_selector('nav, .navigation, [role="navigation"]'):
                features.append('navigation')
            if page.query_selector('.breadcrumb, [aria-label*="breadcrumb"]'):
                features.append('breadcrumbs')

            # Data features
            if page.query_selector('table, .table, [role="table"]'):
                features.append('data-tables')
            if page.query_selector('.chart, .graph, canvas'):
                features.append('data-visualization')

            # Accessibility features
            if page.query_selector('[aria-label], [aria-describedby]'):
                features.append('accessibility-labels')
            if page.query_selector('[role]'):
                features.append('aria-roles')

        except Exception as e:
            self.logger.debug(f"Feature detection failed: {e}")

        return features

    def _run_additional_tests(self, page: Page) -> List[TestResult]:
        """Run additional tests (security, accessibility, performance)"""
        additional_results = []
        
        # Basic security tests
        if self.config.test_security:
            security_result = self._run_basic_security_tests(page)
            if security_result:
                additional_results.append(security_result)
        
        # Basic accessibility tests
        if self.config.test_accessibility:
            accessibility_result = self._run_basic_accessibility_tests(page)
            if accessibility_result:
                additional_results.append(accessibility_result)
        
        # Basic performance tests
        if self.config.test_performance:
            performance_result = self._run_basic_performance_tests(page)
            if performance_result:
                additional_results.append(performance_result)
        
        return additional_results
    
    def _run_basic_security_tests(self, page: Page) -> Optional[TestResult]:
        """Run basic security tests"""
        issues = []
        recommendations = []
        
        try:
            # Check for HTTPS
            if not page.url.startswith('https://') and not page.url.startswith('http://localhost'):
                issues.append("Application not using HTTPS")
                recommendations.append("Implement HTTPS for secure communication")
            
            # Check for security headers (basic check)
            # This would require more advanced implementation
            
        except Exception as e:
            issues.append(f"Security test failed: {str(e)}")
        
        if issues:
            return TestResult(
                test_name="basic_security",
                status="FAILED",
                details="; ".join(issues),
                severity="MEDIUM",
                recommendations=recommendations,
                category=TestCategory.SECURITY.value
            )
        else:
            return TestResult(
                test_name="basic_security",
                status="PASSED",
                details="Basic security checks passed",
                severity="LOW",
                recommendations=["Security basics look good - consider comprehensive security audit"],
                category=TestCategory.SECURITY.value
            )
    
    def _run_basic_accessibility_tests(self, page: Page) -> Optional[TestResult]:
        """Run basic accessibility tests"""
        issues = []
        recommendations = []
        
        try:
            # Check for images without alt text
            images_without_alt = page.query_selector_all('img:not([alt])')
            if images_without_alt:
                issues.append(f"{len(images_without_alt)} images missing alt text")
                recommendations.append("Add alt attributes to all images")
            
            # Check for form labels
            inputs_without_labels = page.query_selector_all('input:not([aria-label]):not([aria-labelledby])')
            unlabeled_count = 0
            for inp in inputs_without_labels:
                input_id = inp.get_attribute('id')
                if not input_id or not page.query_selector(f'label[for="{input_id}"]'):
                    unlabeled_count += 1
            
            if unlabeled_count > 0:
                issues.append(f"{unlabeled_count} form inputs without proper labels")
                recommendations.append("Associate labels with form inputs")
            
        except Exception as e:
            issues.append(f"Accessibility test failed: {str(e)}")
        
        if issues:
            return TestResult(
                test_name="basic_accessibility",
                status="FAILED",
                details="; ".join(issues),
                severity="MEDIUM",
                recommendations=recommendations,
                category=TestCategory.ACCESSIBILITY.value
            )
        else:
            return TestResult(
                test_name="basic_accessibility",
                status="PASSED",
                details="Basic accessibility checks passed",
                severity="LOW",
                recommendations=["Accessibility basics look good - consider comprehensive accessibility audit"],
                category=TestCategory.ACCESSIBILITY.value
            )
    
    def _run_basic_performance_tests(self, page: Page) -> Optional[TestResult]:
        """
        Run basic performance tests

        IMPORTANT: This is a basic implementation that provides minimal performance insights.
        For comprehensive performance testing, use dedicated tools like Lighthouse, WebPageTest, or GTmetrix.

        This function provides:
        - Basic page load timing
        - Resource count analysis
        - Simple performance recommendations
        """
        issues = []
        recommendations = []

        try:
            # Get basic performance metrics
            performance_metrics = page.evaluate("""
                () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const resources = performance.getEntriesByType('resource');

                    return {
                        loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
                        domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
                        resourceCount: resources.length,
                        largeResources: resources.filter(r => r.transferSize > 1000000).length, // > 1MB
                        slowResources: resources.filter(r => r.duration > 3000).length // > 3s
                    };
                }
            """)

            # Analyze metrics and provide recommendations
            if performance_metrics['loadTime'] > 3000:  # > 3 seconds
                issues.append(f"Slow page load time: {performance_metrics['loadTime']:.0f}ms")
                recommendations.append("Optimize page load time - consider code splitting, image optimization, or CDN")

            if performance_metrics['resourceCount'] > 100:
                issues.append(f"High resource count: {performance_metrics['resourceCount']} resources")
                recommendations.append("Reduce number of HTTP requests - bundle resources or use HTTP/2")

            if performance_metrics['largeResources'] > 0:
                issues.append(f"Large resources detected: {performance_metrics['largeResources']} resources > 1MB")
                recommendations.append("Optimize large resources - compress images, minify JS/CSS")

            if performance_metrics['slowResources'] > 0:
                issues.append(f"Slow loading resources: {performance_metrics['slowResources']} resources > 3s")
                recommendations.append("Investigate slow resources - check server response times")

            # Return result with issues if found
            if issues:
                return TestResult(
                    test_name="basic_performance",
                    status="WARNING",
                    details="; ".join(issues),
                    severity="MEDIUM",
                    recommendations=recommendations,
                    category=TestCategory.PERFORMANCE.value
                )
            else:
                # Return success result if no issues found
                return TestResult(
                    test_name="basic_performance",
                    status="PASSED",
                    details="Performance metrics within acceptable ranges",
                    severity="LOW",
                    recommendations=["Performance looks good - continue monitoring"],
                    category=TestCategory.PERFORMANCE.value
                )

        except Exception as e:
            issues.append(f"Performance test failed: {str(e)}")
            recommendations.append("Enable JavaScript and ensure page is fully loaded for performance testing")

            return TestResult(
                test_name="basic_performance",
                status="FAILED",
                details="; ".join(issues),
                severity="LOW",
                recommendations=recommendations,
                category=TestCategory.PERFORMANCE.value
            )
    
    def _get_top_recommendations(self) -> List[str]:
        """Get top recommendations from all test results"""
        all_recommendations = set()
        for result in self.test_suite.results:
            all_recommendations.update(result.recommendations)
        
        # Prioritize critical recommendations
        critical_recs = [rec for rec in all_recommendations if 'critical' in rec.lower() or 'fix' in rec.lower()]
        other_recs = [rec for rec in all_recommendations if rec not in critical_recs]
        
        return list(critical_recs)[:5] + list(other_recs)[:10]
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO if self.config.detailed_logging else logging.WARNING,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('testerat_enhanced.log'),
                logging.StreamHandler()
            ]
        )
        return logging.getLogger(__name__)

    def _add_engine_results_to_suite(self, engine_results: Dict[str, Any], framework: str):
        """Convert engine results to TestResult objects and add to test suite"""
        try:
            from ..utils.test_result import TestResult, TestStatus, TestSeverity, TestCategory

            # Add individual test results
            for test_detail in engine_results.get('test_details', []):
                test_result = TestResult(
                    test_name=test_detail.get('test_name', 'unknown_test'),
                    status=TestStatus.PASSED if test_detail.get('passed', False) else TestStatus.FAILED,
                    severity=TestSeverity.HIGH if test_detail.get('severity') == 'HIGH' else
                            TestSeverity.MEDIUM if test_detail.get('severity') == 'MEDIUM' else TestSeverity.LOW,
                    category=TestCategory.FUNCTIONALITY,
                    details=test_detail.get('details', ''),
                    recommendations=test_detail.get('recommendations', []),
                    execution_time=0.0,
                    framework_detected=framework
                )
                self.test_suite.add_result(test_result)

            # Add critical issues as separate results
            for critical_issue in engine_results.get('critical_issues', []):
                test_result = TestResult(
                    test_name=critical_issue.get('test_name', 'critical_issue'),
                    status=TestStatus.FAILED,
                    severity=TestSeverity.HIGH,
                    category=TestCategory.FUNCTIONALITY,
                    details=critical_issue.get('details', ''),
                    recommendations=critical_issue.get('recommendations', []),
                    execution_time=0.0,
                    framework_detected=framework
                )
                self.test_suite.add_result(test_result)

        except Exception as e:
            self.logger.error(f"❌ Failed to add engine results to test suite: {str(e)}")

    def cleanup(self):
        """Clean up resources used by engines"""
        try:
            if hasattr(self, 'file_upload_engine'):
                self.file_upload_engine.cleanup()
        except Exception as e:
            self.logger.warning(f"⚠️ Cleanup warning: {str(e)}")

    def __del__(self):
        """Ensure cleanup on object destruction"""
        self.cleanup()
