#!/usr/bin/env python3
"""
Test script to validate the critical fixes for Enhanced Testerat Framework

This script tests the fixes for:
1. Framework Detection Issues
2. Hardcoded Route Assumptions  
3. CLI Timeout Issues
4. Error Handling Improvements
"""

import sys
import time
from testerat_enhanced.core.testerat_enhanced import EnhancedTesterat
from testerat_enhanced.config.test_config import UniversalTestConfig, FrameworkType

def test_framework_detection():
    """Test that framework detection works with fallback to UNKNOWN"""
    print("🔍 Testing Framework Detection...")
    
    config = UniversalTestConfig()
    config.headless = True
    config.test_authentication = False
    config.test_workflows = False
    config.test_api_interactions = False
    
    testerat = EnhancedTesterat(config)
    
    # Test with a simple site
    try:
        results = testerat.run_comprehensive_test("https://example.com", "Framework Detection Test")
        framework = results['session']['framework']
        print(f"✅ Framework detection successful: {framework}")
        
        # Should not crash even if framework is unknown
        if framework in ['unknown', 'vanilla_js']:
            print("✅ Graceful fallback to unknown framework working")
        
        return True
    except Exception as e:
        print(f"❌ Framework detection failed: {e}")
        return False

def test_route_discovery():
    """Test that route discovery doesn't fail on hardcoded assumptions"""
    print("🔍 Testing Route Discovery...")
    
    from testerat_enhanced.engines.authentication import AuthenticationEngine
    from testerat_enhanced.config.test_config import AuthConfig
    
    # Test with route discovery enabled
    auth_config = AuthConfig()
    auth_config.discover_routes = True
    auth_config.protected_routes = []  # Start with empty routes
    
    auth_engine = AuthenticationEngine(auth_config)
    
    # This should not fail even with no hardcoded routes
    print("✅ Route discovery configuration working")
    return True

def test_error_handling():
    """Test that error handling works for unreachable sites"""
    print("🔍 Testing Error Handling...")
    
    config = UniversalTestConfig()
    config.headless = True
    config.test_authentication = False
    config.test_workflows = False
    config.test_api_interactions = False
    
    testerat = EnhancedTesterat(config)
    
    # Test with unreachable site
    try:
        results = testerat.run_comprehensive_test("http://nonexistent-site-12345.com", "Error Handling Test")
        
        # Should return error results instead of crashing
        if results['summary']['failed'] > 0:
            print("✅ Error handling working - returned error results instead of crashing")
            return True
        else:
            print("❌ Error handling not working - should have failed")
            return False
            
    except Exception as e:
        print(f"❌ Error handling failed - should not raise exception: {e}")
        return False

def test_timeout_protection():
    """Test that timeout protection works"""
    print("🔍 Testing Timeout Protection...")
    
    config = UniversalTestConfig()
    config.headless = True
    config.timeout = 5000  # 5 seconds - very short for testing
    
    testerat = EnhancedTesterat(config)
    
    start_time = time.time()
    try:
        # Test with a slow site (should timeout gracefully)
        results = testerat.run_comprehensive_test("https://httpbin.org/delay/10", "Timeout Test")
        end_time = time.time()
        
        # Should complete within reasonable time even if site is slow
        if end_time - start_time < 30:  # Should not take more than 30 seconds
            print("✅ Timeout protection working")
            return True
        else:
            print("❌ Timeout protection not working - took too long")
            return False
            
    except Exception as e:
        end_time = time.time()
        if end_time - start_time < 30:
            print("✅ Timeout protection working - failed gracefully")
            return True
        else:
            print(f"❌ Timeout protection failed: {e}")
            return False

def main():
    """Run all validation tests"""
    print("🎯 Enhanced Testerat - Critical Fixes Validation")
    print("=" * 60)
    
    tests = [
        ("Framework Detection", test_framework_detection),
        ("Route Discovery", test_route_discovery), 
        ("Error Handling", test_error_handling),
        ("Timeout Protection", test_timeout_protection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} Test PASSED")
            else:
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            print(f"❌ {test_name} Test CRASHED: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All critical fixes validated successfully!")
        return 0
    else:
        print("⚠️  Some fixes need additional work")
        return 1

if __name__ == "__main__":
    sys.exit(main())
