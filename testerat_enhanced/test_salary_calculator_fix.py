#!/usr/bin/env python3
"""
Test Salary Calculator Fix
Quick test to verify the CSRF fix works
"""

from playwright.sync_api import sync_playwright
import time

def test_salary_calculator():
    print("🧪 TESTING SALARY CALCULATOR FIX")
    print("=" * 50)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Login first
            print("🔐 Logging in...")
            page.goto("http://localhost:3001/login")
            page.wait_for_load_state('networkidle')
            
            email_input = page.query_selector("input[id='email']")
            password_input = page.query_selector("input[id='password']")
            submit_btn = page.query_selector("button[type='submit']")
            
            if all([email_input, password_input, submit_btn]):
                email_input.fill("<EMAIL>")
                password_input.fill("testpassword")
                submit_btn.click()
                page.wait_for_load_state('networkidle')
                time.sleep(2)
                print("✅ Login successful")
            else:
                print("❌ Login elements not found")
                return
            
            # Navigate to salary calculator
            print("📊 Navigating to salary calculator...")
            page.goto("http://localhost:3001/tools/salary-calculator")
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Check for console errors
            console_messages = []
            page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
            
            # Wait for page to load
            page.wait_for_selector('[data-testid="career-path-trigger"]', timeout=10000)
            print("✅ Page loaded successfully")
            
            # Test career path selection
            print("🎯 Testing career path selection...")
            career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
            if career_trigger:
                career_trigger.click()
                time.sleep(1000)  # Wait for dropdown
                
                # Try to select an option
                software_dev_option = page.query_selector('text="Software Developer"')
                if software_dev_option:
                    software_dev_option.click()
                    print("✅ Career path selected: Software Developer")
                else:
                    # Try alternative approach
                    page.keyboard.press('ArrowDown')
                    page.keyboard.press('Enter')
                    print("⚠️ Career path selected via keyboard")
                
                time.sleep(1000)
            else:
                print("❌ Career path trigger not found")
                return
            
            # Test experience level
            print("🎯 Testing experience level...")
            exp_trigger = page.query_selector('[data-testid="experience-trigger"]')
            if exp_trigger:
                exp_trigger.click()
                time.sleep(1000)
                
                mid_level_option = page.query_selector('text="Mid Level"')
                if mid_level_option:
                    mid_level_option.click()
                    print("✅ Experience level selected: Mid Level")
                else:
                    page.keyboard.press('ArrowDown')
                    page.keyboard.press('ArrowDown')
                    page.keyboard.press('Enter')
                    print("⚠️ Experience level selected via keyboard")
                
                time.sleep(1000)
            
            # Test location
            print("🎯 Testing location...")
            location_trigger = page.query_selector('[data-testid="location-trigger"]')
            if location_trigger:
                location_trigger.click()
                time.sleep(1000)
                
                sf_option = page.query_selector('text="San Francisco"')
                if sf_option:
                    sf_option.click()
                    print("✅ Location selected: San Francisco")
                else:
                    page.keyboard.press('ArrowDown')
                    page.keyboard.press('Enter')
                    print("⚠️ Location selected via keyboard")
                
                time.sleep(1000)
            
            # Add a skill
            print("🎯 Testing skills input...")
            skills_input = page.query_selector('input[placeholder*="skill"]')
            if skills_input:
                skills_input.fill("JavaScript")
                page.keyboard.press('Enter')
                time.sleep(1000)
                
                # Check if skill was added
                skill_badge = page.query_selector('text="JavaScript"')
                if skill_badge:
                    print("✅ Skill added: JavaScript")
                else:
                    print("⚠️ Skill input attempted but badge not visible")
            
            # Test calculate button
            print("🎯 Testing calculate button...")
            calculate_btn = page.query_selector('button:has-text("Calculate Salary")')
            if calculate_btn:
                # Check button state
                is_disabled = calculate_btn.get_attribute('disabled')
                print(f"   Button disabled: {is_disabled is not None}")
                
                # Click the button
                calculate_btn.click()
                print("✅ Calculate button clicked")
                
                # Wait for results
                print("⏳ Waiting for calculation results...")
                time.sleep(8000)  # Wait up to 8 seconds
                
                # Check for results
                salary_estimate = page.query_selector('text="Salary Estimate"')
                dollar_amounts = page.query_selector_all('text*="$"')
                
                if salary_estimate:
                    print("✅ Results displayed - Salary Estimate found")
                elif len(dollar_amounts) > 2:
                    print(f"✅ Results displayed - {len(dollar_amounts)} dollar amounts found")
                else:
                    print("❌ No clear results visible")
                    
                    # Check for error messages
                    error_messages = page.query_selector_all('text*="error", text*="Error", text*="failed", text*="Failed"')
                    if error_messages:
                        print(f"❌ Error messages found: {len(error_messages)}")
                        for msg in error_messages[:3]:  # Show first 3 errors
                            print(f"   Error: {msg.text_content()}")
            else:
                print("❌ Calculate button not found")
            
            # Check console for errors
            print("\n📋 Console Messages:")
            csrf_errors = [msg for msg in console_messages if 'csrf' in msg.lower() or 'token' in msg.lower()]
            if csrf_errors:
                print("❌ CSRF-related errors found:")
                for error in csrf_errors:
                    print(f"   {error}")
            else:
                print("✅ No CSRF-related errors")
            
            # Show other errors
            other_errors = [msg for msg in console_messages if 'error' in msg.lower() and 'csrf' not in msg.lower()]
            if other_errors:
                print("⚠️ Other errors found:")
                for error in other_errors[:5]:  # Show first 5
                    print(f"   {error}")
            
            print("\n🎉 Test completed! Check results above.")
            
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
        finally:
            print("\n🔍 Keeping browser open for inspection...")
            time.sleep(10)
            browser.close()

if __name__ == "__main__":
    test_salary_calculator()
