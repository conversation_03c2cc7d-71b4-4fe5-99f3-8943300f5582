#!/usr/bin/env python3
"""
Validation Tests for Enhanced Testerat Fixes

Tests to validate that the architectural fixes work properly and don't introduce new issues.
"""

import sys
import os
import unittest
import logging
from unittest.mock import Mock, patch, MagicMock

# Add the parent directory to the path to import testerat_enhanced as a package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

try:
    from testerat_enhanced.config.test_config import UniversalTestConfig, FrameworkType, create_config_for_framework
    from testerat_enhanced.utils.error_handling import ErrorHandler, ErrorSeverity, safe_dom_operation
    from testerat_enhanced.engines.authentication import AuthenticationEngine
    from testerat_enhanced.config.test_config import AuthConfig
    from testerat_enhanced.engines.workflow import WorkflowEngine
    from testerat_enhanced.engines.api import APIEngine
    from testerat_enhanced.core.testerat_enhanced import EnhancedTesterat
except ImportError as e:
    print(f"Import error: {e}")
    print("Running basic validation without full imports...")
    # Create mock classes for basic testing
    class MockConfig:
        def __init__(self):
            self.framework = "test"
    UniversalTestConfig = MockConfig
    FrameworkType = type('FrameworkType', (), {
        'REACT': 'react', 'VUE': 'vue', 'ANGULAR': 'angular',
        'NEXTJS': 'nextjs', 'NUXTJS': 'nuxtjs', 'SVELTE': 'svelte', 'VANILLA_JS': 'vanilla_js'
    })
    ErrorHandler = type('ErrorHandler', (), {'__init__': lambda self, logger: None})
    ErrorSeverity = type('ErrorSeverity', (), {'DEBUG': 'debug'})
    create_config_for_framework = lambda f, u: MockConfig()


class TestFrameworkDetectionFixes(unittest.TestCase):
    """Test that framework detection improvements work correctly"""
    
    def setUp(self):
        self.config = UniversalTestConfig()
        self.logger = logging.getLogger(__name__)
    
    def test_framework_specific_optimizations(self):
        """Test that all frameworks have proper optimizations"""
        frameworks_to_test = [
            FrameworkType.REACT,
            FrameworkType.VUE,
            FrameworkType.ANGULAR,
            FrameworkType.NEXTJS,
            FrameworkType.NUXTJS,
            FrameworkType.SVELTE,
            FrameworkType.VANILLA_JS
        ]
        
        for framework in frameworks_to_test:
            with self.subTest(framework=framework):
                config = create_config_for_framework(framework, "https://example.com")
                
                # Verify framework is set
                self.assertEqual(config.framework, framework)
                
                # Verify framework-specific optimizations exist
                if framework == FrameworkType.NEXTJS:
                    self.assertIn("/api/auth/session", config.api_config.common_endpoints)
                    self.assertEqual(config.auth_config.login_url, "/api/auth/signin")
                elif framework == FrameworkType.ANGULAR:
                    self.assertEqual(config.workflow_config.step_timeout, 10000)
                elif framework == FrameworkType.SVELTE:
                    self.assertEqual(config.workflow_config.step_timeout, 5000)
                
                # Verify performance thresholds are set appropriately
                self.assertIsInstance(config.performance_thresholds['load_time'], int)
                self.assertGreater(config.performance_thresholds['load_time'], 0)
    
    def test_enhanced_framework_detection_methods(self):
        """Test that enhanced detection methods are implemented"""
        testerat = EnhancedTesterat(self.config, self.logger)
        
        # Test that detection methods exist
        self.assertTrue(hasattr(testerat, '_detect_framework_by_dom'))
        self.assertTrue(hasattr(testerat, '_detect_framework_by_javascript'))
        self.assertTrue(hasattr(testerat, '_detect_framework_by_meta'))
        self.assertTrue(hasattr(testerat, '_detect_application_features'))


class TestErrorHandlingFixes(unittest.TestCase):
    """Test that error handling improvements work correctly"""
    
    def setUp(self):
        self.logger = logging.getLogger(__name__)
        self.error_handler = ErrorHandler(self.logger)
    
    def test_error_handler_creation(self):
        """Test that error handler can be created and used"""
        self.assertIsInstance(self.error_handler, ErrorHandler)
        self.assertEqual(self.error_handler.logger, self.logger)
    
    def test_error_classification(self):
        """Test that errors are properly classified"""
        test_error = ValueError("Test error")
        
        # Test error handling with different severities
        with self.assertLogs(level='DEBUG') as log:
            self.error_handler.handle_error(
                test_error, 
                context="test_context", 
                severity=ErrorSeverity.DEBUG, 
                suppress=True
            )
        
        # Verify error was logged
        self.assertTrue(any("test_context" in record for record in log.records))
    
    def test_safe_execution(self):
        """Test safe execution wrapper"""
        def failing_function():
            raise ValueError("This function always fails")
        
        def working_function():
            return "success"
        
        # Test failing function with safe execution
        result = self.error_handler.safe_execute(
            failing_function,
            context="test_safe_execution",
            default_return="default_value"
        )
        self.assertEqual(result, "default_value")
        
        # Test working function
        result = self.error_handler.safe_execute(
            working_function,
            context="test_safe_execution"
        )
        self.assertEqual(result, "success")
    
    def test_safe_dom_operation(self):
        """Test safe DOM operation wrapper"""
        mock_page = Mock()
        
        def failing_dom_operation():
            raise Exception("DOM operation failed")
        
        result = safe_dom_operation(
            failing_dom_operation,
            mock_page,
            selector=".test-selector",
            context="test_dom_operation",
            logger=self.logger
        )
        
        # Should return None on failure
        self.assertIsNone(result)


class TestArchitecturalFixes(unittest.TestCase):
    """Test that major architectural fixes work correctly"""
    
    def setUp(self):
        self.logger = logging.getLogger(__name__)
        self.config = UniversalTestConfig()
    
    def test_authentication_engine_error_handling(self):
        """Test that authentication engine has proper error handling"""
        auth_config = AuthConfig()
        auth_engine = AuthenticationEngine(auth_config, self.logger)
        
        # Verify error handler is initialized
        self.assertTrue(hasattr(auth_engine, 'error_handler'))
        self.assertIsInstance(auth_engine.error_handler, ErrorHandler)
    
    def test_workflow_engine_navigation_detection(self):
        """Test that workflow engine has proper navigation failure detection"""
        workflow_config = self.config.workflow_config
        workflow_engine = WorkflowEngine(workflow_config, self.logger)
        
        # Verify the function was renamed correctly
        self.assertTrue(hasattr(workflow_engine, '_detect_navigation_failure'))
        self.assertFalse(hasattr(workflow_engine, '_detect_duplicate_case_bug'))
    
    def test_api_engine_csrf_monitoring(self):
        """Test that API engine has real CSRF monitoring"""
        api_config = self.config.api_config
        api_engine = APIEngine(api_config, self.logger)
        
        # Verify CSRF methods exist and are properly named
        self.assertTrue(hasattr(api_engine, '_test_csrf_header_variations'))
        self.assertTrue(hasattr(api_engine, '_test_csrf_validation'))


class TestReportingFixes(unittest.TestCase):
    """Test that reporting fixes work correctly"""
    
    def test_chart_generation_implementation(self):
        """Test that chart generation is actually implemented"""
        from reporting.enhanced_reporter import EnhancedReporter
        
        reporter = EnhancedReporter()
        
        # Test that JavaScript generation works
        js_code = reporter._get_javascript()
        
        # Verify it's not just a placeholder
        self.assertIn("Chart", js_code)
        self.assertIn("new Chart", js_code)
        self.assertNotIn("// Chart.js configuration would go here", js_code)
    
    def test_pdf_generation_implementation(self):
        """Test that PDF generation is implemented"""
        from reporting.enhanced_reporter import EnhancedReporter
        from utils.test_result import TestSuite
        
        reporter = EnhancedReporter()
        test_suite = TestSuite("Test Suite")
        
        # Test that PDF generation doesn't just return empty string
        result = reporter._generate_pdf_report(test_suite)
        
        # Should return a filename or path, not empty string
        self.assertIsInstance(result, str)
        # Should not be the old placeholder behavior
        self.assertNotEqual(result, "")


def run_validation_tests():
    """Run all validation tests"""
    print("🧪 Running Enhanced Testerat Fixes Validation Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestFrameworkDetectionFixes,
        TestErrorHandlingFixes,
        TestArchitecturalFixes,
        TestReportingFixes
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print(f"📊 Test Results Summary:")
    print(f"   Tests Run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success Rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n🚨 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if not result.failures and not result.errors:
        print("\n✅ All tests passed! Fixes are working correctly.")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_validation_tests()
    sys.exit(0 if success else 1)
