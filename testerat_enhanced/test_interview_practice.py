#!/usr/bin/env python3
"""
Comprehensive Interview Practice Testing
Tests the complete interview practice workflow including question generation,
recording, feedback, and session management
"""

from playwright.sync_api import sync_playwright
import time
import json

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        return True
    return False

def test_interview_practice_access(page):
    """Test accessing interview practice from different entry points"""
    print("\n🎤 TESTING INTERVIEW PRACTICE ACCESS")
    print("=" * 50)
    
    results = []
    
    # Test 1: Direct URL access
    print("   🎯 Testing direct URL access...")
    try:
        page.goto("http://localhost:3001/tools/interview-practice")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Check if page loads
        page_title = page.query_selector('h1, h2, [data-testid="page-title"]')
        if page_title and 'interview' in page_title.text_content().lower():
            print("   ✅ Direct URL access working")
            results.append(('Direct URL Access', True))
        else:
            print("   ❌ Direct URL access failed")
            results.append(('Direct URL Access', False))
    except Exception as e:
        print(f"   ❌ Direct URL access error: {e}")
        results.append(('Direct URL Access', False))
    
    # Test 2: Navigation from dashboard
    print("   🎯 Testing navigation from dashboard...")
    try:
        page.goto("http://localhost:3001/dashboard")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Look for interview practice link
        interview_link = page.query_selector('a[href*="interview"], button:has-text("Interview")')
        if interview_link:
            interview_link.click()
            page.wait_for_load_state('networkidle')
            time.sleep(2)
            
            current_url = page.url
            if 'interview' in current_url:
                print("   ✅ Dashboard navigation working")
                results.append(('Dashboard Navigation', True))
            else:
                print("   ❌ Dashboard navigation failed")
                results.append(('Dashboard Navigation', False))
        else:
            print("   ⚠️ Interview practice link not found in dashboard")
            results.append(('Dashboard Navigation', False))
    except Exception as e:
        print(f"   ❌ Dashboard navigation error: {e}")
        results.append(('Dashboard Navigation', False))
    
    return results

def test_interview_session_creation(page):
    """Test creating different types of interview sessions"""
    print("\n📝 TESTING INTERVIEW SESSION CREATION")
    print("=" * 50)
    
    results = []
    
    page.goto("http://localhost:3001/tools/interview-practice")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Test 1: Quick Practice Session
    print("   🎯 Testing Quick Practice session creation...")
    try:
        quick_practice_btn = page.query_selector('button:has-text("Quick Practice"), [data-testid="quick-practice"]')
        if quick_practice_btn:
            quick_practice_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Check if session started
            session_indicator = page.query_selector('text="Question", text="Session", [data-testid="session-active"]')
            if session_indicator:
                print("   ✅ Quick Practice session created")
                results.append(('Quick Practice Creation', True))
            else:
                print("   ❌ Quick Practice session not created")
                results.append(('Quick Practice Creation', False))
        else:
            print("   ❌ Quick Practice button not found")
            results.append(('Quick Practice Creation', False))
    except Exception as e:
        print(f"   ❌ Quick Practice creation error: {e}")
        results.append(('Quick Practice Creation', False))
    
    # Test 2: Custom Session Creation
    print("   🎯 Testing Custom session creation...")
    try:
        page.goto("http://localhost:3001/tools/interview-practice")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        custom_btn = page.query_selector('button:has-text("Custom"), [data-testid="custom-practice"]')
        if custom_btn:
            custom_btn.click()
            page.wait_for_timeout(2000)
            
            # Try to configure custom session
            # Look for configuration options
            config_options = page.query_selector_all('select, input[type="number"], button')
            if len(config_options) > 0:
                print("   ✅ Custom session configuration available")
                results.append(('Custom Session Config', True))
            else:
                print("   ❌ Custom session configuration not found")
                results.append(('Custom Session Config', False))
        else:
            print("   ❌ Custom session button not found")
            results.append(('Custom Session Config', False))
    except Exception as e:
        print(f"   ❌ Custom session creation error: {e}")
        results.append(('Custom Session Config', False))
    
    return results

def test_interview_question_flow(page):
    """Test the interview question and response flow"""
    print("\n❓ TESTING INTERVIEW QUESTION FLOW")
    print("=" * 50)
    
    results = []
    
    # Start a quick practice session
    page.goto("http://localhost:3001/tools/interview-practice")
    page.wait_for_load_state('networkidle')
    time.sleep(2)
    
    # Test 1: Question Display
    print("   🎯 Testing question display...")
    try:
        quick_practice_btn = page.query_selector('button:has-text("Quick Practice"), [data-testid="quick-practice"]')
        if quick_practice_btn:
            quick_practice_btn.click()
            page.wait_for_load_state('networkidle')
            time.sleep(3)
            
            # Look for question text
            question_text = page.query_selector('[data-testid="question-text"], .question-text, h2, h3')
            if question_text and len(question_text.text_content().strip()) > 10:
                print("   ✅ Question displayed correctly")
                results.append(('Question Display', True))
            else:
                print("   ❌ Question not displayed properly")
                results.append(('Question Display', False))
        else:
            print("   ❌ Cannot start session to test questions")
            results.append(('Question Display', False))
    except Exception as e:
        print(f"   ❌ Question display error: {e}")
        results.append(('Question Display', False))
    
    # Test 2: Response Input
    print("   🎯 Testing response input...")
    try:
        # Look for text input or recording controls
        text_input = page.query_selector('textarea, input[type="text"], [data-testid="response-input"]')
        record_btn = page.query_selector('button:has-text("Record"), [data-testid="record-button"]')
        
        if text_input or record_btn:
            print("   ✅ Response input methods available")
            results.append(('Response Input', True))
            
            # Test text input if available
            if text_input:
                text_input.fill("This is a test response to the interview question.")
                page.wait_for_timeout(1000)
                print("   ✅ Text response input working")
        else:
            print("   ❌ No response input methods found")
            results.append(('Response Input', False))
    except Exception as e:
        print(f"   ❌ Response input error: {e}")
        results.append(('Response Input', False))
    
    # Test 3: Navigation Controls
    print("   🎯 Testing navigation controls...")
    try:
        next_btn = page.query_selector('button:has-text("Next"), [data-testid="next-question"]')
        submit_btn = page.query_selector('button:has-text("Submit"), [data-testid="submit-response"]')
        
        if next_btn or submit_btn:
            print("   ✅ Navigation controls available")
            results.append(('Navigation Controls', True))
        else:
            print("   ❌ Navigation controls not found")
            results.append(('Navigation Controls', False))
    except Exception as e:
        print(f"   ❌ Navigation controls error: {e}")
        results.append(('Navigation Controls', False))
    
    return results

def test_interview_session_management(page):
    """Test session pause, resume, and completion"""
    print("\n⏯️ TESTING SESSION MANAGEMENT")
    print("=" * 50)
    
    results = []
    
    # Test 1: Session Controls
    print("   🎯 Testing session controls...")
    try:
        page.goto("http://localhost:3001/tools/interview-practice")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Start session
        quick_practice_btn = page.query_selector('button:has-text("Quick Practice")')
        if quick_practice_btn:
            quick_practice_btn.click()
            page.wait_for_timeout(3000)
            
            # Look for session controls
            pause_btn = page.query_selector('button:has-text("Pause"), [data-testid="pause-session"]')
            exit_btn = page.query_selector('button:has-text("Exit"), [data-testid="exit-session"]')
            
            if pause_btn or exit_btn:
                print("   ✅ Session controls available")
                results.append(('Session Controls', True))
            else:
                print("   ❌ Session controls not found")
                results.append(('Session Controls', False))
        else:
            print("   ❌ Cannot start session to test controls")
            results.append(('Session Controls', False))
    except Exception as e:
        print(f"   ❌ Session controls error: {e}")
        results.append(('Session Controls', False))
    
    # Test 2: Progress Tracking
    print("   🎯 Testing progress tracking...")
    try:
        # Look for progress indicators
        progress_indicator = page.query_selector('[data-testid="progress"], .progress, text*="Question", text*="of"')
        if progress_indicator:
            print("   ✅ Progress tracking visible")
            results.append(('Progress Tracking', True))
        else:
            print("   ❌ Progress tracking not found")
            results.append(('Progress Tracking', False))
    except Exception as e:
        print(f"   ❌ Progress tracking error: {e}")
        results.append(('Progress Tracking', False))
    
    return results

def main():
    print("🧪 COMPREHENSIVE INTERVIEW PRACTICE TESTING")
    print("=" * 70)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        all_results = []
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run all test suites
            access_results = test_interview_practice_access(page)
            session_results = test_interview_session_creation(page)
            question_results = test_interview_question_flow(page)
            management_results = test_interview_session_management(page)
            
            all_results.extend(access_results)
            all_results.extend(session_results)
            all_results.extend(question_results)
            all_results.extend(management_results)
            
            # Generate report
            print("\n" + "=" * 70)
            print("📊 INTERVIEW PRACTICE TEST RESULTS")
            print("=" * 70)
            
            passed = sum(1 for _, success in all_results if success)
            total = len(all_results)
            success_rate = (passed / total * 100) if total > 0 else 0
            
            print(f"\n📈 OVERALL SUMMARY:")
            print(f"   Total Tests: {total}")
            print(f"   ✅ Passed: {passed}")
            print(f"   ❌ Failed: {total - passed}")
            print(f"   📊 Success Rate: {success_rate:.1f}%")
            
            print(f"\n📋 DETAILED RESULTS:")
            for test_name, success in all_results:
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"   {status}: {test_name}")
            
            print(f"\n🎯 FINAL ASSESSMENT:")
            if success_rate >= 90:
                print("🎉 EXCELLENT: Interview Practice feature working perfectly!")
            elif success_rate >= 80:
                print("✅ GOOD: Most functionality working, minor issues to address")
            elif success_rate >= 70:
                print("⚠️ FAIR: Core functionality working, some improvements needed")
            else:
                print("❌ POOR: Significant issues need immediate attention")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 15 seconds for inspection...")
            time.sleep(15)
            browser.close()

if __name__ == "__main__":
    main()
