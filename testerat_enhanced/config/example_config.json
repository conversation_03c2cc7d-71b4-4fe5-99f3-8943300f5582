{"description": "Enhanced Testerat Configuration Example", "version": "2.0.0", "basic_settings": {"headless": true, "viewport_width": 1920, "viewport_height": 1080, "timeout": 30000, "framework": "auto_detect"}, "test_categories": {"test_authentication": true, "test_workflows": true, "test_api_interactions": true, "test_accessibility": true, "test_performance": true, "test_security": true}, "authentication_config": {"login_url": "/login", "logout_url": "/logout", "signup_url": "/register", "discover_routes": true, "protected_routes": [], "test_users": {"standard": {"email": "<EMAIL>", "password": "testpassword123", "description": "Standard test user"}, "admin": {"email": "<EMAIL>", "password": "adminpassword123", "description": "Admin test user"}}, "selectors": {"email_input": "#email, [name='email'], [type='email']", "password_input": "#password, [name='password'], [type='password']", "login_button": "button[type='submit'], .login-btn, .signin-btn", "logout_button": ".logout, .signout, [data-testid='logout']", "signup_button": ".signup, .register, [data-testid='signup']", "auth_indicator": ".user-menu, .profile, .logged-in, [data-testid='user']", "loading_indicator": ".loading, .spinner, [data-testid='loading']"}, "oauth_providers": ["google", "github", "facebook"], "session_timeout": 3600}, "workflow_config": {"max_steps": 10, "step_timeout": 10000, "navigation_timeout": 5000, "form_fill_delay": 100, "click_delay": 500, "test_patterns": ["multi_step_forms", "wizards", "checkout_flows", "onboarding_flows"], "selectors": {"next_button": ".next, [data-testid='next'], button:contains('Next')", "previous_button": ".prev, .back, [data-testid='previous']", "submit_button": "[type='submit'], .submit, [data-testid='submit']", "cancel_button": ".cancel, [data-testid='cancel']", "step_indicator": ".step, .progress, [data-testid='step']", "error_message": ".error, .alert-error, [data-testid='error']"}}, "api_config": {"test_csrf_protection": true, "test_rate_limiting": true, "test_authentication": true, "common_endpoints": ["/api/health", "/api/status", "/api/auth/session", "/api/user/profile", "/api/data"], "csrf_header_variations": ["X-CSRF-Token", "x-csrf-token", "csrf-token", "X-CSRFToken", "_token"], "expected_status_codes": {"success": [200, 201, 202], "client_error": [400, 401, 403, 404], "server_error": [500, 502, 503]}, "rate_limit_thresholds": {"requests_per_minute": 60, "requests_per_hour": 1000}}, "performance_config": {"thresholds": {"load_time": 3000, "first_contentful_paint": 1500, "largest_contentful_paint": 2500, "cumulative_layout_shift": 0.1, "first_input_delay": 100}, "network_conditions": {"slow_3g": {"download": 500, "upload": 500, "latency": 400}, "fast_3g": {"download": 1600, "upload": 750, "latency": 150}}}, "accessibility_config": {"wcag_level": "AA", "test_categories": ["keyboard_navigation", "screen_reader", "color_contrast", "focus_management", "aria_labels"], "ignore_rules": [], "custom_rules": []}, "security_config": {"test_categories": ["csrf_protection", "xss_prevention", "sql_injection", "authentication_bypass", "session_security"], "security_headers": ["Content-Security-Policy", "X-Frame-Options", "X-Content-Type-Options", "Strict-Transport-Security"]}, "reporting_config": {"output_directory": "./testerat_reports", "generate_html": true, "generate_json": true, "generate_pdf": false, "include_screenshots": true, "include_network_logs": true, "include_console_logs": true, "chart_types": ["doughnut", "bar", "line"], "custom_branding": {"logo_url": "", "company_name": "", "report_title": "Enhanced Testerat Report"}}, "framework_specific": {"react": {"wait_for_hydration": true, "detect_react_router": true, "test_hooks": false, "selectors": {"loading_indicator": "[data-testid='loading'], .loading-spinner"}}, "vue": {"wait_for_mount": true, "detect_vue_router": true, "test_transitions": true, "selectors": {"loading_indicator": ".v-progress-circular, [data-testid='loading']"}}, "angular": {"wait_for_angular": true, "detect_angular_router": true, "test_change_detection": false, "selectors": {"loading_indicator": "mat-spinner, [data-testid='loading']"}}, "nextjs": {"test_ssr": true, "test_api_routes": true, "auth_config": {"login_url": "/api/auth/signin", "logout_url": "/api/auth/signout", "session_endpoint": "/api/auth/session"}}}, "environment_specific": {"development": {"timeout": 60000, "verbose_logging": true, "include_debug_info": true}, "staging": {"timeout": 30000, "verbose_logging": false, "include_debug_info": false}, "production": {"timeout": 15000, "verbose_logging": false, "include_debug_info": false, "test_authentication": false}}, "ci_cd_integration": {"fail_on_critical_issues": true, "fail_on_security_issues": true, "success_rate_threshold": 80, "export_junit_xml": true, "slack_notifications": {"enabled": false, "webhook_url": "", "channel": "#testing"}, "github_integration": {"create_issues": false, "comment_on_pr": false}}}