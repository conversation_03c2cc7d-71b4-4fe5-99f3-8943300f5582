'use client';

import React, { useState, useEffect, Suspense } from 'react';

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircle,
  Clock,
  DollarSign,
  MessageSquare,
  Briefcase,
  BookOpen,
  ArrowRight,
  TrendingUp,
  Target,
  Award,
  BarChart3,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PersonalizedResources from '@/components/dashboard/PersonalizedResources';
import GoalSetting from '@/components/progress/GoalSetting';
import AchievementDisplay from '@/components/progress/AchievementBadge';
import ProgressTracker from '@/components/progress/ProgressTracker';
import ProgressAnalytics from '@/components/progress/ProgressAnalytics';
import { PersonalDashboard } from '@/components/analytics/PersonalDashboard';
import PageLayout from '@/components/layout/PageLayout';
import { log } from '@/lib/logger';

interface DashboardStats {
  assessmentCompleted: boolean;
  assessmentId?: string;
  freedomFundTarget?: number;
  freedomFundCurrent?: number;
  forumPosts: number;
  bookmarkedPaths: number;
}

function DashboardContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [stats, setStats] = useState<DashboardStats>({
    assessmentCompleted: false,
    forumPosts: 0,
    bookmarkedPaths: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Handle tab parameter from URL
  useEffect(() => {
    const tabParam = searchParams?.get('tab');
    if (tabParam && ['overview', 'progress', 'goals', 'achievements', 'analytics'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchDashboardData();
    }
  }, [status, router]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Add timeout to prevent infinite loading
      const fetchWithTimeout = async (url: string, timeout = 10000) => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        try {
          const response = await fetch(url, { signal: controller.signal });
          clearTimeout(timeoutId);
          return response;
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      };

      // Fetch assessment status
      let assessmentCompleted = false;
      let assessmentId = undefined;
      try {
        const assessmentResponse = await fetchWithTimeout('/api/assessment');
        if (assessmentResponse.ok) {
          const assessmentData = await assessmentResponse.json();
          assessmentCompleted = assessmentData.status === 'COMPLETED';
          assessmentId = assessmentData.id;
        } else if (assessmentResponse.status !== 404) {
          console.warn('Assessment API returned:', assessmentResponse.status);
        }
        // If 404, no assessment exists yet, so assessmentCompleted remains false
      } catch (error) {
        log.warn('Error fetching assessment data', {
          component: 'dashboard_page',
          action: 'fetch_assessment',
          metadata: { error: (error as Error).message }
        });
        // Don't throw here, just log and continue with other data
      }

      // Fetch freedom fund data
      let freedomFundData = null;
      try {
        const freedomFundResponse = await fetchWithTimeout('/api/freedom-fund');
        if (freedomFundResponse.ok) {
          freedomFundData = await freedomFundResponse.json();
        } else if (freedomFundResponse.status !== 404) {
          console.warn('Freedom fund API returned:', freedomFundResponse.status);
        }
        // If 404, no freedom fund data exists yet
      } catch (error) {
        log.warn('Error fetching freedom fund data', {
          component: 'dashboard_page',
          action: 'fetch_freedom_fund',
          metadata: { error: (error as Error).message }
        });
      }

      // Fetch forum posts (simplified - just get count)
      let forumData = [];
      try {
        const forumResponse = await fetchWithTimeout('/api/forum/posts');
        if (forumResponse.ok) {
          forumData = await forumResponse.json();
        } else if (forumResponse.status !== 404) {
          console.warn('Forum API returned:', forumResponse.status);
        }
      } catch (error) {
        log.warn('Error fetching forum posts', {
          component: 'dashboard_page',
          action: 'fetch_forum_posts',
          metadata: { error: (error as Error).message }
        });
      }

      // Fetch bookmarked career paths count
      let bookmarkedPathsCount = 0;
      try {
        const bookmarksResponse = await fetchWithTimeout('/api/career-paths/bookmarks?limit=1');
        if (bookmarksResponse.ok) {
          const bookmarksData = await bookmarksResponse.json();
          bookmarkedPathsCount = bookmarksData.pagination?.total || 0;
        } else if (bookmarksResponse.status !== 404) {
          console.warn('Bookmarks API returned:', bookmarksResponse.status);
        }
      } catch (error) {
        log.warn('Error fetching bookmarked paths', {
          component: 'dashboard_page',
          action: 'fetch_bookmarked_paths',
          metadata: { error: (error as Error).message }
        });
      }

      setStats({
        assessmentCompleted,
        assessmentId,
        freedomFundTarget: freedomFundData?.targetSavings,
        freedomFundCurrent: freedomFundData?.currentSavingsAmount,
        forumPosts: forumData.length || 0,
        bookmarkedPaths: bookmarkedPathsCount,
      });
    } catch (error) {
      log.error('Error fetching dashboard data', error as Error, {
        component: 'dashboard_page',
        action: 'fetch_dashboard_data'
      });
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              onClick={() => {
                setError(null);
                fetchDashboardData();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to access your dashboard.</p>
        </div>
      </div>
    );
  }

  const freedomFundProgress = stats.freedomFundTarget && stats.freedomFundCurrent
    ? (stats.freedomFundCurrent / stats.freedomFundTarget) * 100
    : 0;

  return (
    <PageLayout maxWidth="6xl" padding="lg">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Welcome back, {session?.user?.name || session?.user?.email || 'User'}!
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Your comprehensive career transition dashboard - overview, progress tracking, and analytics in one place.
        </p>
      </div>

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="progress" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Progress</span>
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Goals</span>
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center space-x-2">
            <Award className="h-4 w-4" />
            <span>Achievements</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab - Original Dashboard Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center gap-3">
                {stats.assessmentCompleted ? (
                  <CheckCircle className="h-8 w-8 text-green-500" />
                ) : (
                  <Clock className="h-8 w-8 text-orange-500" />
                )}
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Assessment</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {stats.assessmentCompleted ? 'Completed' : 'Pending'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center gap-3">
                <DollarSign className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Freedom Fund</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {stats.freedomFundTarget ? `${Math.round(freedomFundProgress)}%` : 'Not Set'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center gap-3">
                <MessageSquare className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Forum Activity</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {stats.forumPosts} posts
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center gap-3">
                <Briefcase className="h-8 w-8 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Bookmarked Paths</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {stats.bookmarkedPaths}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Personalized Resources */}
          <div>
            <PersonalizedResources />
          </div>

          {/* Action Items */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Next Steps
              </h2>
              <div className="space-y-4">
                {!stats.assessmentCompleted && (
                  <div className="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">Complete Assessment</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Get personalized career path recommendations</p>
                      </div>
                    </div>
                    <Button asChild size="sm">
                      <Link href="/assessment">Start</Link>
                    </Button>
                  </div>
                )}

                {stats.assessmentCompleted && stats.assessmentId && (
                  <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">View Assessment Results</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Review your personalized career recommendations</p>
                      </div>
                    </div>
                    <Button asChild size="sm">
                      <Link href={`/assessment/results/${stats.assessmentId}`}>View Results</Link>
                    </Button>
                  </div>
                )}

                {!stats.freedomFundTarget && (
                  <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <DollarSign className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">Set Freedom Fund Goal</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Calculate your financial runway</p>
                      </div>
                    </div>
                    <Button asChild size="sm">
                      <Link href="/freedom-fund">Calculate</Link>
                    </Button>
                  </div>
                )}

                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Briefcase className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">Explore Career Paths</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Discover new opportunities</p>
                    </div>
                  </div>
                  <Button asChild size="sm" variant="outline">
                    <Link href="/career-paths">Explore</Link>
                  </Button>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Resources & Support
              </h2>
              <div className="space-y-4">
                <Link
                  href="/resources"
                  className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <BookOpen className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">Mindset Resources</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Overcome fears and build confidence</p>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </Link>

                <Link
                  href="/forum"
                  className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <MessageSquare className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">Community Forum</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Connect with others on similar journeys</p>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </Link>

                <button
                  onClick={() => setActiveTab('analytics')}
                  className="w-full flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">Progress & Analytics</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Track your learning progress, goals, and achievements</p>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </button>

                <Link
                  href="/help"
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <TrendingUp className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">Help & Guides</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Get help using the platform</p>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </Link>
              </div>
            </div>
          </div>

          {/* Freedom Fund Progress */}
          {stats.freedomFundTarget && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Freedom Fund Progress
                </h2>
                <Button asChild variant="outline" size="sm">
                  <Link href="/freedom-fund">Update</Link>
                </Button>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    ${stats.freedomFundCurrent?.toLocaleString() || 0} of ${stats.freedomFundTarget.toLocaleString()}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {Math.round(freedomFundProgress)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                  <div
                    className="bg-green-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${Math.min(freedomFundProgress, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        {/* Progress Tab */}
        <TabsContent value="progress">
          <ProgressTracker userId={session?.user?.id || undefined} compact={false} />
        </TabsContent>

        {/* Goals Tab */}
        <TabsContent value="goals">
          <GoalSetting />
        </TabsContent>

        {/* Achievements Tab */}
        <TabsContent value="achievements">
          <AchievementDisplay
            userId={session?.user?.id || undefined}
            variant="grid"
            showLocked={true}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {/* Personal Analytics */}
          <PersonalDashboard />

          {/* Progress Analytics */}
          <div className="mt-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Goal Progress Analytics
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Detailed insights into your goal completion and learning streaks
              </p>
            </div>
            <ProgressAnalytics />
          </div>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
}

export default function DashboardPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    }>
      <DashboardContent />
    </Suspense>
  );
}