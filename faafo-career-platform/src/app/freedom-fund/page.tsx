'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import FreedomFundCalculatorForm from '@/components/freedom-fund/FreedomFundCalculatorForm';
import FreedomFundResults from '@/components/freedom-fund/FreedomFundResults';
import { useCSRF } from '@/hooks/useCSRF';

interface FreedomFundFormData {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavings?: number; // This is from the form
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

// Represents the data structure from/to the backend API
interface FreedomFundData extends FreedomFundFormData {
  id: string;
  userId: string;
  targetSavings: number;
  currentSavingsAmount?: number; // This is specifically for the DB model field name
  createdAt: string;
  updatedAt: string;
}

// API interaction functions
const fetchFreedomFundAPI = async (): Promise<FreedomFundData | null> => {
  const response = await fetch('/api/freedom-fund');
  if (response.status === 404) {
    return null;
  }
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Failed to load data' }));
    throw new Error(errorData.error || 'Failed to load Freedom Fund data');
  }
  return response.json();
};

const saveFreedomFundAPI = async (formData: FreedomFundFormData, getHeaders: () => Record<string, string>): Promise<FreedomFundData> => {
  // Calculate target with inflation adjustment if needed
  const baseTarget = formData.monthlyExpenses * formData.coverageMonths;
  const inflationRate = 0.03; // 3% annual inflation
  const targetSavings = formData.adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;

  const response = await fetch('/api/freedom-fund', {
    method: 'POST',
    headers: getHeaders(),
    body: JSON.stringify({
      monthlyExpenses: formData.monthlyExpenses,
      coverageMonths: formData.coverageMonths,
      currentSavingsAmount: formData.currentSavings, // Map form field to API field
      targetSavings, // Send calculated target
      monthlyContribution: formData.monthlyContribution,
      adjustForInflation: formData.adjustForInflation,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Failed to save data' }));
    throw new Error(errorData.error || 'Failed to save Freedom Fund data');
  }

  return response.json();
};

export default function FreedomFundPage() {
  const { status: sessionStatus } = useSession();
  const router = useRouter();
  const { getHeaders, isLoading: csrfLoading } = useCSRF();

  const [targetAmount, setTargetAmount] = useState<number | null>(null);
  const [currentSavingsDisplay, setCurrentSavingsDisplay] = useState<number | null>(null);
  const [monthlyContribution, setMonthlyContribution] = useState<number | null>(null);
  const [monthlyExpenses, setMonthlyExpenses] = useState<number | null>(null);
  const [initialFormData, setInitialFormData] = useState<Partial<FreedomFundFormData> | undefined>(undefined);

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (sessionStatus === 'authenticated') {
      setIsLoading(true);
      setError(null);
      fetchFreedomFundAPI()
        .then(data => {
          if (data) {
            setTargetAmount(data.targetSavings);
            setCurrentSavingsDisplay(data.currentSavingsAmount ?? null);
            setMonthlyExpenses(data.monthlyExpenses);
            // Note: monthlyContribution might not be in the API response yet
            setInitialFormData({
              monthlyExpenses: data.monthlyExpenses,
              coverageMonths: data.coverageMonths,
              currentSavings: data.currentSavingsAmount,
              // Add new fields when they're available in the API
              monthlyContribution: undefined, // Will be added when API is updated
              adjustForInflation: false, // Default value
            });
          } else {
            // No existing data, clear any previous state
            setTargetAmount(null);
            setCurrentSavingsDisplay(null);
            setMonthlyContribution(null);
            setMonthlyExpenses(null);
            setInitialFormData(undefined);
          }
        })
        .catch(err => {
          console.error('Error fetching Freedom Fund data:', err);
          setError(err.message || 'Could not load your saved data.');
          setTargetAmount(null); // Clear stale data on error
          setCurrentSavingsDisplay(null);
          setMonthlyContribution(null);
          setMonthlyExpenses(null);
          setInitialFormData(undefined);
        })
        .finally(() => setIsLoading(false));
    } else if (sessionStatus === 'unauthenticated') {
      router.push('/login');
    }
  }, [sessionStatus, router]);

  const handleCalculateAndSave = useCallback(async (formData: FreedomFundFormData) => {
    setIsSaving(true);
    setError(null);
    try {
      const savedData = await saveFreedomFundAPI(formData, getHeaders);
      setTargetAmount(savedData.targetSavings);
      setCurrentSavingsDisplay(savedData.currentSavingsAmount ?? null);
      setMonthlyContribution(formData.monthlyContribution ?? null);
      setMonthlyExpenses(savedData.monthlyExpenses);
      setInitialFormData({ // Update initial form data for consistency if form is re-rendered
        monthlyExpenses: savedData.monthlyExpenses,
        coverageMonths: savedData.coverageMonths,
        currentSavings: savedData.currentSavingsAmount,
        monthlyContribution: formData.monthlyContribution,
        adjustForInflation: formData.adjustForInflation,
      });
      // Replace alert with a better notification system in the future
      alert('Freedom Fund data saved successfully!');
    } catch (apiError) {
      console.error('API Error saving Freedom Fund data:', apiError);
      const message = apiError instanceof Error ? apiError.message : 'An unknown error occurred.';
      setError(message);
      alert(`Error: ${message}`);
    }
    setIsSaving(false);
  }, [getHeaders]);

  if (sessionStatus === 'loading' || (sessionStatus === 'authenticated' && isLoading)) {
    return <p className="text-center py-10">Loading Freedom Fund data...</p>;
  }
  if (sessionStatus === 'unauthenticated') {
     // Should have been redirected by useEffect, but as a fallback:
    return <p className="text-center py-10">Please <a href="/login" className="underline">login</a> to use the Freedom Fund calculator.</p>;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Freedom Fund Calculator
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Build your emergency savings strategy and track your progress towards complete financial security and peace of mind.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <p className="text-destructive text-center" role="alert">
            <strong>Error:</strong> {error}
          </p>
        </div>
      )}

      <FreedomFundCalculatorForm
        onSubmit={handleCalculateAndSave}
        initialData={initialFormData}
        key={initialFormData ? JSON.stringify(initialFormData) : 'empty-form'}
      />

      {(targetAmount !== null || initialFormData) && (
        <div className="mt-8">
          <FreedomFundResults
            targetAmount={targetAmount}
            currentSavings={currentSavingsDisplay}
            monthlyContribution={monthlyContribution}
            monthlyExpenses={monthlyExpenses}
          />
        </div>
      )}

      {isSaving && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg shadow-lg border">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
              <p className="text-foreground">Saving your Freedom Fund data...</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
