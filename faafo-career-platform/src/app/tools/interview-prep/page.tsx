'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * Redirect page for backward compatibility
 * Redirects /tools/interview-prep to /interview-practice
 */
export default function InterviewPrepRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the actual interview practice page
    router.replace('/interview-practice');
  }, [router]);

  // Show loading state while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Redirecting to Interview Practice...</p>
      </div>
    </div>
  );
}
