'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useForm, Controller } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Calculator,
  DollarSign,
  MapPin,
  Briefcase,
  TrendingUp,
  Info,
  AlertCircle,
  CheckCircle2,
  BarChart3,
  X,
  Plus,
  GraduationCap,
  Building,
  Users
} from 'lucide-react';
import Link from 'next/link';
import {
  SalaryCalculatorForm,
  SalaryCalculationResult,
  SalaryCalculationResponse,
  EXPERIENCE_LEVELS,
  EDUCATION_LEVELS,
  COMPANY_SIZES
} from '@/types/salary-calculator';

// Using unified types from types file

// Removed hardcoded data - now fetched from API

export default function SalaryCalculatorPage() {
  const { data: session } = useSession();
  const [result, setResult] = useState<SalaryCalculationResult | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [csrfToken, setCsrfToken] = useState<string>('');
  const [skillInput, setSkillInput] = useState<string>('');
  const [availableCareerPaths, setAvailableCareerPaths] = useState<string[]>([]);
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm<SalaryCalculatorForm>({
    defaultValues: {
      careerPath: '',
      experienceLevel: 'mid',
      location: 'Other',
      skills: [],
      education: 'bachelor',
      companySize: 'medium',
      industry: 'technology',
    },
  });

  const watchedValues = watch();

  // Fetch CSRF token and initial data on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoading(true);
      try {
        // Fetch CSRF token
        const csrfResponse = await fetch('/api/csrf-token');
        const csrfData = await csrfResponse.json();
        if (csrfData.success) {
          setCsrfToken(csrfData.csrfToken);
        } else {
          console.error('Failed to fetch CSRF token');
        }

        // Fetch career paths
        const careerPathsResponse = await fetch('/api/tools/salary-calculator?type=career-paths');
        const careerPathsData = await careerPathsResponse.json();
        if (careerPathsData.success) {
          setAvailableCareerPaths(careerPathsData.data.map((item: any) => item.name));
        }

        // Fetch locations
        const locationsResponse = await fetch('/api/tools/salary-calculator?type=locations');
        const locationsData = await locationsResponse.json();
        if (locationsData.success) {
          setAvailableLocations(locationsData.data.map((item: any) => item.name));
        }
      } catch (error) {
        console.error('Failed to fetch initial data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, []);

  // Helper functions for skills management with validation
  const validateSkill = (skill: string): string | null => {
    const trimmed = skill.trim();
    if (!trimmed) return 'Skill cannot be empty';
    if (trimmed.length > 50) return 'Skill name too long (max 50 characters)';
    if (!/^[a-zA-Z0-9\s\.\-\+\#]+$/.test(trimmed)) return 'Skill contains invalid characters';
    return null;
  };

  const addSkill = () => {
    const trimmedSkill = skillInput.trim();
    const validationError = validateSkill(trimmedSkill);

    if (validationError) {
      alert(validationError);
      return;
    }

    const currentSkills = watchedValues.skills || [];

    if (currentSkills.includes(trimmedSkill)) {
      alert('Skill already added');
      return;
    }

    if (currentSkills.length >= 20) {
      alert('Maximum 20 skills allowed');
      return;
    }

    setValue('skills', [...currentSkills, trimmedSkill]);
    setSkillInput('');
  };

  const removeSkill = (skillToRemove: string) => {
    const currentSkills = watchedValues.skills || [];
    setValue('skills', currentSkills.filter(skill => skill !== skillToRemove));
  };

  const handleSkillKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSkill();
    }
  };

  // Form submission with proper error handling
  const onSubmit = async (data: SalaryCalculatorForm) => {
    setIsCalculating(true);

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add CSRF token if available (optional for now)
      if (csrfToken) {
        headers['X-CSRF-Token'] = csrfToken;
      }

      const response = await fetch('/api/tools/salary-calculator', {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: SalaryCalculationResponse = await response.json();

      if (result.success && result.data) {
        setResult(result.data);
      } else {
        console.error('Salary calculation failed:', result.error);
        alert(result.error || 'Failed to calculate salary. Please try again.');
      }
    } catch (error) {
      console.error('Error calculating salary:', error);
      if (error instanceof Error) {
        if (error.message.includes('403')) {
          alert('Security error. Please refresh the page and try again.');
        } else if (error.message.includes('429')) {
          alert('Too many requests. Please wait a moment and try again.');
        } else {
          alert('Network error. Please check your connection and try again.');
        }
      } else {
        alert('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsCalculating(false);
    }
  };



  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Calculator className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Salary Calculator</h1>
        </div>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Get personalized salary estimates based on your skills, experience, and location
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Your Details
            </CardTitle>
            <CardDescription>
              Provide your information to get accurate salary estimates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Career Path Selection */}
              <div className="space-y-2">
                <Label htmlFor="careerPath">Career Path *</Label>
                <Controller
                  name="careerPath"
                  control={control}
                  rules={{ required: 'Please select a career path' }}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value} data-testid="career-path-select">
                      <SelectTrigger data-testid="career-path-trigger">
                        <SelectValue placeholder="Select your target career path" />
                      </SelectTrigger>
                      <SelectContent data-testid="career-path-options">
                        {availableCareerPaths.map((path) => (
                          <SelectItem key={path} value={path} data-testid={`career-path-option-${path.toLowerCase().replace(/\s+/g, '-')}`}>
                            {path}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.careerPath && (
                  <p className="text-sm text-red-600">{errors.careerPath.message}</p>
                )}
              </div>

              {/* Experience Level */}
              <div className="space-y-2">
                <Label htmlFor="experienceLevel">Experience Level</Label>
                <Controller
                  name="experienceLevel"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value} data-testid="experience-select">
                      <SelectTrigger data-testid="experience-trigger">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent data-testid="experience-options">
                        {EXPERIENCE_LEVELS.map((level) => (
                          <SelectItem key={level} value={level} data-testid={`experience-option-${level}`}>
                            {level === 'entry' ? 'Entry Level (0-1 years)' :
                             level === 'junior' ? 'Junior (1-3 years)' :
                             level === 'mid' ? 'Mid Level (3-5 years)' :
                             level === 'senior' ? 'Senior (5-8 years)' :
                             level === 'lead' ? 'Lead (8-12 years)' :
                             level === 'principal' ? 'Principal (12+ years)' :
                             'Executive'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Location */}
              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value} data-testid="location-select">
                      <SelectTrigger data-testid="location-trigger">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent data-testid="location-options">
                        {availableLocations.map((location) => (
                          <SelectItem key={location} value={location} data-testid={`location-option-${location.toLowerCase().replace(/\s+/g, '-').replace(/,/g, '')}`}>
                            {location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Skills Input */}
              <div className="space-y-2">
                <Label htmlFor="skills">Skills</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      value={skillInput}
                      onChange={(e) => setSkillInput(e.target.value)}
                      onKeyPress={handleSkillKeyPress}
                      placeholder="Add a skill (e.g., Python, React, AWS)"
                      className="flex-1"
                    />
                    <Button type="button" onClick={addSkill} size="sm" variant="outline">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {watchedValues.skills && watchedValues.skills.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {watchedValues.skills.map((skill, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {skill}
                          <button
                            type="button"
                            onClick={() => removeSkill(skill)}
                            className="ml-1 hover:text-red-500"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Education Level */}
              <div className="space-y-2">
                <Label htmlFor="education" className="flex items-center gap-2">
                  <GraduationCap className="h-4 w-4" />
                  Education Level
                </Label>
                <Controller
                  name="education"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {EDUCATION_LEVELS.map((level) => (
                          <SelectItem key={level} value={level}>
                            {level === 'high_school' ? 'High School' :
                             level === 'associate' ? 'Associate Degree' :
                             level === 'bachelor' ? 'Bachelor\'s Degree' :
                             level === 'master' ? 'Master\'s Degree' :
                             level === 'phd' ? 'PhD/Doctorate' :
                             level === 'bootcamp' ? 'Coding Bootcamp' :
                             'Self-Taught'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Company Size */}
              <div className="space-y-2">
                <Label htmlFor="companySize" className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Company Size
                </Label>
                <Controller
                  name="companySize"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {COMPANY_SIZES.map((size) => (
                          <SelectItem key={size} value={size}>
                            {size === 'startup' ? 'Startup (1-50 employees)' :
                             size === 'small' ? 'Small (51-200 employees)' :
                             size === 'medium' ? 'Medium (201-1000 employees)' :
                             size === 'large' ? 'Large (1001-10000 employees)' :
                             'Enterprise (10000+ employees)'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Industry */}
              <div className="space-y-2">
                <Label htmlFor="industry" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Industry
                </Label>
                <Controller
                  name="industry"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="e.g., Technology, Healthcare, Finance"
                    />
                  )}
                />
              </div>

              <Button type="submit" className="w-full min-h-[44px]" disabled={isCalculating || isLoading}>
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Loading...
                  </>
                ) : isCalculating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Calculating...
                  </>
                ) : (
                  <>
                    <Calculator className="h-4 w-4 mr-2" />
                    Calculate Salary
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Results */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Salary Estimate
              </CardTitle>
              <CardDescription>
                Based on {result.dataPoints.toLocaleString()} data points
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Main Result */}
              <div className="text-center p-6 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg">
                <div className="text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                  ${result.adjustedRange.min.toLocaleString()} - ${result.adjustedRange.max.toLocaleString()}
                </div>
                <div className="text-lg text-gray-600 dark:text-gray-400 mt-2">
                  Median: ${result.median.toLocaleString()}
                </div>
                <div className="flex items-center justify-center gap-2 mt-3">
                  <Badge variant={result.confidence > 80 ? "default" : result.confidence > 60 ? "secondary" : "outline"}>
                    {result.confidence}% Confidence
                  </Badge>
                </div>
              </div>

              {/* Factors Breakdown */}
              <div className="space-y-3">
                <h4 className="font-semibold">Adjustment Factors</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">Location</span>
                      <p className="text-xs text-gray-500">{result.factors.location.impact}</p>
                    </div>
                    <Badge variant={result.factors.location.multiplier >= 1 ? "default" : "secondary"}>
                      {result.factors.location.multiplier > 1 ? '+' : ''}{((result.factors.location.multiplier - 1) * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">Experience</span>
                      <p className="text-xs text-gray-500">{result.factors.experience.impact}</p>
                    </div>
                    <Badge variant={result.factors.experience.multiplier >= 1 ? "default" : "secondary"}>
                      {result.factors.experience.multiplier > 1 ? '+' : ''}{((result.factors.experience.multiplier - 1) * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">Skills</span>
                      <p className="text-xs text-gray-500">{result.factors.skills.impact} ({result.factors.skills.relevantSkills.length} relevant)</p>
                    </div>
                    <Badge variant="default">
                      +{(result.factors.skills.bonus * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">Education</span>
                      <p className="text-xs text-gray-500">{result.factors.education.impact}</p>
                    </div>
                    <Badge variant={result.factors.education.multiplier >= 1 ? "default" : "secondary"}>
                      {result.factors.education.multiplier > 1 ? '+' : ''}{((result.factors.education.multiplier - 1) * 100).toFixed(0)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-sm font-medium">Company Size</span>
                      <p className="text-xs text-gray-500">{result.factors.companySize.impact}</p>
                    </div>
                    <Badge variant={result.factors.companySize.multiplier >= 1 ? "default" : "secondary"}>
                      {result.factors.companySize.multiplier > 1 ? '+' : ''}{((result.factors.companySize.multiplier - 1) * 100).toFixed(0)}%
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Recommendations */}
              {result.marketInsights.recommendations.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-semibold flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Recommendations
                  </h4>
                  <ul className="space-y-2">
                    {result.marketInsights.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Market Insights */}
              <div className="space-y-3">
                <h4 className="font-semibold">Market Insights</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium">Demand</span>
                    <p className="text-xs text-gray-500 capitalize">{result.marketInsights.demand}</p>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Growth</span>
                    <p className="text-xs text-gray-500">{result.marketInsights.growth}</p>
                  </div>
                </div>
                <div>
                  <span className="text-sm font-medium">Top Skills</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {result.marketInsights.topSkills.slice(0, 5).map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Disclaimer */}
      <Card className="mt-8">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p className="font-medium mb-2">Important Disclaimer</p>
              <p>
                Salary estimates are based on aggregated market data and should be used as a general guide only.
                Actual salaries may vary significantly based on company, specific role requirements, negotiation,
                and other factors not captured in this calculator. Always research specific companies and roles
                for the most accurate information.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
