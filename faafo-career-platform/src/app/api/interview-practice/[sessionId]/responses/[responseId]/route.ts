import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { geminiService } from '@/lib/services/geminiService';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Validation schema for updating responses
const updateResponseSchema = z.object({
  responseText: z.string().min(10).max(5000).optional(),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  responseTime: z.number().min(0).max(3600).optional(),
  preparationTime: z.number().min(0).max(1800).optional(),
  userNotes: z.string().max(1000).optional(),
  needsReview: z.boolean().optional(),
  requestNewFeedback: z.boolean().default(false),
});

// GET - Retrieve specific response
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string; responseId: string }> }
) {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 200 : 50 // Higher limit for development
    },
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId, responseId } = await params;

      try {
        const response = await prisma.interviewResponse.findFirst({
          where: {
            id: responseId,
            sessionId,
            userId,
          },
          include: {
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                category: true,
                difficulty: true,
                expectedDuration: true,
                context: true,
                hints: true,
                followUpQuestions: true,
                questionOrder: true,
              },
            },
            session: {
              select: {
                id: true,
                sessionType: true,
                careerPath: true,
                experienceLevel: true,
                status: true,
              },
            },
          },
        });

        if (!response) {
          return NextResponse.json(
            { success: false, error: 'Response not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: response,
        });
      } catch (error) {
        console.error('Error fetching interview response:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview response' },
          { status: 500 }
        );
      }
    }
  );
}

// PATCH - Update specific response
export const PATCH = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string; responseId: string }> }
) => {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 100 : 20 // Higher limit for development
    },
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId, responseId } = await params;

      try {
        const body = await request.json();
        const validation = updateResponseSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const updateData = validation.data;

        // Verify response ownership
        const existingResponse = await prisma.interviewResponse.findFirst({
          where: {
            id: responseId,
            sessionId,
            userId,
          },
          include: {
            question: true,
            session: true,
          },
        });

        if (!existingResponse) {
          return NextResponse.json(
            { success: false, error: 'Response not found' },
            { status: 404 }
          );
        }

        // Prepare update data
        const { requestNewFeedback, ...responseUpdateData } = updateData;

        // Update the response
        let updatedResponse = await prisma.interviewResponse.update({
          where: { id: responseId },
          data: {
            ...responseUpdateData,
            updatedAt: new Date(),
          },
          include: {
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                category: true,
                difficulty: true,
                expectedDuration: true,
                context: true,
                hints: true,
                followUpQuestions: true,
                questionOrder: true,
              },
            },
            session: {
              select: {
                id: true,
                sessionType: true,
                careerPath: true,
                experienceLevel: true,
                status: true,
              },
            },
          },
        });

        // Generate new AI feedback if requested
        if (requestNewFeedback && updateData.responseText) {
          try {
            const feedbackResult = await geminiService.analyzeInterviewResponse({
              questionText: existingResponse.question.questionText,
              questionType: existingResponse.question.questionType,
              questionCategory: existingResponse.question.category,
              responseText: updateData.responseText,
              responseTime: updateData.responseTime || existingResponse.responseTime,
              expectedDuration: existingResponse.question.expectedDuration,
              careerPath: existingResponse.session.careerPath || undefined,
              experienceLevel: existingResponse.session.experienceLevel || undefined,
              context: existingResponse.question.context || undefined,
            });

            if (feedbackResult.success) {
              // Update response with new AI analysis
              updatedResponse = await prisma.interviewResponse.update({
                where: { id: responseId },
                data: {
                  aiScore: feedbackResult.data.overallScore,
                  aiAnalysis: feedbackResult.data.analysis,
                  feedback: feedbackResult.data.feedback,
                  strengths: feedbackResult.data.strengths,
                  improvements: feedbackResult.data.improvements,
                  starMethodScore: feedbackResult.data.starMethodScore,
                  confidenceLevel: feedbackResult.data.confidenceLevel,
                  communicationScore: feedbackResult.data.communicationScore,
                  technicalScore: feedbackResult.data.technicalScore,
                },
                include: {
                  question: {
                    select: {
                      id: true,
                      questionText: true,
                      questionType: true,
                      category: true,
                      difficulty: true,
                      expectedDuration: true,
                      context: true,
                      hints: true,
                      followUpQuestions: true,
                      questionOrder: true,
                    },
                  },
                  session: {
                    select: {
                      id: true,
                      sessionType: true,
                      careerPath: true,
                      experienceLevel: true,
                      status: true,
                    },
                  },
                },
              });
            }
          } catch (feedbackError) {
            console.error('Error generating new AI feedback:', feedbackError);
            // Continue without new feedback - don't fail the update
          }
        }

        // Update session last active time
        await prisma.interviewSession.update({
          where: { id: sessionId },
          data: { lastActiveAt: new Date() },
        });

        return NextResponse.json({
          success: true,
          data: updatedResponse,
          message: 'Response updated successfully',
        });
      } catch (error) {
        console.error('Error updating interview response:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to update interview response' },
          { status: 500 }
        );
      }
    }
  );
});

// DELETE - Delete specific response
export const DELETE = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string; responseId: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId, responseId } = await params;

      try {
        // Verify response ownership
        const existingResponse = await prisma.interviewResponse.findFirst({
          where: {
            id: responseId,
            sessionId,
            userId,
          },
        });

        if (!existingResponse) {
          return NextResponse.json(
            { success: false, error: 'Response not found' },
            { status: 404 }
          );
        }

        // Delete the response
        await prisma.interviewResponse.delete({
          where: { id: responseId },
        });

        // Update session completed questions count
        const completedResponses = await prisma.interviewResponse.count({
          where: {
            sessionId,
            userId,
            isCompleted: true,
          },
        });

        await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            completedQuestions: completedResponses,
            lastActiveAt: new Date(),
          },
        });

        return NextResponse.json({
          success: true,
          message: 'Response deleted successfully',
        });
      } catch (error) {
        console.error('Error deleting interview response:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to delete interview response' },
          { status: 500 }
        );
      }
    }
  );
});
