import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { UserValidationService } from '@/lib/user-validation-service';
import { UnifiedValidationService } from '@/lib/unified-validation-service';
import { z } from 'zod';

// Using unified validation service - no need for duplicate schema

// GET - Retrieve user's interview sessions
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 100 : 30 // Higher limit for development
    },
    async () => {
      // Enhanced user validation with automatic repair
      const validation = await UserValidationService.validateUserSession(request, {
        validateUserExists: true,
        checkAccountLock: true
      });

      if (!validation.isValid) {
        return NextResponse.json(
          { success: false, error: validation.error },
          { status: validation.statusCode || 401 }
        );
      }

      const userId = validation.userId!;
      const { searchParams } = new URL(request.url);
      const status = searchParams.get('status');
      const limit = parseInt(searchParams.get('limit') || '10');
      const offset = parseInt(searchParams.get('offset') || '0');

      try {
        const whereClause: any = { userId };
        if (status) {
          whereClause.status = status;
        }

        const [sessions, total] = await Promise.all([
          prisma.interviewSession.findMany({
            where: whereClause,
            include: {
              questions: {
                select: {
                  id: true,
                  questionType: true,
                  category: true,
                  difficulty: true,
                  questionOrder: true,
                },
                orderBy: { questionOrder: 'asc' }
              },
              responses: {
                where: { userId }, // Filter responses by current user
                select: {
                  id: true,
                  questionId: true,
                  isCompleted: true,
                  aiScore: true,
                },
              },
              _count: {
                select: {
                  questions: true,
                  responses: {
                    where: { userId, isCompleted: true }
                  }
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
            skip: offset,
          }),
          prisma.interviewSession.count({ where: whereClause }),
        ]);

        const response = NextResponse.json({
          success: true,
          data: {
            sessions,
            pagination: {
              total,
              limit,
              offset,
              hasMore: offset + limit < total,
            },
          },
        });

        // Add caching headers for better performance
        response.headers.set('Cache-Control', 'private, max-age=60, stale-while-revalidate=300');

        return response;
      } catch (error) {
        console.error('Error fetching interview sessions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview sessions' },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Create new interview session
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ? 50 : 10 // Higher limit for development
      },
      async () => {
      // Enhanced user validation with automatic repair
      const validation = await UserValidationService.validateUserSession(request, {
        validateUserExists: true,
        checkAccountLock: true
      });

      if (!validation.isValid) {
        return NextResponse.json(
          { success: false, error: validation.error },
          { status: validation.statusCode || 401 }
        );
      }

      const userId = validation.userId!;

      // Ensure user has required relationships
      await UserValidationService.ensureUserRelationships(userId);

      try {
        const body = await request.json();
        const validation = UnifiedValidationService.validateSessionConfig(body);

        if (!validation.isValid) {
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid request data',
              details: validation.errors,
              securityFlags: validation.securityFlags
            },
            { status: 400 }
          );
        }

        const sessionData = validation.sanitizedData!;

        // Create the interview session
        const interviewSession = await prisma.interviewSession.create({
          data: {
            userId,
            sessionType: sessionData.sessionType as any,
            careerPath: sessionData.careerPath,
            experienceLevel: sessionData.experienceLevel as any,
            companyType: sessionData.companyType,
            industryFocus: sessionData.industryFocus,
            specificRole: sessionData.specificRole,
            interviewType: sessionData.interviewType as any,
            preparationTime: sessionData.preparationTime,
            focusAreas: sessionData.focusAreas,
            difficulty: sessionData.difficulty as any,
            totalQuestions: sessionData.totalQuestions,
            status: 'IN_PROGRESS',
            sessionConfig: {
              createdVia: 'api',
              userAgent: request.headers.get('user-agent'),
            },
          },
          select: {
            id: true,
            userId: true,
            sessionType: true,
            careerPath: true,
            experienceLevel: true,
            companyType: true,
            industryFocus: true,
            specificRole: true,
            interviewType: true,
            preparationTime: true,
            focusAreas: true,
            difficulty: true,
            totalQuestions: true,
            status: true,
            timeSpent: true,
            startedAt: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                questions: true,
                responses: true
              }
            }
          },
        });

        // Note: Questions will be generated when the user starts the session
        // This prevents session creation from failing due to question generation issues

        return NextResponse.json({
          success: true,
          data: interviewSession,
          message: 'Interview session created successfully',
        });
      } catch (error) {
        console.error('Error creating interview session:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to create interview session' },
          { status: 500 }
        );
      }
    }
    );
  });
});
