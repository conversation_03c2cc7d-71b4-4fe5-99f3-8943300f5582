/**
 * AI Service Performance Dashboard API
 * Comprehensive monitoring and analytics endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { geminiService } from '@/lib/services/geminiService';
import { advancedCacheManager } from '@/lib/advanced-cache-manager';
import { requestOptimizer } from '@/lib/request-optimizer';
import { performanceMonitor } from '@/lib/performance-monitor';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is admin
    if (!session?.user?.email || !session.user.email.includes('admin')) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view') || 'overview';
    const timeRange = searchParams.get('timeRange') || '24h';

    switch (view) {
      case 'overview':
        return NextResponse.json(await getOverviewData());
      
      case 'performance':
        return NextResponse.json(await getPerformanceData(timeRange));
      
      case 'cache':
        return NextResponse.json(await getCacheData());
      
      case 'requests':
        return NextResponse.json(await getRequestData());
      
      case 'health':
        return NextResponse.json(await getHealthData());
      
      case 'insights':
        return NextResponse.json(await getInsightsData());
      
      default:
        return NextResponse.json(
          { error: 'Invalid view parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('AI Performance Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getOverviewData() {
  const [
    healthStatus,
    cacheStats,
    requestStats,
    performanceStats,
    aiServiceStats
  ] = await Promise.all([
    geminiService.healthCheck(),
    advancedCacheManager.getStats(),
    requestOptimizer.getStats(),
    performanceMonitor.getInsights(),
    aiServiceMonitor.getMetrics()
  ]);

  return {
    timestamp: new Date().toISOString(),
    overview: {
      systemHealth: healthStatus,
      totalRequests: aiServiceStats.totalRequests,
      successRate: aiServiceStats.totalRequests > 0 ?
        (aiServiceStats.successfulRequests / aiServiceStats.totalRequests) * 100 : 0,
      averageResponseTime: aiServiceStats.averageResponseTime,
      cacheHitRate: cacheStats.hitRate,
      activeRequests: requestOptimizer.getActiveRequestCount(),
      queuedRequests: requestOptimizer.getQueuedRequestCount(),
      performanceScore: performanceStats.overallScore
    },
    quickStats: {
      uptime: aiServiceStats.uptime,
      errorRate: aiServiceStats.totalRequests > 0 ?
        (aiServiceStats.failedRequests / aiServiceStats.totalRequests) * 100 : 0,
      throughput: requestStats.throughputPerSecond,
      memoryUsage: cacheStats.memoryUsage
    }
  };
}

async function getPerformanceData(timeRange: string) {
  const insights = performanceMonitor.getInsights();
  const metrics = performanceMonitor.getMetrics();
  const alerts = performanceMonitor.getAlerts();

  return {
    timestamp: new Date().toISOString(),
    timeRange,
    performance: {
      overallScore: insights.overallScore,
      trends: insights.trends,
      bottlenecks: insights.bottlenecks,
      recommendations: insights.recommendations,
      metrics: Array.from(metrics.entries() as any).map((entry: any) => ({
        operation: entry[0],
        dataPoints: Array.isArray(entry[1]) ? entry[1].slice(-100) : [] // Last 100 data points
      })),
      alerts: {
        active: alerts.length,
        critical: alerts.filter(a => a.type === 'critical').length,
        recent: alerts.slice(-10)
      }
    }
  };
}

async function getCacheData() {
  const advancedCacheStats = advancedCacheManager.getStats();
  const advancedCacheHealth = await advancedCacheManager.healthCheck();
  const advancedCacheConfig = advancedCacheManager.getConfig();

  return {
    timestamp: new Date().toISOString(),
    cache: {
      advanced: {
        stats: advancedCacheStats,
        health: advancedCacheHealth,
        config: advancedCacheConfig
      },
      performance: {
        hitRate: advancedCacheStats.hitRate,
        averageResponseTime: advancedCacheStats.averageResponseTime,
        memoryUsage: advancedCacheStats.memoryUsage,
        compressionRatio: advancedCacheStats.compressionRatio
      },
      recommendations: generateCacheRecommendations(advancedCacheStats)
    }
  };
}

async function getRequestData() {
  const requestStats = requestOptimizer.getStats();
  const requestHealth = await requestOptimizer.healthCheck();
  const requestConfig = requestOptimizer.getConfig();

  return {
    timestamp: new Date().toISOString(),
    requests: {
      stats: requestStats,
      health: requestHealth,
      config: requestConfig,
      optimization: {
        batchingEfficiency: requestStats.batchedRequests / requestStats.totalRequests,
        deduplicationRate: requestStats.deduplicatedRequests / requestStats.totalRequests,
        averageBatchSize: requestStats.averageBatchSize,
        queuePerformance: {
          active: requestOptimizer.getActiveRequestCount(),
          queued: requestOptimizer.getQueuedRequestCount(),
          throughput: requestStats.throughputPerSecond
        }
      }
    }
  };
}

async function getHealthData() {
  const [
    aiHealth,
    cacheHealth,
    requestHealth,
    performanceHealth
  ] = await Promise.all([
    geminiService.healthCheck(),
    advancedCacheManager.healthCheck(),
    requestOptimizer.healthCheck(),
    performanceMonitor.healthCheck()
  ]);

  const overallStatus = determineOverallHealth([
    aiHealth.ai,
    cacheHealth.status === 'healthy',
    requestHealth.status === 'healthy',
    performanceHealth.status === 'healthy'
  ]);

  return {
    timestamp: new Date().toISOString(),
    health: {
      overall: overallStatus,
      components: {
        aiService: aiHealth,
        cache: cacheHealth,
        requestOptimizer: requestHealth,
        performanceMonitor: performanceHealth
      },
      systemMetrics: {
        uptime: Date.now() - (aiServiceMonitor.getMetrics().uptime || Date.now()),
        lastHealthCheck: new Date().toISOString(),
        healthScore: calculateHealthScore([aiHealth, cacheHealth, requestHealth, performanceHealth])
      }
    }
  };
}

async function getInsightsData() {
  const performanceInsights = performanceMonitor.getInsights();
  const aiServiceAnalytics = aiServiceMonitor.getUsageAnalytics();
  const cacheStats = advancedCacheManager.getStats();
  const requestStats = requestOptimizer.getStats();

  return {
    timestamp: new Date().toISOString(),
    insights: {
      performance: performanceInsights,
      usage: aiServiceAnalytics,
      optimization: {
        cacheOptimization: generateCacheOptimizationInsights(cacheStats),
        requestOptimization: generateRequestOptimizationInsights(requestStats),
        systemOptimization: generateSystemOptimizationInsights(performanceInsights)
      },
      predictions: {
        capacityNeeds: predictCapacityNeeds(aiServiceAnalytics),
        performanceTrends: predictPerformanceTrends(performanceInsights),
        optimizationOpportunities: identifyOptimizationOpportunities({
          cache: cacheStats,
          requests: requestStats,
          performance: performanceInsights
        })
      }
    }
  };
}

function generateCacheRecommendations(stats: any) {
  const recommendations = [];
  
  if (stats.hitRate < 0.7) {
    recommendations.push({
      priority: 'high',
      category: 'cache-hit-rate',
      message: 'Cache hit rate is below optimal (70%)',
      action: 'Consider implementing cache warming or increasing TTL values',
      impact: 'Could improve response times by 20-30%'
    });
  }
  
  if (stats.memoryUsage > 0.8) {
    recommendations.push({
      priority: 'medium',
      category: 'memory-usage',
      message: 'Memory usage is high',
      action: 'Consider implementing more aggressive cache eviction or increasing memory limits',
      impact: 'Prevent potential memory-related performance issues'
    });
  }
  
  return recommendations;
}

function determineOverallHealth(componentStatuses: boolean[]): 'healthy' | 'degraded' | 'unhealthy' {
  const healthyCount = componentStatuses.filter(status => status).length;
  const totalCount = componentStatuses.length;
  
  if (healthyCount === totalCount) return 'healthy';
  if (healthyCount >= totalCount * 0.7) return 'degraded';
  return 'unhealthy';
}

function calculateHealthScore(healthData: any[]): number {
  // Calculate a composite health score from 0-100
  let totalScore = 0;
  let componentCount = 0;
  
  healthData.forEach(component => {
    if (component.status === 'healthy') totalScore += 100;
    else if (component.status === 'degraded') totalScore += 70;
    else totalScore += 30;
    componentCount++;
  });
  
  return Math.round(totalScore / componentCount);
}

function generateCacheOptimizationInsights(stats: any) {
  return {
    currentEfficiency: stats.hitRate,
    potentialImprovement: Math.max(0, 0.9 - stats.hitRate),
    recommendations: [
      'Implement predictive cache warming',
      'Optimize cache key generation',
      'Adjust TTL values based on access patterns'
    ]
  };
}

function generateRequestOptimizationInsights(stats: any) {
  return {
    batchingEfficiency: stats.batchedRequests / Math.max(stats.totalRequests, 1),
    deduplicationSavings: stats.deduplicatedRequests,
    recommendations: [
      'Increase batch size for better throughput',
      'Implement request prioritization',
      'Optimize queue management'
    ]
  };
}

function generateSystemOptimizationInsights(insights: any) {
  return {
    overallScore: insights.overallScore,
    criticalBottlenecks: insights.bottlenecks.filter((b: any) => b.severity === 'high'),
    quickWins: insights.recommendations.filter((r: any) => r.priority === 'high')
  };
}

function predictCapacityNeeds(analytics: any) {
  return {
    currentLoad: analytics.requestsPerHour || 0,
    projectedGrowth: '15-20% monthly',
    recommendedScaling: 'Consider horizontal scaling if load exceeds 1000 req/hour',
    timeToCapacity: 'Approximately 3-6 months at current growth rate'
  };
}

function predictPerformanceTrends(insights: any) {
  return {
    responseTimeTrend: insights.trends.responseTime,
    throughputTrend: insights.trends.throughput,
    errorRateTrend: insights.trends.errorRate,
    prediction: 'Performance expected to remain stable with current optimizations'
  };
}

function identifyOptimizationOpportunities(data: any) {
  const opportunities = [];
  
  if (data.cache.hitRate < 0.8) {
    opportunities.push({
      area: 'Caching',
      opportunity: 'Improve cache hit rate',
      impact: 'High',
      effort: 'Medium',
      description: 'Implement intelligent cache warming and optimization'
    });
  }
  
  if (data.requests.averageWaitTime > 100) {
    opportunities.push({
      area: 'Request Processing',
      opportunity: 'Reduce request wait times',
      impact: 'Medium',
      effort: 'Low',
      description: 'Optimize request batching and queue management'
    });
  }
  
  return opportunities;
}
