import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { ErrorReporter } from '@/lib/errorReporting';
import { log } from '@/lib/logger';
import { trackError } from '@/lib/errorTracking';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';
import { ValidationPipelines } from '@/lib/validation-pipeline';

// Validation schemas with proper length limits
const personalInfoSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: z.string()
    .email('Valid email is required')
    .max(254, 'Email is too long'),
  phone: z.string()
    .max(20, 'Phone number is too long')
    .regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),
  location: z.string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
  website: z.string()
    .url('Please enter a valid website URL')
    .max(500, 'Website URL is too long')
    .optional()
    .or(z.literal('')),
  linkedIn: z.string()
    .url('Please enter a valid LinkedIn URL')
    .max(500, 'LinkedIn URL is too long')
    .optional()
    .or(z.literal(''))
});

const experienceSchema = z.object({
  company: z.string().min(1, 'Company name is required'),
  position: z.string().min(1, 'Position is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  description: z.string().optional(),
  achievements: z.array(z.string()).optional()
});

const educationSchema = z.object({
  institution: z.string().min(1, 'Institution is required'),
  degree: z.string().min(1, 'Degree is required'),
  field: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  gpa: z.string().optional(),
  honors: z.string().optional()
});

const skillSchema = z.object({
  name: z.string().min(1, 'Skill name is required'),
  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  category: z.string().optional()
});

const resumeCreateSchema = z.object({
  title: z.string()
    .min(1, 'Resume title is required')
    .max(200, 'Resume title must be less than 200 characters'),
  personalInfo: personalInfoSchema,
  summary: z.string()
    .max(2000, 'Summary must be less than 2000 characters')
    .optional(),
  experience: z.array(experienceSchema)
    .max(20, 'Maximum 20 experience entries allowed')
    .optional(),
  education: z.array(educationSchema)
    .max(10, 'Maximum 10 education entries allowed')
    .optional(),
  skills: z.array(skillSchema)
    .max(50, 'Maximum 50 skills allowed')
    .optional(),
  sections: z.record(z.any()).optional(),
  template: z.string()
    .max(50, 'Template name is too long')
    .default('modern'),
  isPublic: z.boolean().default(false)
});

// GET - List user's resumes
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes
    async () => {
      const startTime = Date.now();
      const session = await getServerSession(authOptions);

      if (!session?.user?.email) {
        log.auth('resume_access_denied', undefined, false, {
          component: 'resume_builder_api',
          action: 'list_resumes'
        });
        return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
      }

      try {
        log.info('Fetching user resumes', {
          component: 'resume_builder_api',
          action: 'list_resumes',
          userId: session.user.email
        });

        const dbStartTime = Date.now();
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true }
        });

        if (!user) {
          return NextResponse.json({ error: 'User not found' }, { status: 404 });
        }

        const resumes = await prisma.resume.findMany({
          where: { 
            userId: user.id,
            isActive: true
          },
          select: {
            id: true,
            title: true,
            template: true,
            isPublic: true,
            lastExported: true,
            exportCount: true,
            createdAt: true,
            updatedAt: true
          },
          orderBy: { updatedAt: 'desc' }
        });

        const dbDuration = Date.now() - dbStartTime;
        log.database('findMany', 'resume', dbDuration, {
          userId: user.id
        });

        const totalDuration = Date.now() - startTime;
        log.api('GET', '/api/resume-builder', 200, totalDuration, {
          component: 'resume_builder_api',
          userId: session.user.email
        });

        const response = NextResponse.json({
          success: true,
          data: resumes
        });

        // Add caching headers for better performance
        response.headers.set('Cache-Control', 'private, max-age=30, stale-while-revalidate=120');

        return response;

      } catch (error) {
        const totalDuration = Date.now() - startTime;

        log.error('Error fetching resumes', error as Error, {
          component: 'resume_builder_api',
          action: 'list_resumes',
          userId: session.user.email
        });

        trackError.api(error as Error, '/api/resume-builder', 'GET', 500);

        ErrorReporter.captureError(error as Error, {
          userId: session.user.email,
          userEmail: session.user.email,
          action: 'list_resumes',
          component: 'resume_builder_api',
        });

        log.api('GET', '/api/resume-builder', 500, totalDuration, {
          component: 'resume_builder_api',
          userId: session.user.email
        });

        return NextResponse.json({ 
          success: false,
          error: 'Internal Server Error' 
        }, { status: 500 });
      }
    }
  );
}

// POST - Create new resume
export async function POST(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 creates per 15 minutes
      async () => {
        const startTime = Date.now();
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
          return NextResponse.json({ 
            success: false,
            error: 'Not authenticated' 
          }, { status: 401 });
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            select: { id: true }
          });

          if (!user) {
            return NextResponse.json({ 
              success: false,
              error: 'User not found' 
            }, { status: 404 });
          }

          const body = await request.json();

          // Step 1: Use validation pipeline for comprehensive validation and sanitization
          const personalInfoPipeline = ValidationPipelines.createPersonalInfoPipeline();
          const resumePipeline = ValidationPipelines.createResumePipeline();

          // Validate personal info
          const personalInfoResult = await personalInfoPipeline.validate(body.personalInfo);
          if (!personalInfoResult.isValid) {
            return NextResponse.json({
              success: false,
              error: 'Personal information validation failed',
              details: personalInfoResult.errors
            }, { status: 400 });
          }

          // Validate resume data
          const resumeResult = await resumePipeline.validate(body);
          if (!resumeResult.isValid) {
            return NextResponse.json({
              success: false,
              error: 'Resume data validation failed',
              details: resumeResult.errors
            }, { status: 400 });
          }

          // Step 2: Use sanitized data from validation pipeline
          const sanitizedBody = {
            ...resumeResult.sanitizedData,
            personalInfo: personalInfoResult.sanitizedData
          };

          // Step 3: Additional Zod validation for complex structures
          const validatedData = resumeCreateSchema.parse(sanitizedBody);

          log.info('Creating new resume', {
            component: 'resume_builder_api',
            action: 'create_resume',
            userId: user.id
          });

          const dbStartTime = Date.now();
          const resume = await prisma.resume.create({
            data: {
              userId: user.id,
              title: validatedData.title,
              personalInfo: validatedData.personalInfo,
              summary: validatedData.summary,
              experience: validatedData.experience || [],
              education: validatedData.education || [],
              skills: validatedData.skills || [],
              sections: validatedData.sections || {},
              template: validatedData.template,
              isPublic: validatedData.isPublic
            }
          });

          const dbDuration = Date.now() - dbStartTime;
          log.database('create', 'resume', dbDuration, {
            userId: user.id
          });

          const totalDuration = Date.now() - startTime;
          log.api('POST', '/api/resume-builder', 201, totalDuration, {
            component: 'resume_builder_api',
            userId: session.user.email
          });

          return NextResponse.json({
            success: true,
            data: resume
          }, { status: 201 });

        } catch (error) {
          const totalDuration = Date.now() - startTime;

          if (error instanceof z.ZodError) {
            log.warn('Resume validation failed', {
              component: 'resume_builder_api',
              action: 'create_resume',
              userId: session.user.email
            });

            return NextResponse.json({
              success: false,
              error: 'Validation failed',
              details: error.errors
            }, { status: 400 });
          }

          log.error('Error creating resume', error as Error, {
            component: 'resume_builder_api',
            action: 'create_resume',
            userId: session.user.email
          });

          trackError.api(error as Error, '/api/resume-builder', 'POST', 500);

          ErrorReporter.captureError(error as Error, {
            userId: session.user.email,
            userEmail: session.user.email,
            action: 'create_resume',
            component: 'resume_builder_api',
          });

          log.api('POST', '/api/resume-builder', 500, totalDuration, {
            component: 'resume_builder_api',
            userId: session.user.email
          });

          return NextResponse.json({
            success: false,
            error: 'Internal Server Error'
          }, { status: 500 });
        }
      }
    );
  });
}
