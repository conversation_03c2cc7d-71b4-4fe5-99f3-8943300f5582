import { NextRequest, NextResponse } from 'next/server';
import { getCSRFTokenEndpoint } from '@/lib/csrf';

export async function GET(request: NextRequest) {
  try {
    // Use the unified CSRF system
    return await getCSRFTokenEndpoint(request);
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}
