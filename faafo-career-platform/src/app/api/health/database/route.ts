import { NextResponse } from 'next/server';
import { prisma, withDatabaseRetry } from '@/lib/prisma';

export async function GET() {
  try {
    const startTime = Date.now();
    
    // Test database connection with retry logic
    await withDatabaseRetry(async () => {
      await prisma.$queryRaw`SELECT 1`;
    }, 2, 1000);
    
    const responseTime = Date.now() - startTime;
    
    return NextResponse.json({
      status: 'healthy',
      database: 'connected',
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 503 });
  }
}
