import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { apiCache } from '@/lib/cache';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Resource ID is required' },
        { status: 400 }
      );
    }

    // Check cache first
    const cacheKey = `learning_resource:${id}`;
    const cached = apiCache.getJSON(cacheKey);
    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      });
    }

    // Optimized query - get resource without ratings first
    const resource = await prisma.learningResource.findUnique({
      where: { id },
      select: {
        id: true,
        title: true,
        description: true,
        url: true,
        type: true,
        category: true,
        skillLevel: true,
        author: true,
        duration: true,
        cost: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        skills: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!resource) {
      return NextResponse.json(
        { success: false, error: 'Resource not found' },
        { status: 404 }
      );
    }

    // Get rating aggregation in a separate optimized query
    const ratingAggregation = await prisma.resourceRating.aggregate({
      where: { resourceId: id },
      _avg: { rating: true },
      _count: { rating: true }
    });

    const averageRating = ratingAggregation._avg.rating || 0;
    const totalRatings = ratingAggregation._count.rating || 0;

    // Format the response
    const formattedResource = {
      id: resource.id,
      title: resource.title,
      description: resource.description,
      url: resource.url,
      type: resource.type,
      category: resource.category,
      skillLevel: resource.skillLevel,
      author: resource.author,
      duration: resource.duration,
      cost: resource.cost,
      averageRating: Math.round(averageRating * 10) / 10,
      totalRatings,
      careerPaths: resource.careerPaths,
      skills: resource.skills,
      isActive: resource.isActive,
      createdAt: resource.createdAt,
      updatedAt: resource.updatedAt,
    };

    // Cache the result for 10 minutes
    apiCache.setJSON(cacheKey, formattedResource, 10 * 60 * 1000);

    return NextResponse.json({
      success: true,
      data: formattedResource,
    });

  } catch (error) {
    console.error('Error fetching resource:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch resource' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Resource ID is required' },
        { status: 400 }
      );
    }

    const {
      title,
      description,
      url,
      type,
      category,
      skillLevel,
      author,
      duration,
      cost,
      format,
      isActive,
    } = body;

    // Check if resource exists
    const existingResource = await prisma.learningResource.findUnique({
      where: { id },
    });

    if (!existingResource) {
      return NextResponse.json(
        { success: false, error: 'Resource not found' },
        { status: 404 }
      );
    }

    const updatedResource = await prisma.learningResource.update({
      where: { id },
      data: {
        title: title || existingResource.title,
        description: description || existingResource.description,
        url: url || existingResource.url,
        type: type || existingResource.type,
        category: category || existingResource.category,
        skillLevel: skillLevel || existingResource.skillLevel,
        author: author !== undefined ? author : existingResource.author,
        duration: duration !== undefined ? duration : existingResource.duration,
        cost: cost || existingResource.cost,
        format: format || existingResource.format,
        isActive: isActive !== undefined ? isActive : existingResource.isActive,
        updatedAt: new Date(),
      },
      include: {
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        ratings: {
          select: {
            rating: true,
          },
        },
      },
    });

    // Calculate average rating
    const averageRating = updatedResource.ratings.length > 0 
      ? updatedResource.ratings.reduce((sum, rating) => sum + rating.rating, 0) / updatedResource.ratings.length
      : 0;

    const totalRatings = updatedResource.ratings.length;

    const formattedResource = {
      ...updatedResource,
      averageRating: Math.round(averageRating * 10) / 10,
      totalRatings,
    };

    return NextResponse.json({
      success: true,
      data: formattedResource,
    });

  } catch (error) {
    console.error('Error updating resource:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update resource' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Resource ID is required' },
        { status: 400 }
      );
    }

    // Check if resource exists
    const existingResource = await prisma.learningResource.findUnique({
      where: { id },
    });

    if (!existingResource) {
      return NextResponse.json(
        { success: false, error: 'Resource not found' },
        { status: 404 }
      );
    }

    // Soft delete by setting isActive to false
    await prisma.learningResource.update({
      where: { id },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Resource deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting resource:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete resource' },
      { status: 500 }
    );
  }
}
