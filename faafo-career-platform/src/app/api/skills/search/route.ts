import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { prisma } from '@/lib/prisma';

interface SkillSearchResult {
  id: string;
  name: string;
  category: string;
  description?: string;
  marketData?: {
    averageSalary?: number;
    demandLevel?: string;
    growthRate?: number;
  };
}

interface SkillSearchResponse {
  success: boolean;
  skills: SkillSearchResult[];
  total: number;
  query: string;
}

async function handleSkillSearch(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q')?.trim();
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);
    const category = searchParams.get('category');

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        skills: [],
        total: 0,
        query: query || '',
      } as SkillSearchResponse);
    }

    // Build search conditions
    const searchConditions: any = {
      OR: [
        {
          name: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: query,
            mode: 'insensitive',
          },
        },
        {
          category: {
            contains: query,
            mode: 'insensitive',
          },
        },
      ],
    };

    // Add category filter if specified
    if (category) {
      searchConditions.AND = [
        {
          category: {
            equals: category,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Search skills in database
    const skills = await prisma.skill.findMany({
      where: searchConditions,
      include: {
        marketData: {
          where: { isActive: true },
          orderBy: { dataDate: 'desc' },
          take: 1,
        },
      },
      take: limit,
      orderBy: [
        {
          name: 'asc',
        },
      ],
    });

    // Format response
    const formattedSkills: SkillSearchResult[] = skills.map(skill => ({
      id: skill.id,
      name: skill.name,
      category: skill.category,
      description: skill.description || undefined,
      marketData: skill.marketData[0] ? {
        averageSalary: skill.marketData[0].averageSalary || undefined,
        demandLevel: skill.marketData[0].demandLevel || undefined,
        growthRate: skill.marketData[0].growthRate || undefined,
      } : undefined,
    }));

    // If no results found in database, provide some common skills as fallback
    if (formattedSkills.length === 0) {
      const commonSkills = getCommonSkillSuggestions(query);
      return NextResponse.json({
        success: true,
        skills: commonSkills,
        total: commonSkills.length,
        query,
      } as SkillSearchResponse);
    }

    return NextResponse.json({
      success: true,
      skills: formattedSkills,
      total: formattedSkills.length,
      query,
    } as SkillSearchResponse);

  } catch (error) {
    console.error('Error searching skills:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to search skills',
        skills: [],
        total: 0,
        query: '',
      } as SkillSearchResponse,
      { status: 500 }
    );
  }
}

// Fallback common skills when database search returns no results
function getCommonSkillSuggestions(query: string): SkillSearchResult[] {
  const commonSkills = [
    { name: 'JavaScript', category: 'Programming Languages' },
    { name: 'Python', category: 'Programming Languages' },
    { name: 'React', category: 'Frontend Frameworks' },
    { name: 'Node.js', category: 'Backend Technologies' },
    { name: 'TypeScript', category: 'Programming Languages' },
    { name: 'SQL', category: 'Database Technologies' },
    { name: 'Git', category: 'Development Tools' },
    { name: 'Docker', category: 'DevOps Tools' },
    { name: 'AWS', category: 'Cloud Platforms' },
    { name: 'Project Management', category: 'Soft Skills' },
    { name: 'Communication', category: 'Soft Skills' },
    { name: 'Leadership', category: 'Soft Skills' },
    { name: 'Problem Solving', category: 'Soft Skills' },
    { name: 'Data Analysis', category: 'Analytics' },
    { name: 'Machine Learning', category: 'AI/ML' },
    { name: 'UI/UX Design', category: 'Design' },
    { name: 'Agile Methodology', category: 'Project Management' },
    { name: 'REST APIs', category: 'Backend Technologies' },
    { name: 'MongoDB', category: 'Database Technologies' },
    { name: 'CSS', category: 'Frontend Technologies' },
  ];

  const queryLower = query.toLowerCase();
  return commonSkills
    .filter(skill => 
      skill.name.toLowerCase().includes(queryLower) ||
      skill.category.toLowerCase().includes(queryLower)
    )
    .slice(0, 10)
    .map((skill, index) => ({
      id: `common-${index}`,
      name: skill.name,
      category: skill.category,
      description: `${skill.name} - ${skill.category}`,
    }));
}

// Export handlers with rate limiting
export const GET = withErrorHandler(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 searches per 15 minutes
    () => handleSkillSearch(request)
  );
});
