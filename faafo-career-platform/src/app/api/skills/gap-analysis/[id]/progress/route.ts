import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateProgressSchema = z.object({
  completedSkills: z.array(z.string()).min(1, 'At least one skill must be completed'),
  milestoneId: z.string().min(1, 'Milestone ID is required'),
  notes: z.string().max(1000, 'Notes too long').optional(),
});

interface UpdateGapAnalysisProgressRequest {
  completedSkills: string[];
  milestoneId: string;
  notes?: string;
}

interface UpdateGapAnalysisProgressResponse {
  success: boolean;
  data: {
    updatedAnalysis: {
      completionPercentage: number;
      nextMilestone: {
        skills: string[];
        estimatedHours: number;
        dueDate: string;
      };
    };
    achievements: Array<{
      type: string;
      title: string;
      points: number;
    }>;
  };
}

async function handleUpdateGapAnalysisProgress(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const userId = session.user.id;
  const analysisId = params.id;

  try {
    const body = await request.json();
    const validation = updateProgressSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid progress update data',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { completedSkills, milestoneId, notes } = validation.data;

    // Get the gap analysis
    const analysis = await prisma.skillGapAnalysis.findFirst({
      where: {
        id: analysisId,
        userId,
      },
    });

    if (!analysis) {
      return NextResponse.json(
        { success: false, error: 'Gap analysis not found' },
        { status: 404 }
      );
    }

    if (analysis.status !== 'ACTIVE') {
      return NextResponse.json(
        { success: false, error: 'Cannot update inactive analysis' },
        { status: 400 }
      );
    }

    // Parse current progress tracking
    const progressTracking = analysis.progressTracking as any || {
      milestones: [],
      completedMilestones: [],
      completedSkills: [],
      currentPhase: 'planning',
    };

    // Update completed skills
    const newCompletedSkills = Array.from(new Set([
      ...(progressTracking.completedSkills || []),
      ...completedSkills,
    ]));

    // Mark milestone as completed
    const completedMilestones = Array.from(new Set([
      ...(progressTracking.completedMilestones || []),
      milestoneId,
    ]));

    // Calculate completion percentage
    const totalSkillGaps = Array.isArray(analysis.skillGaps) ? analysis.skillGaps.length : 0;
    const completionPercentage = totalSkillGaps > 0 
      ? Math.round((newCompletedSkills.length / totalSkillGaps) * 100)
      : 0;

    // Update progress tracking
    const updatedProgressTracking = {
      ...progressTracking,
      completedSkills: newCompletedSkills,
      completedMilestones,
      lastUpdated: new Date().toISOString(),
      notes: notes ? [...(progressTracking.notes || []), {
        date: new Date().toISOString(),
        milestone: milestoneId,
        note: notes,
      }] : progressTracking.notes,
    };

    // Find next milestone
    const milestones = progressTracking.milestones || [];
    const nextMilestone = milestones.find((m: any) => 
      !completedMilestones.includes(m.month.toString())
    );

    // Update the analysis
    const updatedAnalysis = await prisma.skillGapAnalysis.update({
      where: { id: analysisId },
      data: {
        progressTracking: updatedProgressTracking,
        completionPercentage,
        lastUpdated: new Date(),
        status: completionPercentage >= 100 ? 'COMPLETED' : 'ACTIVE',
      },
    });

    // Update user skill progress for completed skills
    const achievements = [];
    for (const skillName of completedSkills) {
      try {
        // Find skill by name
        const skill = await prisma.skill.findFirst({
          where: { name: { contains: skillName, mode: 'insensitive' } },
        });

        if (skill) {
          // Update user skill progress
          await prisma.userSkillProgress.upsert({
            where: {
              userId_skillId: {
                userId,
                skillId: skill.id,
              },
            },
            update: {
              progressPoints: { increment: 25 }, // Bonus points for gap analysis completion
              lastPracticed: new Date(),
            },
            create: {
              userId,
              skillId: skill.id,
              currentLevel: 'INTERMEDIATE',
              progressPoints: 25,
              lastPracticed: new Date(),
            },
          });

          achievements.push({
            type: 'SKILL_PROGRESS',
            title: `${skillName} Progress`,
            points: 25,
          });
        }
      } catch (error) {
        console.error(`Error updating progress for skill ${skillName}:`, error);
      }
    }

    // Add milestone completion achievement
    achievements.push({
      type: 'MILESTONE_COMPLETED',
      title: `Milestone ${milestoneId} Completed`,
      points: 50,
    });

    // Prepare response data
    const responseData: UpdateGapAnalysisProgressResponse = {
      success: true,
      data: {
        updatedAnalysis: {
          completionPercentage,
          nextMilestone: nextMilestone ? {
            skills: nextMilestone.skills || [],
            estimatedHours: nextMilestone.estimatedHours || 0,
            dueDate: calculateMilestoneDueDate(analysis.createdAt, nextMilestone.month),
          } : {
            skills: [],
            estimatedHours: 0,
            dueDate: new Date().toISOString(),
          },
        },
        achievements,
      },
    };

    return NextResponse.json(responseData);
  } catch (error) {
    console.error('Error updating gap analysis progress:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update progress' 
      },
      { status: 500 }
    );
  }
}

function calculateMilestoneDueDate(startDate: Date, milestoneMonth: number): string {
  const dueDate = new Date(startDate);
  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);
  return dueDate.toISOString();
}

export const PUT = withErrorHandler(async (request: NextRequest, context: any) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 30 }, // 30 updates per 15 minutes
      () => handleUpdateGapAnalysisProgress(request, context)
    );
  });
});
