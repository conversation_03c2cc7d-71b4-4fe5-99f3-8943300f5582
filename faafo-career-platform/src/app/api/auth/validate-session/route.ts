/**
 * API endpoint for session validation
 * Provides server-side session validation for the authentication state manager
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isUserAdmin } from '@/lib/auth-utils';
import { rateLimiters } from '@/lib/rate-limit';

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = rateLimiters.api.check(request);

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          error: 'Too many session validation requests',
          retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimitResult.limit.toString(),
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),
            'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
          }
        }
      );
    }

    // Get current session
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { 
          valid: false, 
          error: 'No active session',
          user: null,
          isAdmin: false
        },
        { status: 401 }
      );
    }

    // Validate session integrity
    const now = Math.floor(Date.now() / 1000);
    const sessionToken = session as any;

    // Check if session has expired
    if (sessionToken.exp && sessionToken.exp < now) {
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Session expired',
          user: null,
          isAdmin: false
        },
        { status: 401 }
      );
    }

    // Check if session is too old (force refresh after 30 days)
    if (sessionToken.iat && (now - sessionToken.iat) > (30 * 24 * 60 * 60)) {
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Session too old, please re-authenticate',
          user: null,
          isAdmin: false
        },
        { status: 401 }
      );
    }

    // Check admin status
    let isAdmin = false;
    try {
      isAdmin = await isUserAdmin(session.user.id);
    } catch (error) {
      console.error('Error checking admin status during session validation:', error);
      // Don't fail validation just because admin check failed
    }

    // Validate user still exists and is active
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: {
          id: true,
          email: true,
          name: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true
        }
      });

      await prisma.$disconnect();

      if (!user) {
        return NextResponse.json(
          { 
            valid: false, 
            error: 'User not found',
            user: null,
            isAdmin: false
          },
          { status: 401 }
        );
      }

      // Return successful validation
      return NextResponse.json({
        valid: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        },
        isAdmin,
        sessionInfo: {
          sessionId: sessionToken.sessionId || null,
          issuedAt: sessionToken.iat,
          expiresAt: sessionToken.exp,
          lastActivity: now
        }
      });

    } catch (error) {
      console.error('Database error during session validation:', error);
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Database error during validation',
          user: null,
          isAdmin: false
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Session validation error:', error);
    return NextResponse.json(
      { 
        valid: false, 
        error: 'Internal server error',
        user: null,
        isAdmin: false
      },
      { status: 500 }
    );
  }
}

// Only allow GET requests
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
