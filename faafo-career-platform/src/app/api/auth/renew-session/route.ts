/**
 * Session Renewal API Endpoint
 * Handles session renewal requests
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { log } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    // Get current session
    const session = await getServerSession(authOptions);

    if (!session) {
      log.warn('Session renewal attempted without valid session', {
        component: 'session_renewal_api',
        action: 'renewal_attempt_no_session',
        metadata: {
          userAgent: request.headers.get('user-agent'),
          ip: request.headers.get('x-forwarded-for') || 'unknown'
        }
      });

      return NextResponse.json(
        { error: 'No valid session found' },
        { status: 401 }
      );
    }

    // Check if session is close to expiry
    const now = Date.now();
    const sessionExpiry = session.expires ? new Date(session.expires).getTime() : now + (30 * 60 * 1000);
    const timeUntilExpiry = sessionExpiry - now;

    // Only allow renewal if session is within 10 minutes of expiry
    const renewalWindow = 10 * 60 * 1000; // 10 minutes
    if (timeUntilExpiry > renewalWindow) {
      log.info('Session renewal attempted too early', {
        component: 'session_renewal_api',
        action: 'renewal_too_early',
        metadata: {
          userId: session.user?.id,
          timeUntilExpiry: Math.round(timeUntilExpiry / 1000),
          renewalWindow: Math.round(renewalWindow / 1000)
        }
      });

      return NextResponse.json(
        { 
          error: 'Session renewal not needed yet',
          timeUntilExpiry: Math.round(timeUntilExpiry / 1000)
        },
        { status: 400 }
      );
    }

    // Calculate new expiry time (extend by 30 minutes)
    const extensionTime = 30 * 60 * 1000; // 30 minutes
    const newExpiry = now + extensionTime;

    // In a real implementation, you would:
    // 1. Update the session in your database
    // 2. Update any JWT tokens
    // 3. Set new cookies with extended expiry
    
    // For NextAuth.js, the session is automatically managed
    // We'll return the new expiry time for the client to track
    
    log.info('Session renewed successfully', {
      component: 'session_renewal_api',
      action: 'session_renewed',
      metadata: {
        userId: session.user?.id,
        oldExpiry: sessionExpiry,
        newExpiry: newExpiry,
        extensionMinutes: extensionTime / (60 * 1000)
      }
    });

    // Set response headers to extend session cookies if needed
    const response = NextResponse.json({
      success: true,
      message: 'Session renewed successfully',
      expiresAt: newExpiry,
      extensionMinutes: extensionTime / (60 * 1000)
    });

    // Update session cookie expiry
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: extensionTime / 1000, // Convert to seconds
      path: '/'
    };

    // Note: In a real implementation with custom session management,
    // you would update the actual session cookie here
    // For NextAuth.js, this is handled automatically

    return response;

  } catch (error) {
    log.error('Error during session renewal', error as Error, {
      component: 'session_renewal_api',
      action: 'renewal_error'
    });

    return NextResponse.json(
      { error: 'Internal server error during session renewal' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get current session status
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { 
          valid: false,
          error: 'No valid session found'
        },
        { status: 401 }
      );
    }

    const now = Date.now();
    const sessionExpiry = session.expires ? new Date(session.expires).getTime() : now + (30 * 60 * 1000);
    const timeUntilExpiry = sessionExpiry - now;

    return NextResponse.json({
      valid: true,
      expiresAt: sessionExpiry,
      timeUntilExpiry: Math.round(timeUntilExpiry / 1000),
      canRenew: timeUntilExpiry <= (10 * 60 * 1000), // Can renew within 10 minutes
      user: {
        id: session.user?.id,
        email: session.user?.email,
        name: session.user?.name
      }
    });

  } catch (error) {
    log.error('Error checking session status', error as Error, {
      component: 'session_renewal_api',
      action: 'status_check_error'
    });

    return NextResponse.json(
      { error: 'Internal server error checking session status' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
