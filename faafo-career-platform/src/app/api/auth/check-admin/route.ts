import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isUserAdmin } from '@/lib/auth-utils';
import { rateLimiters } from '@/lib/rate-limit';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = rateLimiters.auth.check(request);

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        {
          isAdmin: false,
          error: 'Too many admin check requests',
          retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': rateLimitResult.limit.toString(),
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),
            'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
          }
        }
      );
    }

    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({
        isAdmin: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Get userId from query params
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    // Security check: users can only check their own admin status
    // unless they are already an admin
    if (userId && userId !== session.user.id) {
      const currentUserIsAdmin = await isUserAdmin(session.user.id);
      if (!currentUserIsAdmin) {
        return NextResponse.json({
          isAdmin: false,
          error: 'Unauthorized to check other users admin status'
        }, { status: 403 });
      }
    }

    // Use current user's ID if no userId provided
    const targetUserId = userId || session.user.id;

    // Check admin status using the centralized function
    const isAdmin = await isUserAdmin(targetUserId);

    return NextResponse.json({
      isAdmin,
      userId: targetUserId,
      checkedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json({
      isAdmin: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
