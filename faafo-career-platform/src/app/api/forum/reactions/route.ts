import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
// GET handler to retrieve reactions for a post or reply
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('postId');
    const replyId = searchParams.get('replyId');

    if (!postId && !replyId) {
      return NextResponse.json(
        { error: 'Either postId or replyId is required' },
        { status: 400 }
      );
    }

    let reactions: any[] = [];

    if (postId) {
      reactions = await prisma.forumPostReaction.findMany({
        where: { postId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    } else if (replyId) {
      reactions = await prisma.forumReplyReaction.findMany({
        where: { replyId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    }

    // Group reactions by type for easy counting
    const reactionCounts = reactions.reduce((acc, reaction) => {
      acc[reaction.type] = (acc[reaction.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      reactions,
      counts: reactionCounts,
      total: reactions.length,
    });
  } catch (error) {
    console.error('Error fetching reactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reactions' },
      { status: 500 }
    );
  }
}

// POST handler to add or update a reaction
export async function POST(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { postId, replyId, type } = body;

    if (!postId && !replyId) {
      return NextResponse.json(
        { error: 'Either postId or replyId is required' },
        { status: 400 }
      );
    }

    if (!type) {
      return NextResponse.json(
        { error: 'Reaction type is required' },
        { status: 400 }
      );
    }

    // Validate reaction type
    const validTypes = ['LIKE', 'DISLIKE', 'HELPFUL', 'INSIGHTFUL', 'INSPIRING'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid reaction type' },
        { status: 400 }
      );
    }

    // Check if post or reply exists
    if (postId) {
      const post = await prisma.forumPost.findUnique({
        where: { id: postId },
      });
      if (!post) {
        return NextResponse.json({ error: 'Post not found' }, { status: 404 });
      }
    }

    if (replyId) {
      const reply = await prisma.forumReply.findUnique({
        where: { id: replyId },
      });
      if (!reply) {
        return NextResponse.json({ error: 'Reply not found' }, { status: 404 });
      }
    }

    // Check if user already reacted
    let existingReaction: any = null;

    if (postId) {
      existingReaction = await prisma.forumPostReaction.findFirst({
        where: {
          userId: session.user.id,
          postId,
        },
      });
    } else if (replyId) {
      existingReaction = await prisma.forumReplyReaction.findFirst({
        where: {
          userId: session.user.id,
          replyId,
        },
      });
    }

    let reaction;

    if (existingReaction) {
      if (existingReaction.type === type) {
        // Same reaction - remove it (toggle off)
        if (postId) {
          await prisma.forumPostReaction.delete({
            where: { id: existingReaction.id },
          });
        } else {
          await prisma.forumReplyReaction.delete({
            where: { id: existingReaction.id },
          });
        }
        return NextResponse.json({ message: 'Reaction removed', removed: true });
      } else {
        // Different reaction - update it
        if (postId) {
          reaction = await prisma.forumPostReaction.update({
            where: { id: existingReaction.id },
            data: { type },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });
        } else {
          reaction = await prisma.forumReplyReaction.update({
            where: { id: existingReaction.id },
            data: { type },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });
        }
      }
    } else {
      // New reaction - create it
      if (postId) {
        reaction = await prisma.forumPostReaction.create({
          data: {
            userId: session.user.id,
            type,
            postId,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });
      } else {
        reaction = await prisma.forumReplyReaction.create({
          data: {
            userId: session.user.id,
            type,
            replyId,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });
      }
    }

    return NextResponse.json(reaction);
  } catch (error) {
    console.error('Error managing reaction:', error);
    return NextResponse.json(
      { error: 'Failed to manage reaction' },
      { status: 500 }
    );
  }
    }
  );
  });
}

// DELETE handler to remove a reaction
export async function DELETE(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 },
      async () => {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('postId');
    const replyId = searchParams.get('replyId');

    if (!postId && !replyId) {
      return NextResponse.json(
        { error: 'Either postId or replyId is required' },
        { status: 400 }
      );
    }

    let reaction: any = null;

    if (postId) {
      reaction = await prisma.forumPostReaction.findFirst({
        where: {
          userId: session.user.id,
          postId,
        },
      });

      if (reaction) {
        await prisma.forumPostReaction.delete({
          where: { id: reaction.id },
        });
      }
    } else if (replyId) {
      reaction = await prisma.forumReplyReaction.findFirst({
        where: {
          userId: session.user.id,
          replyId,
        },
      });

      if (reaction) {
        await prisma.forumReplyReaction.delete({
          where: { id: reaction.id },
        });
      }
    }

    if (!reaction) {
      return NextResponse.json(
        { error: 'Reaction not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Reaction removed successfully' });
  } catch (error) {
    console.error('Error removing reaction:', error);
    return NextResponse.json(
      { error: 'Failed to remove reaction' },
      { status: 500 }
    );
  }
    }
  );
  });
}
