import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';

import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
// GET handler to retrieve forum posts
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    const whereClause: any = {
      isHidden: false,
    };

    if (categoryId) {
      whereClause.categoryId = categoryId;
    }

    const posts = await prisma.forumPost.findMany({
      where: whereClause,
      include: {
        author: {
          select: {
            id: true,
            email: true,
            name: true,
            profile: {
              select: {
                profilePictureUrl: true,
                forumReputation: true,
                forumPostCount: true,
                currentCareerPath: true,
                progressLevel: true,
              },
            },
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            replies: true,
            reactions: true,
            bookmarks: true,
          },
        },
        reactions: {
          select: {
            type: true,
            userId: true,
          },
        },
      },
      orderBy: [
        { isPinned: 'desc' },
        { createdAt: 'desc' },
      ],
      skip,
      take: limit,
    });

    const totalPosts = await prisma.forumPost.count({
      where: whereClause,
    });

    return NextResponse.json({
      posts,
      pagination: {
        page,
        limit,
        total: totalPosts,
        pages: Math.ceil(totalPosts / limit),
      },
    }, { status: 200 });
  } catch (error) {
    console.error('Error fetching forum posts:', error);
    return NextResponse.json({ error: 'Failed to fetch forum posts' }, { status: 500 });
  }
}

// POST handler to create a new forum post
export async function POST(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { title, content, categoryId } = await request.json();

    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 });
    }

    if (title.length > 200) {
      return NextResponse.json({ error: 'Title must be 200 characters or less' }, { status: 400 });
    }

    if (content.length > 5000) {
      return NextResponse.json({ error: 'Content must be 5000 characters or less' }, { status: 400 });
    }

    // Validate category if provided
    if (categoryId) {
      const category = await prisma.forumCategory.findUnique({
        where: { id: categoryId },
      });
      if (!category) {
        return NextResponse.json({ error: 'Invalid category' }, { status: 400 });
      }
    }

    // Use transaction to create post and update user profile
    const result = await prisma.$transaction(async (tx) => {
      const newPost = await tx.forumPost.create({
        data: {
          title: title.trim(),
          content: content.trim(),
          authorId: session.user?.id!,
          categoryId: categoryId || null,
        },
        include: {
          author: {
            select: {
              id: true,
              email: true,
              name: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  forumReputation: true,
                  forumPostCount: true,
                  currentCareerPath: true,
                  progressLevel: true,
                },
              },
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              replies: true,
              reactions: true,
              bookmarks: true,
            },
          },
        },
      });

      // Update user's forum post count and reputation
      await tx.profile.upsert({
        where: { userId: session.user?.id! },
        update: {
          forumPostCount: { increment: 1 },
          forumReputation: { increment: 1 }, // 1 point for creating a post
        },
        create: {
          userId: session.user?.id!,
          forumPostCount: 1,
          forumReputation: 1,
        },
      });

      // Update category post count if category is specified
      if (categoryId) {
        await tx.forumCategory.update({
          where: { id: categoryId },
          data: {
            postCount: { increment: 1 },
            lastPostAt: new Date(),
            lastPostBy: session.user?.id!,
          },
        });
      }

      return newPost;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating forum post:', error);
    return NextResponse.json({ error: 'Failed to create forum post' }, { status: 500 });
  }
    }
  );
  });
}
