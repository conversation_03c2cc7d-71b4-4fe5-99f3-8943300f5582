/* Layout Protection CSS - Prevents UI breaking with edge case inputs */

/* Text Overflow Protection */
.text-safe {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-truncate-multiline {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-truncate-2 {
  -webkit-line-clamp: 2;
}

.text-truncate-3 {
  -webkit-line-clamp: 3;
}

.text-truncate-4 {
  -webkit-line-clamp: 4;
}

.text-truncate-5 {
  -webkit-line-clamp: 5;
}

/* Container Protection */
.container-safe {
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.flex-safe {
  min-width: 0;
  flex-shrink: 1;
}

.grid-safe {
  min-width: 0;
  overflow: hidden;
}

/* Input Protection */
.input-safe {
  max-width: 100%;
  word-break: break-all;
}

.input-safe:focus {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Card and Content Protection */
.card-safe {
  overflow: hidden;
  word-wrap: break-word;
}

.card-safe .card-content {
  min-width: 0;
  overflow: hidden;
}

.card-safe .card-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Table Protection */
.table-safe {
  table-layout: fixed;
  width: 100%;
}

.table-safe th,
.table-safe td {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-safe .table-cell-expandable {
  white-space: normal;
  max-width: none;
}

/* Form Protection */
.form-safe {
  max-width: 100%;
  overflow: hidden;
}

.form-safe .form-field {
  min-width: 0;
  max-width: 100%;
}

.form-safe .form-label {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.form-safe .form-error {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* Navigation Protection */
.nav-safe {
  overflow: hidden;
}

.nav-safe .nav-item {
  min-width: 0;
  flex-shrink: 1;
}

.nav-safe .nav-link {
  word-wrap: break-word;
  overflow-wrap: break-word;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* Button Protection */
.btn-safe {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  min-width: 0;
}

.btn-safe.btn-icon-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-safe.btn-icon-text .btn-text {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Modal and Dialog Protection */
.modal-safe {
  max-width: calc(100vw - 2rem);
  max-height: calc(100vh - 2rem);
  overflow: auto;
}

.modal-safe .modal-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.modal-safe .modal-title {
  word-wrap: break-word;
  overflow-wrap: break-word;
  margin-right: 2rem; /* Space for close button */
}

/* Responsive Protection */
@media (max-width: 640px) {
  .mobile-safe {
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
  }
  
  .mobile-safe .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  
  .mobile-safe .mobile-truncate {
    -webkit-line-clamp: 2;
  }
}

@media (max-width: 480px) {
  .xs-safe {
    min-width: 0;
    max-width: 100%;
  }
  
  .xs-safe .xs-text {
    font-size: 0.75rem;
    line-height: 1rem;
  }
  
  .xs-safe .xs-truncate {
    -webkit-line-clamp: 1;
  }
}

/* Utility Classes for Edge Cases */
.break-all {
  word-break: break-all;
}

.break-words {
  word-break: break-word;
}

.break-normal {
  word-break: normal;
  overflow-wrap: normal;
}

.no-wrap {
  white-space: nowrap;
}

.wrap {
  white-space: normal;
}

.pre-wrap {
  white-space: pre-wrap;
}

/* Scroll Protection */
.scroll-safe {
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--muted) var(--background);
}

.scroll-safe::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scroll-safe::-webkit-scrollbar-track {
  background: var(--background);
}

.scroll-safe::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 3px;
}

.scroll-safe::-webkit-scrollbar-thumb:hover {
  background: var(--muted-foreground);
}

/* Content Security */
.content-safe {
  /* Prevent content from breaking layout */
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  
  /* Prevent XSS through CSS */
  content: none;
}

.content-safe * {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Image Protection */
.img-safe {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

.img-safe.img-cover {
  object-fit: cover;
}

.img-safe.img-fill {
  object-fit: fill;
}

/* Video Protection */
.video-safe {
  max-width: 100%;
  height: auto;
}

/* Iframe Protection */
.iframe-safe {
  max-width: 100%;
  border: none;
  overflow: hidden;
}

/* Print Protection */
@media print {
  .print-safe {
    word-wrap: break-word;
    overflow-wrap: break-word;
    page-break-inside: avoid;
  }
  
  .print-safe .no-print {
    display: none;
  }
}
