'use client';

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuthStatus } from '@/hooks/useAuthState';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = '/login',
  fallback 
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    if (status === 'loading') {
      // Still loading, don't do anything yet
      return;
    }

    if (requireAuth && status === 'unauthenticated') {
      // Need auth but not authenticated, redirect to login
      setIsRedirecting(true);
      const currentPath = window.location.pathname + window.location.search;
      const loginUrl = `${redirectTo}?callbackUrl=${encodeURIComponent(currentPath)}`;
      router.push(loginUrl);
      return;
    }

    if (!requireAuth && status === 'authenticated') {
      // Don't need auth but authenticated, redirect to dashboard
      setIsRedirecting(true);
      router.push('/dashboard');
      return;
    }
  }, [status, requireAuth, redirectTo, router]);

  // Show loading while session is loading or while redirecting
  if (status === 'loading' || isRedirecting) {
    return fallback || (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Show content if auth requirements are met
  if (requireAuth && status === 'authenticated') {
    return <>{children}</>;
  }

  if (!requireAuth && status === 'unauthenticated') {
    return <>{children}</>;
  }

  // Fallback for any other state
  return fallback || (
    <div className="flex items-center justify-center min-h-[400px]">
      <LoadingSpinner size="lg" />
    </div>
  );
}

interface AuthStateProps {
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
}

export function AuthState({ children, loadingComponent }: AuthStateProps) {
  const { status } = useSession();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return loadingComponent || (
      <div className="flex items-center justify-center min-h-[200px]">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  // Show loading while session is being determined
  if (status === 'loading') {
    return loadingComponent || (
      <div className="flex items-center justify-center min-h-[200px]">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  return <>{children}</>;
}

interface ConditionalAuthContentProps {
  children: React.ReactNode;
  authenticated?: React.ReactNode;
  unauthenticated?: React.ReactNode;
  loading?: React.ReactNode;
}

export function ConditionalAuthContent({
  children,
  authenticated,
  unauthenticated,
  loading
}: ConditionalAuthContentProps) {
  const { isAuthenticated, isLoading } = useAuthStatus();

  if (isLoading) {
    return loading || (
      <div className="flex items-center justify-center min-h-[100px]">
        <LoadingSpinner size="sm" />
      </div>
    );
  }

  if (isAuthenticated) {
    return authenticated || children;
  }

  if (!isAuthenticated) {
    return unauthenticated || children;
  }

  return <>{children}</>;
}
