'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Clock, RefreshCw, LogOut } from 'lucide-react';
import { useSessionTimeout } from '@/hooks/useSessionMonitor';
import { signOut } from 'next-auth/react';

interface SessionTimeoutWarningProps {
  warningMinutes?: number;
  className?: string;
}

export function SessionTimeoutWarning({ 
  warningMinutes = 5, 
  className 
}: SessionTimeoutWarningProps) {
  const [countdown, setCountdown] = useState(0);
  
  const { 
    timeoutWarning, 
    dismissWarning, 
    extendSession 
  } = useSessionTimeout({
    warningMinutes,
    onExpired: () => {
      // Session expired, redirect to login
      signOut({ callbackUrl: '/login' });
    }
  });

  // Countdown timer
  useEffect(() => {
    if (!timeoutWarning.show) {
      setCountdown(0);
      return;
    }

    setCountdown(timeoutWarning.minutesLeft * 60); // Convert to seconds

    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [timeoutWarning.show, timeoutWarning.minutesLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleExtendSession = async () => {
    try {
      await extendSession();
    } catch (error) {
      console.error('Failed to extend session:', error);
    }
  };

  const handleLogout = () => {
    signOut({ callbackUrl: '/login' });
  };

  if (!timeoutWarning.show) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-yellow-600">
            <Clock className="h-5 w-5" />
            Session Timeout Warning
          </CardTitle>
          <CardDescription>
            Your session will expire soon due to inactivity.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription className="text-center">
              <div className="text-2xl font-bold text-red-600 mb-2">
                {formatTime(countdown)}
              </div>
              <div className="text-sm text-muted-foreground">
                Time remaining until automatic logout
              </div>
            </AlertDescription>
          </Alert>

          <div className="text-sm text-muted-foreground">
            You will be automatically logged out to protect your account security. 
            Click "Stay Logged In" to extend your session.
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={handleExtendSession}
              className="flex-1"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Stay Logged In
            </Button>
            <Button 
              onClick={handleLogout}
              variant="outline"
              className="flex-1"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout Now
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface SessionStatusIndicatorProps {
  className?: string;
}

export function SessionStatusIndicator({ className }: SessionStatusIndicatorProps) {
  const { sessionState } = useSessionTimeout();
  const [showDetails, setShowDetails] = useState(false);

  if (!sessionState.isAuthenticated) {
    return null;
  }

  const getStatusColor = () => {
    if (!sessionState.isOnline) return 'bg-red-500';
    if (sessionState.isExpired) return 'bg-red-500';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!sessionState.isOnline) return 'Offline';
    if (sessionState.isExpired) return 'Session Expired';
    return 'Online';
  };

  return (
    <div className={className}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowDetails(!showDetails)}
        className="h-8 px-2"
      >
        <div className={`w-2 h-2 rounded-full mr-2 ${getStatusColor()}`} />
        <span className="text-xs">{getStatusText()}</span>
      </Button>

      {showDetails && (
        <div className="absolute top-full right-0 mt-1 p-3 bg-popover border rounded-md shadow-md min-w-[200px] z-50">
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span>Status:</span>
              <span className={sessionState.isOnline ? 'text-green-600' : 'text-red-600'}>
                {getStatusText()}
              </span>
            </div>
            <div className="flex justify-between">
              <span>User:</span>
              <span className="truncate ml-2">{sessionState.user?.email}</span>
            </div>
            {sessionState.sessionId && (
              <div className="flex justify-between">
                <span>Session:</span>
                <span className="font-mono text-xs truncate ml-2">
                  {sessionState.sessionId.slice(-8)}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface OfflineIndicatorProps {
  className?: string;
}

export function OfflineIndicator({ className }: OfflineIndicatorProps) {
  const { sessionState } = useSessionTimeout();

  if (sessionState.isOnline) {
    return null;
  }

  return (
    <Alert className={className}>
      <AlertDescription className="flex items-center gap-2">
        <div className="w-2 h-2 bg-red-500 rounded-full" />
        You are currently offline. Some features may not be available.
      </AlertDescription>
    </Alert>
  );
}
