'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, AlertTriangle, RefreshCw, Home } from 'lucide-react';
import Link from 'next/link';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  context?: 'csrf' | 'auth' | 'api' | 'general';
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

export class SecurityErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorId: `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // Log security errors with enhanced context
    const securityContext = {
      errorId: this.state.errorId,
      context: this.props.context || 'general',
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'server',
      componentStack: errorInfo.componentStack,
      errorStack: error.stack,
      errorMessage: error.message,
      errorName: error.name
    };

    console.error('Security Error Boundary caught an error:', securityContext);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportSecurityError(error, errorInfo, securityContext);
    }
  }

  private reportSecurityError = (error: Error, errorInfo: ErrorInfo, context: any) => {
    // Send to Sentry or other error tracking service
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: errorInfo,
          security: context
        },
        tags: {
          errorBoundary: 'security',
          context: this.props.context || 'general',
          errorType: 'security'
        },
        extra: context
      });
    }

    // Also send to our custom error reporting endpoint
    if (typeof window !== 'undefined') {
      fetch('/api/error-reporting', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'security_error',
          error: {
            message: error.message,
            name: error.name,
            stack: error.stack
          },
          context,
          timestamp: new Date().toISOString()
        })
      }).catch(reportError => {
        console.error('Failed to report security error:', reportError);
      });
    }
  };

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default security error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <Shield className="mx-auto h-16 w-16 text-red-500" />
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
                Security Error
              </h2>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {this.getErrorMessage()}
              </p>
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4 text-left">
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                    Error Details (Development Only)
                  </summary>
                  <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
                    {this.state.error.message}
                    {'\n\n'}
                    {this.state.error.stack}
                  </pre>
                </details>
              )}
            </div>

            <div className="flex flex-col space-y-3">
              <button
                onClick={this.handleRetry}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </button>

              <Link
                href="/"
                className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Link>

              {this.props.context === 'csrf' && (
                <button
                  onClick={() => window.location.reload()}
                  className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Page
                </button>
              )}
            </div>

            {this.state.errorId && (
              <p className="text-center text-xs text-gray-500 dark:text-gray-400">
                Error ID: {this.state.errorId}
              </p>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }

  private getErrorMessage(): string {
    const context = this.props.context;
    const error = this.state.error;

    if (context === 'csrf') {
      return 'A security token error occurred. This might be due to an expired session or network issue.';
    }

    if (context === 'auth') {
      return 'An authentication error occurred. Please log in again to continue.';
    }

    if (context === 'api') {
      return 'A secure API communication error occurred. Please try again.';
    }

    if (error?.message?.toLowerCase().includes('csrf')) {
      return 'A security token error occurred. Please refresh the page and try again.';
    }

    if (error?.message?.toLowerCase().includes('unauthorized')) {
      return 'You are not authorized to perform this action. Please log in and try again.';
    }

    return 'A security-related error occurred. Please try again or contact support if the problem persists.';
  }
}

// Specialized security error boundaries for different contexts
export const CSRFErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SecurityErrorBoundary
    context="csrf"
    onError={(error, errorInfo) => {
      console.error('CSRF Error:', { error, errorInfo });
    }}
  >
    {children}
  </SecurityErrorBoundary>
);

export const AuthErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SecurityErrorBoundary
    context="auth"
    onError={(error, errorInfo) => {
      console.error('Auth Error:', { error, errorInfo });
    }}
  >
    {children}
  </SecurityErrorBoundary>
);

export const APIErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <SecurityErrorBoundary
    context="api"
    onError={(error, errorInfo) => {
      console.error('API Error:', { error, errorInfo });
    }}
  >
    {children}
  </SecurityErrorBoundary>
);

// Higher-order component for wrapping components with security error boundary
export function withSecurityErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  context?: 'csrf' | 'auth' | 'api' | 'general'
) {
  return function WrappedComponent(props: P) {
    return (
      <SecurityErrorBoundary context={context}>
        <Component {...props} />
      </SecurityErrorBoundary>
    );
  };
}

export default SecurityErrorBoundary;
