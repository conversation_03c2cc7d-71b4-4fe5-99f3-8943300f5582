'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  BookOpen,
  ArrowRight,
  Star,
  Users,
  DollarSign,
  Lightbulb,
  BarChart3,
  Calendar,
  MapPin,
  Award,
  Zap,
  Brain,
  Compass,
  Network,
  GraduationCap,
  Timer,
  Filter,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import { getReadinessLevel } from '@/lib/assessmentScoring';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';

interface AssessmentResultsProps {
  assessmentId: string;
}

interface AssessmentResultsData {
  assessment: {
    id: string;
    status: string;
    completedAt: Date | null;
    currentStep: number;
  };
  insights: {
    scores: {
      readinessScore: number;
      riskTolerance: number;
      urgencyLevel: number;
      skillsConfidence: number;
      supportLevel: number;
      financialReadiness: number;
    };
    primaryMotivation: string;
    topSkills: string[];
    biggestObstacles: string[];
    recommendedTimeline: string;
    keyRecommendations: string[];
    careerPathSuggestions: string[];
  };
  careerSuggestions: Array<{
    careerPath: {
      id: string;
      name: string;
      slug: string;
      overview: string;
      pros: string;
      cons: string;
      actionableSteps: any;
    };
    score: number;
    matchReason?: string;
  }>;
  personalizedRecommendations?: {
    learningResources: any[];
    skillGaps: any[];
    nextSteps: string[];
  };
}

const AssessmentResults: React.FC<AssessmentResultsProps> = ({ assessmentId }) => {
  const [results, setResults] = useState<AssessmentResultsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchResults = async () => {
      if (!assessmentId) {
        setLoading(false);
        setError('Assessment ID is required.');
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/assessment/results/${assessmentId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }
        
        const data = await response.json();
        setResults(data.data);
      } catch (err) {
        console.error('Error fetching assessment results:', err);
        const friendlyError = getUserFriendlyError(err, 'assessment');
        setError(friendlyError.message);
      } finally {
        setLoading(false);
      }
    };

    fetchResults();
  }, [assessmentId]);

  if (loading) {
    return (
      <div className="container mx-auto py-8 space-y-6">
        <div className="text-center mb-8">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <Skeleton className="h-6 w-3/4 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    const friendlyError = getUserFriendlyError(error, 'assessment');
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-700 mb-2">{friendlyError.title}</h2>
          <p className="text-red-600 mb-4">{friendlyError.message}</p>
          {friendlyError.action && (
            <p className="text-sm text-gray-600 mb-4">{friendlyError.action}</p>
          )}
          <Button asChild>
            <Link href="/assessment">Take Assessment Again</Link>
          </Button>
        </Card>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">No Results Available</h2>
          <p className="text-gray-600 mb-4">Complete your assessment to see personalized results.</p>
          <Button asChild>
            <Link href="/assessment">Take Assessment</Link>
          </Button>
        </Card>
      </div>
    );
  }

  const { insights, careerSuggestions, personalizedRecommendations } = results;
  const readinessLevel = getReadinessLevel(insights.scores.readinessScore);

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Your Career Assessment Results
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Personalized insights and recommendations based on your responses
        </p>
      </div>

      {/* Overall Readiness Score */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            Career Readiness Assessment
          </h2>
          <Badge variant={insights.scores.readinessScore >= 70 ? 'default' : 'secondary'}>
            {readinessLevel}
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <ScoreCard 
            title="Overall Readiness" 
            score={insights.scores.readinessScore} 
            icon={<Target className="h-4 w-4" />}
            max={100}
          />
          <ScoreCard 
            title="Skills Confidence" 
            score={insights.scores.skillsConfidence} 
            icon={<Star className="h-4 w-4" />}
            max={100}
          />
          <ScoreCard 
            title="Financial Readiness" 
            score={insights.scores.financialReadiness} 
            icon={<DollarSign className="h-4 w-4" />}
            max={5}
          />
          <ScoreCard 
            title="Support Level" 
            score={insights.scores.supportLevel} 
            icon={<Users className="h-4 w-4" />}
            max={5}
          />
          <ScoreCard 
            title="Risk Tolerance" 
            score={insights.scores.riskTolerance} 
            icon={<TrendingUp className="h-4 w-4" />}
            max={5}
          />
          <ScoreCard 
            title="Urgency Level" 
            score={insights.scores.urgencyLevel} 
            icon={<Clock className="h-4 w-4" />}
            max={5}
          />
        </div>
      </Card>

      {/* Key Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Lightbulb className="h-5 w-5 text-yellow-600" />
            Key Insights
          </h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Primary Motivation</h4>
              <p className="text-gray-600 dark:text-gray-400">{insights.primaryMotivation}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Top Skills</h4>
              <div className="flex flex-wrap gap-2 mt-2">
                {insights.topSkills.map((skill, index) => (
                  <Badge key={index} variant="outline">{skill}</Badge>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Recommended Timeline</h4>
              <p className="text-gray-600 dark:text-gray-400">{insights.recommendedTimeline}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Key Recommendations
          </h3>
          <ul className="space-y-3">
            {insights.keyRecommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2">
                <ArrowRight className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">{recommendation}</span>
              </li>
            ))}
          </ul>
        </Card>
      </div>

      {/* Enhanced Career Path Suggestions */}
      {careerSuggestions.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <Compass className="h-5 w-5 text-emerald-600" />
              Your Career Path Recommendations
            </h3>
            <Badge variant="outline" className="text-emerald-600 border-emerald-600">
              {careerSuggestions.length} Matches Found
            </Badge>
          </div>

          <div className="mb-6 p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
            <p className="text-sm text-emerald-700 dark:text-emerald-300">
              <Brain className="h-4 w-4 inline mr-2" />
              These career paths are ranked based on your assessment responses, skills, and career goals.
              Each suggestion includes detailed insights to help you make informed decisions.
            </p>
          </div>

          <Tabs defaultValue="top-matches" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="top-matches">Top Matches</TabsTrigger>
              <TabsTrigger value="all-suggestions">All Suggestions</TabsTrigger>
            </TabsList>

            <TabsContent value="top-matches" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {careerSuggestions.slice(0, 4).map((suggestion, index) => (
                  <EnhancedCareerSuggestionCard
                    key={suggestion.careerPath.id}
                    suggestion={suggestion}
                    rank={index + 1}
                    isTopMatch={index < 2}
                  />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="all-suggestions" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {careerSuggestions.map((suggestion, index) => (
                  <CareerSuggestionCard
                    key={suggestion.careerPath.id}
                    suggestion={suggestion}
                    rank={index + 1}
                  />
                ))}
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
            <Button asChild>
              <Link href="/career-paths">Explore All Career Paths</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/ai/career-recommendations">Get AI-Powered Recommendations</Link>
            </Button>
          </div>
        </Card>
      )}

      {/* Personalized Recommendations */}
      {personalizedRecommendations && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Enhanced Next Steps with Timeline */}
          {personalizedRecommendations.nextSteps.length > 0 && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-600" />
                  Your Action Plan
                </h3>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  {personalizedRecommendations.nextSteps.length} Steps
                </Badge>
              </div>

              <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <p className="text-sm text-green-700 dark:text-green-300">
                  <MapPin className="h-4 w-4 inline mr-2" />
                  Follow this roadmap to systematically advance toward your career goals.
                </p>
              </div>

              <div className="space-y-4">
                {personalizedRecommendations.nextSteps.map((step, index) => {
                  const timeframes = ['Week 1-2', 'Week 3-4', 'Month 2', 'Month 3', 'Month 4-6', 'Ongoing'];
                  const priorities = ['Critical', 'High', 'Medium', 'Medium', 'Low', 'Maintenance'];

                  return (
                    <div key={index} className="flex items-start gap-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <p className="text-gray-900 dark:text-gray-100 font-medium">{step}</p>
                          <div className="flex items-center gap-2 ml-4">
                            <Badge variant={index < 2 ? 'destructive' : index < 4 ? 'default' : 'secondary'} className="text-xs">
                              {priorities[index] || 'Medium'}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{timeframes[index] || 'Flexible'}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Target className="h-3 w-3" />
                            <span>Step {index + 1} of {personalizedRecommendations.nextSteps.length}</span>
                          </div>
                        </div>

                        {/* Progress indicator */}
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-500 mb-1">
                            <span>Progress</span>
                            <span>0%</span>
                          </div>
                          <Progress value={0} className="h-1" />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="mt-6 flex flex-col sm:flex-row gap-3">
                <Button asChild className="flex-1">
                  <Link href="/dashboard">
                    <Calendar className="h-4 w-4 mr-2" />
                    Track Progress
                  </Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/ai/career-recommendations">
                    <Brain className="h-4 w-4 mr-2" />
                    Get AI Guidance
                  </Link>
                </Button>
              </div>
            </Card>
          )}

          {/* Enhanced Skill Gap Analysis */}
          {personalizedRecommendations.skillGaps.length > 0 && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-orange-600" />
                  Skill Gap Analysis
                </h3>
                <Badge variant="outline" className="text-orange-600 border-orange-600">
                  {personalizedRecommendations.skillGaps.filter(g => g.priority === 'high').length} High Priority
                </Badge>
              </div>

              <div className="mb-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  <Zap className="h-4 w-4 inline mr-2" />
                  Focus on high-priority skills first to maximize your career transition success.
                </p>
              </div>

              <div className="space-y-4">
                {personalizedRecommendations.skillGaps.map((gap, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-gray-900 dark:text-gray-100">{gap.skill}</span>
                          <Badge variant={gap.priority === 'high' ? 'destructive' : gap.priority === 'medium' ? 'default' : 'secondary'}>
                            {gap.priority} priority
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{gap.reason}</p>

                        {/* Skill Progress Visualization */}
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>Current Level</span>
                            <span>Target Level</span>
                          </div>
                          <Progress value={gap.currentLevel || 20} className="h-2" />
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-600">{gap.currentLevel || 20}%</span>
                            <span className="text-orange-600 font-medium">{gap.targetLevel || 80}%</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <Timer className="h-3 w-3" />
                        <span>Est. {gap.estimatedTime || '2-3 months'}</span>
                      </div>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/ai/skills-analysis?skill=${encodeURIComponent(gap.skill)}`}>
                          <GraduationCap className="h-3 w-3 mr-1" />
                          Learn
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 text-center">
                <Button asChild>
                  <Link href="/ai/skills-analysis">Get Detailed Skills Analysis</Link>
                </Button>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Enhanced Learning Resources */}
      {personalizedRecommendations?.learningResources && personalizedRecommendations.learningResources.length > 0 && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-purple-600" />
              Personalized Learning Resources
            </h3>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <Badge variant="outline" className="text-purple-600 border-purple-600">
                {personalizedRecommendations.learningResources.length} Resources
              </Badge>
            </div>
          </div>

          <div className="mb-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <p className="text-sm text-purple-700 dark:text-purple-300">
              <Award className="h-4 w-4 inline mr-2" />
              These resources are specifically chosen based on your career goals, current skills, and learning preferences.
            </p>
          </div>

          <Tabs defaultValue="priority" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="priority">Priority</TabsTrigger>
              <TabsTrigger value="type">By Type</TabsTrigger>
              <TabsTrigger value="level">By Level</TabsTrigger>
            </TabsList>

            <TabsContent value="priority" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {personalizedRecommendations.learningResources.slice(0, 6).map((resource, index) => (
                  <EnhancedResourceCard key={resource.id} resource={resource} isPriority={index < 3} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="type" className="mt-6">
              <div className="space-y-6">
                {['COURSE', 'BOOK', 'VIDEO', 'ARTICLE'].map(type => {
                  const typeResources = personalizedRecommendations.learningResources.filter(r => r.type === type);
                  if (typeResources.length === 0) return null;

                  return (
                    <div key={type}>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3 capitalize">
                        {type.toLowerCase()}s ({typeResources.length})
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {typeResources.slice(0, 3).map((resource) => (
                          <EnhancedResourceCard key={resource.id} resource={resource} />
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="level" className="mt-6">
              <div className="space-y-6">
                {['BEGINNER', 'INTERMEDIATE', 'ADVANCED'].map(level => {
                  const levelResources = personalizedRecommendations.learningResources.filter(r => r.skillLevel === level);
                  if (levelResources.length === 0) return null;

                  return (
                    <div key={level}>
                      <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                        {level} Level ({levelResources.length})
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {levelResources.slice(0, 3).map((resource) => (
                          <EnhancedResourceCard key={resource.id} resource={resource} />
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
            <Button asChild>
              <Link href="/resources">Browse All Resources</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/learning-paths">View Learning Paths</Link>
            </Button>
          </div>
        </Card>
      )}

      {/* Career Roadmap & Industry Insights */}
      {careerSuggestions.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Career Roadmap */}
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
              <Network className="h-5 w-5 text-indigo-600" />
              Career Roadmap
            </h3>

            <div className="mb-4 p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
              <p className="text-sm text-indigo-700 dark:text-indigo-300">
                <Compass className="h-4 w-4 inline mr-2" />
                Your personalized path to {careerSuggestions[0]?.careerPath.name}
              </p>
            </div>

            <div className="space-y-4">
              {[
                { phase: 'Foundation', duration: '0-3 months', description: 'Build core skills and knowledge base' },
                { phase: 'Development', duration: '3-9 months', description: 'Gain practical experience and portfolio' },
                { phase: 'Specialization', duration: '9-18 months', description: 'Focus on specific areas and expertise' },
                { phase: 'Professional', duration: '18+ months', description: 'Advanced skills and leadership roles' }
              ].map((stage, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      index === 0 ? 'bg-indigo-600 text-white' :
                      index === 1 ? 'bg-indigo-100 text-indigo-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      {index + 1}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">{stage.phase}</h4>
                      <span className="text-xs text-gray-500">{stage.duration}</span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{stage.description}</p>
                    {index < 3 && <div className="w-px h-4 bg-gray-200 dark:bg-gray-700 ml-4 mt-2"></div>}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6">
              <Button variant="outline" size="sm" asChild className="w-full">
                <Link href="/learning-paths">
                  <MapPin className="h-4 w-4 mr-2" />
                  View Detailed Roadmap
                </Link>
              </Button>
            </div>
          </Card>

          {/* Industry Insights */}
          <Card className="p-6">
            <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Industry Insights
            </h3>

            <div className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-700 dark:text-blue-300">Market Outlook</span>
                </div>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Strong growth expected in {careerSuggestions[0]?.careerPath.name} with 15% job growth projected over next 5 years.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <DollarSign className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">$75K+</div>
                  <div className="text-xs text-gray-500">Avg. Starting Salary</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Users className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">50K+</div>
                  <div className="text-xs text-gray-500">Open Positions</div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Top Skills in Demand</h4>
                <div className="flex flex-wrap gap-2">
                  {insights.topSkills.slice(0, 4).map((skill, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">Remote Work Options</h4>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">85%</span>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <Button variant="outline" size="sm" asChild className="w-full">
                <Link href="/industry-insights">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Full Report
                </Link>
              </Button>
            </div>
          </Card>
        </div>
      )}

      {/* Networking & Mentorship Suggestions */}
      {careerSuggestions.length > 0 && (
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <Network className="h-5 w-5 text-teal-600" />
            Networking & Mentorship
          </h3>

          <div className="mb-6 p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
            <p className="text-sm text-teal-700 dark:text-teal-300">
              <Users className="h-4 w-4 inline mr-2" />
              Connect with professionals in your target field to accelerate your career transition.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <Network className="h-8 w-8 text-teal-600 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Professional Networks</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Join industry-specific communities and professional associations.
              </p>
              <Button size="sm" variant="outline" asChild>
                <Link href="/networking">Find Networks</Link>
              </Button>
            </div>

            <div className="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <GraduationCap className="h-8 w-8 text-teal-600 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Find Mentors</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Connect with experienced professionals for guidance and advice.
              </p>
              <Button size="sm" variant="outline" asChild>
                <Link href="/mentorship">Find Mentors</Link>
              </Button>
            </div>

            <div className="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <Calendar className="h-8 w-8 text-teal-600 mx-auto mb-3" />
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Industry Events</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Attend conferences, workshops, and networking events in your area.
              </p>
              <Button size="sm" variant="outline" asChild>
                <Link href="/events">Find Events</Link>
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button asChild size="lg">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
        <Button variant="outline" size="lg" asChild>
          <Link href="/assessment">Retake Assessment</Link>
        </Button>
        <Button variant="outline" size="lg" asChild>
          <Link href={`/assessment/results/${assessmentId}`}>View Full Report</Link>
        </Button>
      </div>
    </div>
  );
};

// Helper component for score cards
const ScoreCard: React.FC<{
  title: string;
  score: number;
  icon: React.ReactNode;
  max: number;
}> = ({ title, score, icon, max }) => {
  const percentage = (score / max) * 100;

  return (
    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{title}</span>
        {icon}
      </div>
      <div className="flex items-center gap-2">
        <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          {score}{max === 100 ? '' : `/${max}`}
        </span>
        {max === 100 && <span className="text-sm text-gray-500">%</span>}
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-500"
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  );
};

// Helper component for career suggestion cards
const CareerSuggestionCard: React.FC<{
  suggestion: {
    careerPath: {
      id: string;
      name: string;
      slug: string;
      overview: string;
      pros: string;
      cons: string;
      actionableSteps: any;
    };
    score: number;
    matchReason?: string;
  };
  rank: number;
}> = ({ suggestion, rank }) => {
  const { careerPath, score, matchReason } = suggestion;

  // Parse pros and cons if they're JSON strings
  let pros: string[] = [];
  let cons: string[] = [];

  try {
    pros = typeof careerPath.pros === 'string' ? JSON.parse(careerPath.pros) : careerPath.pros;
    cons = typeof careerPath.cons === 'string' ? JSON.parse(careerPath.cons) : careerPath.cons;
  } catch (e) {
    // If parsing fails, treat as single string
    pros = [careerPath.pros];
    cons = [careerPath.cons];
  }

  return (
    <Card className="p-4 hover:shadow-lg transition-shadow relative">
      <div className="absolute top-2 right-2">
        <Badge variant={rank <= 3 ? 'default' : 'secondary'}>
          #{rank}
        </Badge>
      </div>

      <div className="mb-3">
        <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-1 pr-12">
          {careerPath.name}
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
          {careerPath.overview}
        </p>
      </div>

      {matchReason && (
        <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs text-blue-700 dark:text-blue-300">
          <strong>Match:</strong> {matchReason}
        </div>
      )}

      <div className="space-y-2 mb-4">
        <div>
          <span className="text-xs font-medium text-green-700 dark:text-green-300">Top Benefits:</span>
          <ul className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {pros.slice(0, 2).map((pro, index) => (
              <li key={index} className="flex items-start gap-1">
                <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="line-clamp-1">{pro}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">Match Score:</span>
          <Badge variant="outline">{score}</Badge>
        </div>
        <Button size="sm" variant="ghost" asChild>
          <Link href={`/career-paths/${careerPath.slug}`}>
            Learn More
          </Link>
        </Button>
      </div>
    </Card>
  );
};

// Enhanced Career Suggestion Card with more details
const EnhancedCareerSuggestionCard: React.FC<{
  suggestion: {
    careerPath: {
      id: string;
      name: string;
      slug: string;
      overview: string;
      pros: string;
      cons: string;
      actionableSteps: any;
    };
    score: number;
    matchReason?: string;
  };
  rank: number;
  isTopMatch?: boolean;
}> = ({ suggestion, rank, isTopMatch = false }) => {
  const { careerPath, score, matchReason } = suggestion;

  // Parse pros and cons if they're JSON strings
  let pros: string[] = [];
  let cons: string[] = [];

  try {
    pros = typeof careerPath.pros === 'string' ? JSON.parse(careerPath.pros) : careerPath.pros;
    cons = typeof careerPath.cons === 'string' ? JSON.parse(careerPath.cons) : careerPath.cons;
  } catch (e) {
    pros = [careerPath.pros];
    cons = [careerPath.cons];
  }

  return (
    <Card className={`p-6 hover:shadow-lg transition-all relative ${isTopMatch ? 'ring-2 ring-emerald-200 dark:ring-emerald-800' : ''}`}>
      <div className="absolute top-3 right-3 flex items-center gap-2">
        {isTopMatch && (
          <Badge variant="default" className="bg-emerald-600">
            <Star className="h-3 w-3 mr-1" />
            Top Match
          </Badge>
        )}
        <Badge variant={rank <= 3 ? 'default' : 'secondary'}>
          #{rank}
        </Badge>
      </div>

      <div className="mb-4 pr-20">
        <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100 mb-2">
          {careerPath.name}
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
          {careerPath.overview}
        </p>
      </div>

      {matchReason && (
        <div className="mb-4 p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
          <div className="flex items-start gap-2">
            <Brain className="h-4 w-4 text-emerald-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-xs font-medium text-emerald-700 dark:text-emerald-300 mb-1">Why this matches you:</p>
              <p className="text-xs text-emerald-600 dark:text-emerald-400">{matchReason}</p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-3 mb-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-700 dark:text-green-300">Key Benefits</span>
          </div>
          <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            {pros.slice(0, 3).map((pro, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                <span className="line-clamp-1">{pro}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Match Score Visualization */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Match Score</span>
            <span className="font-medium text-emerald-600">{score}%</span>
          </div>
          <Progress value={score} className="h-2" />
        </div>
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center gap-3 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <DollarSign className="h-3 w-3" />
            <span>Salary Info</span>
          </div>
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            <span>Growth</span>
          </div>
        </div>
        <Button size="sm" asChild>
          <Link href={`/career-paths/${careerPath.slug}`}>
            Explore Path
            <ExternalLink className="h-3 w-3 ml-1" />
          </Link>
        </Button>
      </div>
    </Card>
  );
};

// Enhanced Resource Card with more details
const EnhancedResourceCard: React.FC<{
  resource: any;
  isPriority?: boolean;
}> = ({ resource, isPriority = false }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'COURSE': return <GraduationCap className="h-4 w-4" />;
      case 'BOOK': return <BookOpen className="h-4 w-4" />;
      case 'VIDEO': return <Star className="h-4 w-4" />;
      case 'ARTICLE': return <Target className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'BEGINNER': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'INTERMEDIATE': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'ADVANCED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow relative ${isPriority ? 'ring-1 ring-purple-200 dark:ring-purple-800' : ''}`}>
      {isPriority && (
        <div className="absolute top-2 right-2">
          <Badge variant="default" className="bg-purple-600 text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Priority
          </Badge>
        </div>
      )}

      <div className="mb-3">
        <div className="flex items-start gap-2 mb-2">
          <div className="text-purple-600 mt-1">
            {getTypeIcon(resource.type)}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 line-clamp-2 pr-8">
              {resource.title}
            </h4>
          </div>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
          {resource.description}
        </p>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Badge variant="outline" className={getLevelColor(resource.skillLevel)}>
            {resource.skillLevel}
          </Badge>
          <Badge variant="outline">{resource.category}</Badge>
        </div>

        {resource.duration && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Timer className="h-3 w-3" />
            <span>{resource.duration}</span>
          </div>
        )}

        {resource.averageRating && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span>{resource.averageRating.toFixed(1)} ({resource.totalRatings} reviews)</span>
          </div>
        )}
      </div>

      <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className={`text-xs px-2 py-1 rounded ${
            resource.cost === 'FREE' ? 'bg-green-100 text-green-800' :
            resource.cost === 'PAID' ? 'bg-blue-100 text-blue-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            {resource.cost}
          </span>
          <Button size="sm" variant="ghost" asChild>
            <Link href={`/resources/${resource.id}`}>
              View Resource
              <ExternalLink className="h-3 w-3 ml-1" />
            </Link>
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default AssessmentResults;
