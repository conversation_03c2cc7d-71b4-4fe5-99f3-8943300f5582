'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

/**
 * Self-Healing Error Boundary for Interview Practice Feature
 * 
 * Provides automatic error recovery, user-friendly error messages,
 * and error reporting for the interview practice components.
 */
export class InterviewPracticeErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('Interview Practice Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report error to monitoring service
    this.reportError(error, errorInfo);

    // Attempt automatic recovery for certain error types
    this.attemptAutoRecovery(error);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // Report to error monitoring service (e.g., Sentry)
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        feature: 'interview-practice'
      };

      // Send to monitoring service
      console.error('Error Report:', errorReport);
      
      // In production, send to actual monitoring service
      if (process.env.NODE_ENV === 'production') {
        // Example: Sentry.captureException(error, { extra: errorReport });
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private attemptAutoRecovery = (error: Error) => {
    // Attempt automatic recovery for certain error types
    if (this.retryCount < this.maxRetries) {
      const recoveryStrategies = [
        // Network errors - retry after delay
        {
          condition: (err: Error) => err.message.includes('fetch') || err.message.includes('network'),
          action: () => {
            setTimeout(() => {
              this.handleRetry();
            }, 2000 * (this.retryCount + 1)); // Exponential backoff
          }
        },
        // State errors - reset component state
        {
          condition: (err: Error) => err.message.includes('state') || err.message.includes('undefined'),
          action: () => {
            setTimeout(() => {
              this.handleRetry();
            }, 1000);
          }
        },
        // Memory errors - force garbage collection and retry
        {
          condition: (err: Error) => err.message.includes('memory') || err.message.includes('heap'),
          action: () => {
            // Clear any large objects from memory
            if (window.gc) {
              window.gc();
            }
            setTimeout(() => {
              this.handleRetry();
            }, 3000);
          }
        }
      ];

      const strategy = recoveryStrategies.find(s => s.condition(error));
      if (strategy) {
        console.log(`Attempting auto-recovery for error: ${error.message}`);
        strategy.action();
      }
    }
  };

  private handleRetry = () => {
    this.retryCount++;
    console.log(`Retry attempt ${this.retryCount}/${this.maxRetries}`);
    
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleManualRetry = () => {
    this.retryCount = 0;
    this.handleRetry();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportBug = () => {
    const errorDetails = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString()
    };

    const mailtoLink = `mailto:<EMAIL>?subject=Interview Practice Error Report&body=${encodeURIComponent(
      `Error ID: ${this.state.errorId}\n\nError Details:\n${JSON.stringify(errorDetails, null, 2)}\n\nPlease describe what you were doing when this error occurred:`
    )}`;

    window.open(mailtoLink);
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-6 w-6 text-red-500" />
                <CardTitle className="text-red-600">Interview Practice Error</CardTitle>
              </div>
              <CardDescription>
                Something went wrong with the interview practice feature. Don't worry, we're here to help!
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Error ID:</strong> {this.state.errorId}
                  <br />
                  <strong>Time:</strong> {new Date().toLocaleString()}
                </AlertDescription>
              </Alert>

              {this.retryCount < this.maxRetries && (
                <Alert>
                  <RefreshCw className="h-4 w-4" />
                  <AlertDescription>
                    We're attempting to recover automatically. Retry attempt {this.retryCount}/{this.maxRetries}
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-4">
                <h3 className="font-semibold">What you can do:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    onClick={this.handleManualRetry}
                    className="flex items-center space-x-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span>Try Again</span>
                  </Button>
                  
                  <Button 
                    variant="outline"
                    onClick={this.handleGoHome}
                    className="flex items-center space-x-2"
                  >
                    <Home className="h-4 w-4" />
                    <span>Go Home</span>
                  </Button>
                  
                  <Button 
                    variant="outline"
                    onClick={this.handleReportBug}
                    className="flex items-center space-x-2"
                  >
                    <Bug className="h-4 w-4" />
                    <span>Report Bug</span>
                  </Button>
                </div>
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6">
                  <summary className="cursor-pointer font-semibold text-sm text-gray-600">
                    Developer Details (Development Only)
                  </summary>
                  <div className="mt-2 p-4 bg-gray-100 rounded text-xs font-mono">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    <div className="mb-2">
                      <strong>Stack:</strong>
                      <pre className="whitespace-pre-wrap">{this.state.error.stack}</pre>
                    </div>
                    {this.state.errorInfo && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap">{this.state.errorInfo.componentStack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default InterviewPracticeErrorBoundary;
