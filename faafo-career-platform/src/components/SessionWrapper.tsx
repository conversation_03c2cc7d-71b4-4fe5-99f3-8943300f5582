"use client";

import { SessionProvider } from 'next-auth/react';
import React, { useEffect, useState } from 'react';

interface SessionWrapperProps {
  children: React.ReactNode;
}

export default function SessionWrapper({
  children,
}: SessionWrapperProps) {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Monitor online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial state
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <SessionProvider
      refetchInterval={isOnline ? 5 * 60 : 0} // Only refetch when online
      refetchOnWindowFocus={isOnline} // Only refetch on focus when online
      refetchWhenOffline={false} // Don't refetch when offline
      basePath="/api/auth" // Ensure correct auth path
      baseUrl={typeof window !== 'undefined' ? window.location.origin : undefined}
    >
      {children}
    </SessionProvider>
  );
}
