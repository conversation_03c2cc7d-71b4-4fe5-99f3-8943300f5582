'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, GripVertical } from 'lucide-react';
import { Education } from './ResumeBuilder';

interface EducationFormProps {
  education: Education[];
  onChange: (education: Education[]) => void;
}

export function EducationForm({ education, onChange }: EducationFormProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      gpa: '',
      honors: '',
    };
    onChange([...education, newEducation]);
    setExpandedItems(prev => new Set([...Array.from(prev), newEducation.id]));
  };

  const updateEducation = (id: string, updates: Partial<Education>) => {
    onChange(
      education.map(edu =>
        edu.id === id ? { ...edu, ...updates } : edu
      )
    );
  };

  const removeEducation = (id: string) => {
    onChange(education.filter(edu => edu.id !== id));
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Education</CardTitle>
              <CardDescription>
                Add your educational background, starting with the most recent
              </CardDescription>
            </div>
            <Button onClick={addEducation} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Education
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {education.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No education added yet.</p>
              <p className="text-sm">Click "Add Education" to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {education.map((edu, index) => (
                <Card key={edu.id} className="border-l-4 border-l-green-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <GripVertical className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">
                            {edu.degree || 'New Degree'} 
                            {edu.field && ` in ${edu.field}`}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {edu.institution || 'Institution'}
                            {edu.startDate && edu.endDate && ` • ${edu.startDate} - ${edu.endDate}`}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(edu.id)}
                        >
                          {expandedItems.has(edu.id) ? 'Collapse' : 'Expand'}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeEducation(edu.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  {expandedItems.has(edu.id) && (
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`institution-${edu.id}`}>Institution *</Label>
                          <Input
                            id={`institution-${edu.id}`}
                            value={edu.institution}
                            onChange={(e) => updateEducation(edu.id, { institution: e.target.value })}
                            placeholder="University of California, Berkeley"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor={`degree-${edu.id}`}>Degree *</Label>
                          <Input
                            id={`degree-${edu.id}`}
                            value={edu.degree}
                            onChange={(e) => updateEducation(edu.id, { degree: e.target.value })}
                            placeholder="Bachelor of Science"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor={`field-${edu.id}`}>Field of Study</Label>
                        <Input
                          id={`field-${edu.id}`}
                          value={edu.field || ''}
                          onChange={(e) => updateEducation(edu.id, { field: e.target.value })}
                          placeholder="Computer Science"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`startDate-${edu.id}`}>Start Date</Label>
                          <Input
                            id={`startDate-${edu.id}`}
                            type="month"
                            value={edu.startDate || ''}
                            onChange={(e) => updateEducation(edu.id, { startDate: e.target.value })}
                          />
                        </div>
                        <div>
                          <Label htmlFor={`endDate-${edu.id}`}>End Date</Label>
                          <Input
                            id={`endDate-${edu.id}`}
                            type="month"
                            value={edu.endDate || ''}
                            onChange={(e) => updateEducation(edu.id, { endDate: e.target.value })}
                            placeholder="Leave empty if ongoing"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`gpa-${edu.id}`}>GPA</Label>
                          <Input
                            id={`gpa-${edu.id}`}
                            value={edu.gpa || ''}
                            onChange={(e) => updateEducation(edu.id, { gpa: e.target.value })}
                            placeholder="3.8/4.0"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`honors-${edu.id}`}>Honors/Awards</Label>
                          <Input
                            id={`honors-${edu.id}`}
                            value={edu.honors || ''}
                            onChange={(e) => updateEducation(edu.id, { honors: e.target.value })}
                            placeholder="Magna Cum Laude, Dean's List"
                          />
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
