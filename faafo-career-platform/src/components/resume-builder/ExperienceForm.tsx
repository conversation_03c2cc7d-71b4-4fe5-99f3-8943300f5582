'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2, GripVertical } from 'lucide-react';
import { Experience } from './ResumeBuilder';

interface ExperienceFormProps {
  experience: Experience[];
  onChange: (experience: Experience[]) => void;
}

export function ExperienceForm({ experience, onChange }: ExperienceFormProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      company: '',
      position: '',
      startDate: '',
      endDate: '',
      description: '',
      achievements: [],
    };
    onChange([...experience, newExperience]);
    setExpandedItems(prev => new Set([...Array.from(prev), newExperience.id]));
  };

  const updateExperience = (id: string, updates: Partial<Experience>) => {
    onChange(
      experience.map(exp =>
        exp.id === id ? { ...exp, ...updates } : exp
      )
    );
  };

  const removeExperience = (id: string) => {
    onChange(experience.filter(exp => exp.id !== id));
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const addAchievement = (experienceId: string) => {
    const exp = experience.find(e => e.id === experienceId);
    if (exp) {
      const achievements = [...(exp.achievements || []), ''];
      updateExperience(experienceId, { achievements });
    }
  };

  const updateAchievement = (experienceId: string, index: number, value: string) => {
    const exp = experience.find(e => e.id === experienceId);
    if (exp) {
      const achievements = [...(exp.achievements || [])];
      achievements[index] = value;
      updateExperience(experienceId, { achievements });
    }
  };

  const removeAchievement = (experienceId: string, index: number) => {
    const exp = experience.find(e => e.id === experienceId);
    if (exp) {
      const achievements = exp.achievements?.filter((_, i) => i !== index) || [];
      updateExperience(experienceId, { achievements });
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Work Experience</CardTitle>
              <CardDescription>
                Add your professional work experience, starting with the most recent
              </CardDescription>
            </div>
            <Button onClick={addExperience} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Experience
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {experience.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No work experience added yet.</p>
              <p className="text-sm">Click "Add Experience" to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {experience.map((exp, index) => (
                <Card key={exp.id} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <GripVertical className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <h4 className="font-medium">
                            {exp.position || 'New Position'} 
                            {exp.company && ` at ${exp.company}`}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {exp.startDate} {exp.endDate ? `- ${exp.endDate}` : '- Present'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(exp.id)}
                        >
                          {expandedItems.has(exp.id) ? 'Collapse' : 'Expand'}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeExperience(exp.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  {expandedItems.has(exp.id) && (
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`position-${exp.id}`}>Job Title *</Label>
                          <Input
                            id={`position-${exp.id}`}
                            value={exp.position}
                            onChange={(e) => updateExperience(exp.id, { position: e.target.value })}
                            placeholder="Software Engineer"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor={`company-${exp.id}`}>Company *</Label>
                          <Input
                            id={`company-${exp.id}`}
                            value={exp.company}
                            onChange={(e) => updateExperience(exp.id, { company: e.target.value })}
                            placeholder="Tech Corp"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`startDate-${exp.id}`}>Start Date *</Label>
                          <Input
                            id={`startDate-${exp.id}`}
                            type="month"
                            value={exp.startDate}
                            onChange={(e) => updateExperience(exp.id, { startDate: e.target.value })}
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor={`endDate-${exp.id}`}>End Date</Label>
                          <Input
                            id={`endDate-${exp.id}`}
                            type="month"
                            value={exp.endDate || ''}
                            onChange={(e) => updateExperience(exp.id, { endDate: e.target.value })}
                            placeholder="Leave empty if current position"
                          />
                        </div>
                      </div>

                      <div>
                        <Label htmlFor={`description-${exp.id}`}>Job Description</Label>
                        <Textarea
                          id={`description-${exp.id}`}
                          value={exp.description || ''}
                          onChange={(e) => updateExperience(exp.id, { description: e.target.value })}
                          placeholder="Describe your role and responsibilities..."
                          rows={3}
                        />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <Label>Key Achievements</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => addAchievement(exp.id)}
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add Achievement
                          </Button>
                        </div>
                        <div className="space-y-2">
                          {exp.achievements?.map((achievement, achievementIndex) => (
                            <div key={achievementIndex} className="flex gap-2">
                              <Input
                                value={achievement}
                                onChange={(e) => updateAchievement(exp.id, achievementIndex, e.target.value)}
                                placeholder="Increased team productivity by 25%..."
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeAchievement(exp.id, achievementIndex)}
                                className="text-destructive hover:text-destructive"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
