'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PersonalInfo } from './ResumeBuilder';
import { z } from 'zod';

// Validation schema for personal info
const personalInfoSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
  email: z.string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(254, 'Email is too long'),
  phone: z.string()
    .regex(/^[\+]?[1-9][\d\s\-\(\)]{0,15}$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),
  location: z.string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
  website: z.string()
    .url('Please enter a valid website URL')
    .optional()
    .or(z.literal('')),
  linkedIn: z.string()
    .url('Please enter a valid LinkedIn URL')
    .optional()
    .or(z.literal(''))
});

interface PersonalInfoFormProps {
  personalInfo: PersonalInfo;
  onChange: (personalInfo: PersonalInfo) => void;
}

type ValidationErrors = {
  [K in keyof PersonalInfo]?: string;
};

export function PersonalInfoForm({ personalInfo, onChange }: PersonalInfoFormProps) {
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<{[K in keyof PersonalInfo]?: boolean}>({});

  const validateField = (field: keyof PersonalInfo, value: string) => {
    try {
      const fieldSchema = personalInfoSchema.shape[field];
      if (fieldSchema) {
        fieldSchema.parse(value);
        setValidationErrors(prev => ({ ...prev, [field]: undefined }));
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        setValidationErrors(prev => ({
          ...prev,
          [field]: error.errors[0]?.message || 'Invalid value'
        }));
      }
    }
  };

  const updateField = (field: keyof PersonalInfo, value: string) => {
    // Validate the field if it has been touched
    if (touched[field]) {
      validateField(field, value);
    }

    onChange({
      ...personalInfo,
      [field]: value,
    });
  };

  const handleBlur = (field: keyof PersonalInfo) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateField(field, personalInfo[field] || '');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>
          Enter your basic contact information and professional details
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              value={personalInfo.firstName}
              onChange={(e) => updateField('firstName', e.target.value)}
              onBlur={() => handleBlur('firstName')}
              placeholder="John"
              required
              className={validationErrors.firstName ? 'border-red-500' : ''}
              maxLength={50}
            />
            {validationErrors.firstName && (
              <div className="text-red-500 text-sm mt-1">{validationErrors.firstName}</div>
            )}
          </div>
          <div>
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              value={personalInfo.lastName}
              onChange={(e) => updateField('lastName', e.target.value)}
              onBlur={() => handleBlur('lastName')}
              placeholder="Doe"
              required
              className={validationErrors.lastName ? 'border-red-500' : ''}
              maxLength={50}
            />
            {validationErrors.lastName && (
              <div className="text-red-500 text-sm mt-1">{validationErrors.lastName}</div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              value={personalInfo.email}
              onChange={(e) => updateField('email', e.target.value)}
              onBlur={() => handleBlur('email')}
              placeholder="<EMAIL>"
              required
              className={validationErrors.email ? 'border-red-500' : ''}
              maxLength={254}
            />
            {validationErrors.email && (
              <div className="text-red-500 text-sm mt-1">{validationErrors.email}</div>
            )}
          </div>
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={personalInfo.phone || ''}
              onChange={(e) => updateField('phone', e.target.value)}
              onBlur={() => handleBlur('phone')}
              placeholder="+****************"
              className={validationErrors.phone ? 'border-red-500' : ''}
            />
            {validationErrors.phone && (
              <div className="text-red-500 text-sm mt-1">{validationErrors.phone}</div>
            )}
          </div>
        </div>

        <div>
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            value={personalInfo.location || ''}
            onChange={(e) => updateField('location', e.target.value)}
            onBlur={() => handleBlur('location')}
            placeholder="San Francisco, CA"
            className={validationErrors.location ? 'border-red-500' : ''}
            maxLength={100}
          />
          {validationErrors.location && (
            <div className="text-red-500 text-sm mt-1">{validationErrors.location}</div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              type="url"
              value={personalInfo.website || ''}
              onChange={(e) => updateField('website', e.target.value)}
              onBlur={() => handleBlur('website')}
              placeholder="https://johndoe.com"
              className={validationErrors.website ? 'border-red-500' : ''}
            />
            {validationErrors.website && (
              <div className="text-red-500 text-sm mt-1">{validationErrors.website}</div>
            )}
          </div>
          <div>
            <Label htmlFor="linkedIn">LinkedIn Profile</Label>
            <Input
              id="linkedIn"
              type="url"
              value={personalInfo.linkedIn || ''}
              onChange={(e) => updateField('linkedIn', e.target.value)}
              onBlur={() => handleBlur('linkedIn')}
              placeholder="https://linkedin.com/in/johndoe"
              className={validationErrors.linkedIn ? 'border-red-500' : ''}
            />
            {validationErrors.linkedIn && (
              <div className="text-red-500 text-sm mt-1">{validationErrors.linkedIn}</div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
