'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Mail, Phone, MapPin, Globe, Linkedin } from 'lucide-react';
import { Resume } from './ResumeBuilder';
import { SafeTextDisplay, TextOverflow } from '@/components/ui/text-truncate';

interface ResumePreviewProps {
  resume: Resume;
}

export function ResumePreview({ resume }: ResumePreviewProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString + '-01'); // Add day for proper parsing
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
  };

  const formatDateRange = (startDate?: string, endDate?: string) => {
    const start = formatDate(startDate);
    const end = endDate ? formatDate(endDate) : 'Present';
    return start && end ? `${start} - ${end}` : '';
  };

  const getLevelBadgeColor = (level?: string) => {
    switch (level) {
      case 'BEGINNER':
        return 'bg-red-100 text-red-800';
      case 'INTERMEDIATE':
        return 'bg-yellow-100 text-yellow-800';
      case 'ADVANCED':
        return 'bg-blue-100 text-blue-800';
      case 'EXPERT':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="max-w-4xl mx-auto card-safe">
      <CardContent className="p-8 space-y-6 content-safe">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">
            <TextOverflow maxLines={2}>
              {resume.personalInfo.firstName} {resume.personalInfo.lastName}
            </TextOverflow>
          </h1>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
            {resume.personalInfo.email && (
              <div className="flex items-center gap-1">
                <Mail className="w-4 h-4" />
                {resume.personalInfo.email}
              </div>
            )}
            {resume.personalInfo.phone && (
              <div className="flex items-center gap-1">
                <Phone className="w-4 h-4" />
                {resume.personalInfo.phone}
              </div>
            )}
            {resume.personalInfo.location && (
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                {resume.personalInfo.location}
              </div>
            )}
            {resume.personalInfo.website && (
              <div className="flex items-center gap-1">
                <Globe className="w-4 h-4" />
                <a href={resume.personalInfo.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                  Website
                </a>
              </div>
            )}
            {resume.personalInfo.linkedIn && (
              <div className="flex items-center gap-1">
                <Linkedin className="w-4 h-4" />
                <a href={resume.personalInfo.linkedIn} target="_blank" rel="noopener noreferrer" className="hover:underline">
                  LinkedIn
                </a>
              </div>
            )}
          </div>
        </div>

        {/* Professional Summary */}
        {resume.summary && (
          <div>
            <h2 className="text-xl font-semibold mb-3">Professional Summary</h2>
            <SafeTextDisplay
              text={resume.summary}
              maxLength={2000}
              maxLines={6}
              className="text-muted-foreground leading-relaxed"
            />
          </div>
        )}

        {/* Experience */}
        {resume.experience.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Work Experience</h2>
            <div className="space-y-6">
              {resume.experience.map((exp) => (
                <div key={exp.id}>
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1 mr-4">
                      <TextOverflow maxLines={2}>
                        <h3 className="font-semibold text-lg">{exp.position}</h3>
                      </TextOverflow>
                      <TextOverflow maxLines={1}>
                        <p className="text-muted-foreground font-medium">{exp.company}</p>
                      </TextOverflow>
                    </div>
                    <div className="text-sm text-muted-foreground text-right">
                      {formatDateRange(exp.startDate, exp.endDate)}
                    </div>
                  </div>
                  {exp.description && (
                    <SafeTextDisplay
                      text={exp.description}
                      maxLength={500}
                      maxLines={4}
                      className="text-muted-foreground mb-2 leading-relaxed"
                    />
                  )}
                  {exp.achievements && exp.achievements.length > 0 && (
                    <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                      {exp.achievements.map((achievement, index) => (
                        <li key={index}>{achievement}</li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education */}
        {resume.education.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Education</h2>
            <div className="space-y-4">
              {resume.education.map((edu) => (
                <div key={edu.id}>
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="font-semibold">{edu.degree}</h3>
                      {edu.field && <p className="text-muted-foreground">in {edu.field}</p>}
                      <p className="text-muted-foreground font-medium">{edu.institution}</p>
                    </div>
                    <div className="text-sm text-muted-foreground text-right">
                      {formatDateRange(edu.startDate, edu.endDate)}
                    </div>
                  </div>
                  <div className="flex gap-4 text-sm text-muted-foreground">
                    {edu.gpa && <span>GPA: {edu.gpa}</span>}
                    {edu.honors && <span>{edu.honors}</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills */}
        {resume.skills.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Skills</h2>
            <div className="space-y-4">
              {/* Group skills by category */}
              {Object.entries(
                resume.skills.reduce((acc, skill) => {
                  const category = skill.category || 'Other';
                  if (!acc[category]) acc[category] = [];
                  acc[category].push(skill);
                  return acc;
                }, {} as Record<string, typeof resume.skills>)
              ).map(([category, categorySkills]) => (
                <div key={category}>
                  <h3 className="font-medium text-sm text-muted-foreground uppercase tracking-wide mb-2">
                    {category}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {categorySkills.map((skill) => (
                      <Badge
                        key={skill.id}
                        variant="outline"
                        className={getLevelBadgeColor(skill.level)}
                      >
                        {skill.name}
                        {skill.level && (
                          <span className="ml-1 text-xs opacity-75">
                            ({skill.level.toLowerCase()})
                          </span>
                        )}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Template Info */}
        <Separator />
        <div className="text-center text-xs text-muted-foreground">
          <p>Template: {resume.template} • Created with Resume Builder</p>
        </div>
      </CardContent>
    </Card>
  );
}
