/**
 * Authentication Loading States
 * Enhanced loading indicators for authentication processes
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Loader2,
  Shield,
  User,
  Key,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  Lock,
  Unlock
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface AuthLoadingStateProps {
  state: 'checking' | 'signing-in' | 'signing-out' | 'redirecting' | 'verifying' | 'renewing';
  message?: string;
  progress?: number;
  showProgress?: boolean;
  showIcon?: boolean;
  compact?: boolean;
  className?: string;
}

export function AuthLoadingState({
  state,
  message,
  progress,
  showProgress = false,
  showIcon = true,
  compact = false,
  className
}: AuthLoadingStateProps) {
  const getStateConfig = () => {
    switch (state) {
      case 'checking':
        return {
          icon: Shield,
          title: 'Checking Authentication',
          defaultMessage: 'Verifying your session...',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800'
        };
      case 'signing-in':
        return {
          icon: User,
          title: 'Signing In',
          defaultMessage: 'Authenticating your credentials...',
          color: 'text-green-600',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          borderColor: 'border-green-200 dark:border-green-800'
        };
      case 'signing-out':
        return {
          icon: Lock,
          title: 'Signing Out',
          defaultMessage: 'Securely ending your session...',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20',
          borderColor: 'border-orange-200 dark:border-orange-800'
        };
      case 'redirecting':
        return {
          icon: Zap,
          title: 'Redirecting',
          defaultMessage: 'Taking you to your destination...',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50 dark:bg-purple-900/20',
          borderColor: 'border-purple-200 dark:border-purple-800'
        };
      case 'verifying':
        return {
          icon: Key,
          title: 'Verifying',
          defaultMessage: 'Confirming your identity...',
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
          borderColor: 'border-indigo-200 dark:border-indigo-800'
        };
      case 'renewing':
        return {
          icon: Unlock,
          title: 'Renewing Session',
          defaultMessage: 'Extending your session...',
          color: 'text-teal-600',
          bgColor: 'bg-teal-50 dark:bg-teal-900/20',
          borderColor: 'border-teal-200 dark:border-teal-800'
        };
      default:
        return {
          icon: Loader2,
          title: 'Loading',
          defaultMessage: 'Please wait...',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20',
          borderColor: 'border-gray-200 dark:border-gray-800'
        };
    }
  };

  const config = getStateConfig();
  const Icon = config.icon;
  const displayMessage = message || config.defaultMessage;

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2 p-2", className)}>
        {showIcon && (
          <Icon className={cn("h-4 w-4 animate-spin", config.color)} />
        )}
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {displayMessage}
        </span>
        {showProgress && progress !== undefined && (
          <Progress value={progress} className="w-16 h-2" />
        )}
      </div>
    );
  }

  return (
    <Card className={cn(config.bgColor, config.borderColor, className)}>
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          {showIcon && (
            <div className="flex-shrink-0">
              <Icon className={cn("h-8 w-8 animate-spin", config.color)} />
            </div>
          )}
          <div className="flex-1 space-y-2">
            <h3 className={cn("font-medium", config.color)}>
              {config.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {displayMessage}
            </p>
            {showProgress && progress !== undefined && (
              <div className="space-y-1">
                <Progress value={progress} className="h-2" />
                <p className="text-xs text-gray-500">
                  {Math.round(progress)}% complete
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Full page authentication loading screen
 */
export function AuthLoadingScreen({
  state,
  message,
  progress,
  showProgress = true,
  className
}: Omit<AuthLoadingStateProps, 'compact'>) {
  return (
    <div className={cn(
      "min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",
      className
    )}>
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            FAAFO Career Platform
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Professional Development Hub
          </p>
        </div>
        
        <AuthLoadingState
          state={state}
          message={message}
          progress={progress}
          showProgress={showProgress}
          showIcon={true}
          compact={false}
        />
      </div>
    </div>
  );
}

/**
 * Authentication skeleton loader for navigation
 */
export function AuthNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center space-x-4", className)}>
      <Skeleton className="h-8 w-8 rounded-full" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-3 w-16" />
      </div>
    </div>
  );
}

/**
 * Authentication status indicator
 */
export function AuthStatusIndicator({
  status,
  className
}: {
  status: 'authenticated' | 'unauthenticated' | 'loading' | 'error';
  className?: string;
}) {
  const getStatusConfig = () => {
    switch (status) {
      case 'authenticated':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100 dark:bg-green-900/20',
          label: 'Authenticated'
        };
      case 'unauthenticated':
        return {
          icon: Lock,
          color: 'text-gray-600',
          bgColor: 'bg-gray-100 dark:bg-gray-900/20',
          label: 'Not Signed In'
        };
      case 'loading':
        return {
          icon: Loader2,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100 dark:bg-blue-900/20',
          label: 'Checking...'
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100 dark:bg-red-900/20',
          label: 'Error'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Badge
      variant="secondary"
      className={cn(
        "flex items-center gap-1",
        config.bgColor,
        className
      )}
    >
      <Icon className={cn(
        "h-3 w-3",
        config.color,
        status === 'loading' && "animate-spin"
      )} />
      <span className={config.color}>{config.label}</span>
    </Badge>
  );
}

/**
 * Session timeout countdown
 */
export function SessionTimeoutCountdown({
  timeRemaining,
  isWarning = false,
  className
}: {
  timeRemaining: number;
  isWarning?: boolean;
  className?: string;
}) {
  const minutes = Math.floor(timeRemaining / (1000 * 60));
  const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);
  
  const formatTime = () => {
    if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <Badge
      variant={isWarning ? "destructive" : "secondary"}
      className={cn(
        "flex items-center gap-1 font-mono",
        isWarning && "animate-pulse",
        className
      )}
    >
      <Clock className="h-3 w-3" />
      {formatTime()}
    </Badge>
  );
}

/**
 * Multi-step authentication progress
 */
export function AuthProgressSteps({
  steps,
  currentStep,
  className
}: {
  steps: string[];
  currentStep: number;
  className?: string;
}) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
        <span>Step {currentStep + 1} of {steps.length}</span>
        <span>{Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
      </div>
      
      <Progress value={((currentStep + 1) / steps.length) * 100} className="h-2" />
      
      <div className="space-y-2">
        {steps.map((step, index) => (
          <div
            key={index}
            className={cn(
              "flex items-center gap-2 text-sm",
              index < currentStep && "text-green-600 dark:text-green-400",
              index === currentStep && "text-blue-600 dark:text-blue-400 font-medium",
              index > currentStep && "text-gray-400 dark:text-gray-600"
            )}
          >
            {index < currentStep ? (
              <CheckCircle className="h-4 w-4" />
            ) : index === currentStep ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Clock className="h-4 w-4" />
            )}
            {step}
          </div>
        ))}
      </div>
    </div>
  );
}
