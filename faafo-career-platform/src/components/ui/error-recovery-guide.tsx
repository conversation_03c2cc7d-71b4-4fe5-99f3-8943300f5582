/**
 * Error Recovery Guide Component
 * Displays step-by-step recovery instructions for errors
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  ExternalLink,
  Phone,
  Lightbulb,
  ArrowRight,
  Shield,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ErrorRecoveryGuidance, ErrorRecoveryGuide, RecoveryStep } from '@/lib/error-recovery-guidance';

export interface ErrorRecoveryGuideProps {
  error: any;
  context?: string;
  onRetry?: () => void;
  onRefresh?: () => void;
  onNavigate?: (url: string) => void;
  onContactSupport?: () => void;
  className?: string;
  compact?: boolean;
  showPreventionTips?: boolean;
}

export function ErrorRecoveryGuideComponent({
  error,
  context,
  onRetry,
  onRefresh,
  onNavigate,
  onContactSupport,
  className,
  compact = false,
  showPreventionTips = true
}: ErrorRecoveryGuideProps) {
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [showPreventionTipsCollapsed, setShowPreventionTipsCollapsed] = useState(false);

  // Get error code from error object
  const errorCode = getErrorCode(error);
  const guide = ErrorRecoveryGuidance.getGuidance(errorCode, context);

  if (!guide) {
    return (
      <Alert className={cn("border-orange-200 bg-orange-50 dark:bg-orange-900/20", className)}>
        <AlertCircle className="h-4 w-4 text-orange-600" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">Something went wrong</p>
            <p className="text-sm">
              We don't have specific recovery instructions for this error, but here are some general steps:
            </p>
            <div className="flex gap-2 mt-3">
              {onRefresh && (
                <Button variant="outline" size="sm" onClick={onRefresh}>
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Refresh
                </Button>
              )}
              {onRetry && (
                <Button variant="outline" size="sm" onClick={onRetry}>
                  Try Again
                </Button>
              )}
              {onContactSupport && (
                <Button variant="outline" size="sm" onClick={onContactSupport}>
                  <Phone className="h-3 w-3 mr-1" />
                  Contact Support
                </Button>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  const handleStepComplete = (stepNumber: number) => {
    setCompletedSteps(prev => new Set(Array.from(prev).concat(stepNumber)));
  };

  const handleStepAction = (step: RecoveryStep) => {
    switch (step.action) {
      case 'refresh':
        onRefresh?.();
        break;
      case 'retry':
        onRetry?.();
        break;
      case 'navigate':
        if (step.actionUrl) {
          onNavigate?.(step.actionUrl);
        }
        break;
      case 'contact':
        onContactSupport?.();
        break;
    }
    handleStepComplete(step.step);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'low':
        return <AlertCircle className="h-4 w-4" />;
      case 'medium':
        return <AlertCircle className="h-4 w-4" />;
      case 'high':
        return <Shield className="h-4 w-4" />;
      case 'critical':
        return <Zap className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  if (compact) {
    return (
      <Alert className={cn("border-orange-200 bg-orange-50 dark:bg-orange-900/20", className)}>
        <AlertCircle className="h-4 w-4 text-orange-600" />
        <AlertDescription>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="font-medium">{guide.title}</p>
              <Badge className={getSeverityColor(guide.severity)}>
                {guide.severity}
              </Badge>
            </div>
            <p className="text-sm">{guide.description}</p>
            <div className="flex gap-2 mt-3">
              {guide.steps.slice(0, 2).map((step) => (
                step.action && (
                  <Button
                    key={step.step}
                    variant="outline"
                    size="sm"
                    onClick={() => handleStepAction(step)}
                  >
                    {step.actionLabel || step.instruction}
                  </Button>
                )
              ))}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={cn("border-orange-200 bg-orange-50 dark:bg-orange-900/20", className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
              {getSeverityIcon(guide.severity)}
              {guide.title}
            </CardTitle>
            <CardDescription className="text-orange-700 dark:text-orange-300">
              {guide.description}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getSeverityColor(guide.severity)}>
              {guide.severity}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {guide.estimatedTime}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Recovery Steps */}
        <div className="space-y-3">
          <h4 className="font-medium text-orange-800 dark:text-orange-200 flex items-center gap-2">
            <ArrowRight className="h-4 w-4" />
            Recovery Steps
          </h4>
          
          <div className="space-y-2">
            {guide.steps.map((step) => (
              <div
                key={step.step}
                className={cn(
                  "flex items-start gap-3 p-3 rounded-lg border",
                  completedSteps.has(step.step)
                    ? "bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800"
                    : "bg-white border-orange-200 dark:bg-gray-800 dark:border-orange-700"
                )}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {completedSteps.has(step.step) ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <div className="h-5 w-5 rounded-full border-2 border-orange-300 flex items-center justify-center text-xs font-medium text-orange-600">
                      {step.step}
                    </div>
                  )}
                </div>
                
                <div className="flex-1 space-y-2">
                  <p className={cn(
                    "text-sm",
                    completedSteps.has(step.step)
                      ? "text-green-800 dark:text-green-200"
                      : "text-orange-800 dark:text-orange-200"
                  )}>
                    {step.instruction}
                  </p>
                  
                  {step.action && step.actionLabel && !completedSteps.has(step.step) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStepAction(step)}
                      className="h-7 px-3 text-xs"
                    >
                      {step.action === 'navigate' && <ExternalLink className="h-3 w-3 mr-1" />}
                      {step.action === 'refresh' && <RefreshCw className="h-3 w-3 mr-1" />}
                      {step.action === 'contact' && <Phone className="h-3 w-3 mr-1" />}
                      {step.actionLabel}
                    </Button>
                  )}
                </div>
                
                {!completedSteps.has(step.step) && !step.action && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleStepComplete(step.step)}
                    className="h-7 px-2 text-xs"
                  >
                    Done
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Prevention Tips */}
        {showPreventionTips && guide.preventionTips && guide.preventionTips.length > 0 && (
          <div className="space-y-2">
            <Button
              variant="ghost"
              className="w-full justify-between p-0 h-auto"
              onClick={() => setShowPreventionTipsCollapsed(!showPreventionTipsCollapsed)}
            >
              <span className="font-medium text-orange-800 dark:text-orange-200 flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Prevention Tips
              </span>
              {showPreventionTipsCollapsed ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            {showPreventionTipsCollapsed && (
              <div className="space-y-2 mt-2">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                  <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-200">
                    {guide.preventionTips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-600 mt-1">•</span>
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Contact Support */}
        {guide.contactSupport && (
          <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
            <Phone className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800 dark:text-blue-200">
              <div className="flex items-center justify-between">
                <span>Need additional help? Our support team is here to assist you.</span>
                {onContactSupport && (
                  <Button variant="outline" size="sm" onClick={onContactSupport}>
                    Contact Support
                  </Button>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Escalation Path */}
        {guide.escalationPath && (
          <Alert className="border-red-200 bg-red-50 dark:bg-red-900/20">
            <Shield className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800 dark:text-red-200 text-sm">
              <strong>Escalation:</strong> {guide.escalationPath}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Extract error code from various error formats
 */
function getErrorCode(error: any): string {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object') {
    return error.code || error.status?.toString() || error.message || 'UNKNOWN_ERROR';
  }
  
  return 'UNKNOWN_ERROR';
}

/**
 * Quick error recovery component for inline use
 */
export function QuickErrorRecovery({
  error,
  context,
  onRetry,
  onRefresh,
  className
}: Pick<ErrorRecoveryGuideProps, 'error' | 'context' | 'onRetry' | 'onRefresh' | 'className'>) {
  return (
    <ErrorRecoveryGuideComponent
      error={error}
      context={context}
      onRetry={onRetry}
      onRefresh={onRefresh}
      className={className}
      compact={true}
      showPreventionTips={false}
    />
  );
}
