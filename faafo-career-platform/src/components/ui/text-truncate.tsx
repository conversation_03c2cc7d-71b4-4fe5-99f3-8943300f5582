'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface TextTruncateProps {
  text: string;
  maxLength?: number;
  className?: string;
  showToggle?: boolean;
  expandText?: string;
  collapseText?: string;
}

export function TextTruncate({
  text,
  maxLength = 100,
  className,
  showToggle = true,
  expandText = 'Show more',
  collapseText = 'Show less'
}: TextTruncateProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text || text.length <= maxLength) {
    return <span className={className}>{text}</span>;
  }

  const truncatedText = text.slice(0, maxLength);
  const displayText = isExpanded ? text : `${truncatedText}...`;

  return (
    <span className={className}>
      {displayText}
      {showToggle && (
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="ml-1 text-blue-600 hover:text-blue-800 underline text-sm"
          type="button"
        >
          {isExpanded ? collapseText : expandText}
        </button>
      )}
    </span>
  );
}

interface TextOverflowProps {
  children: React.ReactNode;
  className?: string;
  maxLines?: number;
}

export function TextOverflow({ children, className, maxLines = 1 }: TextOverflowProps) {
  const lineClampClass = maxLines === 1 
    ? 'line-clamp-1' 
    : maxLines === 2 
    ? 'line-clamp-2' 
    : maxLines === 3 
    ? 'line-clamp-3' 
    : `line-clamp-${maxLines}`;

  return (
    <div className={cn('overflow-hidden text-ellipsis', lineClampClass, className)}>
      {children}
    </div>
  );
}

interface SafeTextDisplayProps {
  text: string;
  maxLength?: number;
  maxLines?: number;
  className?: string;
  allowExpansion?: boolean;
}

export function SafeTextDisplay({
  text,
  maxLength = 200,
  maxLines = 3,
  className,
  allowExpansion = true
}: SafeTextDisplayProps) {
  // Sanitize and limit text length
  const sanitizedText = text?.replace(/<[^>]*>/g, '') || '';
  const limitedText = sanitizedText.length > maxLength 
    ? sanitizedText.slice(0, maxLength) 
    : sanitizedText;

  if (allowExpansion && sanitizedText.length > maxLength) {
    return (
      <TextTruncate
        text={sanitizedText}
        maxLength={maxLength}
        className={className}
      />
    );
  }

  return (
    <TextOverflow className={className} maxLines={maxLines}>
      {limitedText}
    </TextOverflow>
  );
}
