import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}>
      <span className="sr-only">Loading...</span>
    </div>
  );
}

interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingState({ message = 'Loading...', size = 'md', className = '' }: LoadingStateProps) {
  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <LoadingSpinner size={size} />
      <span className="text-gray-600 dark:text-gray-400">{message}</span>
    </div>
  );
}

interface ProgressiveLoadingProps {
  message?: string;
  subMessage?: string;
  progress?: number;
  className?: string;
}

export function ProgressiveLoading({
  message = 'Loading...',
  subMessage,
  progress,
  className = ''
}: ProgressiveLoadingProps) {
  return (
    <div className={`flex flex-col items-center justify-center space-y-4 p-8 ${className}`}>
      <LoadingSpinner size="lg" />
      <div className="text-center">
        <p className="text-lg font-medium text-gray-900 dark:text-gray-100">{message}</p>
        {subMessage && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{subMessage}</p>
        )}
        {progress !== undefined && (
          <div className="w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
        )}
      </div>
    </div>
  );
}
