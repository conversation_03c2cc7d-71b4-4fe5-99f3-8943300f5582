'use client';

import React, { useEffect, useState } from 'react';

interface PerformanceMonitorProps {
  name: string;
  onLoadTime?: (time: number) => void;
  children: React.ReactNode;
}

export function PerformanceMonitor({ name, onLoadTime, children }: PerformanceMonitorProps) {
  const [startTime] = useState(Date.now());
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!isLoaded) {
      const loadTime = Date.now() - startTime;
      console.log(`[Performance] ${name} loaded in ${loadTime}ms`);
      
      if (onLoadTime) {
        onLoadTime(loadTime);
      }
      
      setIsLoaded(true);
    }
  }, [name, startTime, isLoaded, onLoadTime]);

  return <>{children}</>;
}

interface LoadingTimeTrackerProps {
  onComplete: (time: number) => void;
  timeout?: number;
}

export function useLoadingTimeTracker({ onComplete, timeout = 30000 }: LoadingTimeTrackerProps) {
  const [startTime] = useState(Date.now());
  const [isCompleted, setIsCompleted] = useState(false);

  const complete = () => {
    if (!isCompleted) {
      const loadTime = Date.now() - startTime;
      onComplete(loadTime);
      setIsCompleted(true);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!isCompleted) {
        console.warn(`[Performance] Loading timeout after ${timeout}ms`);
        complete();
      }
    }, timeout);

    return () => clearTimeout(timeoutId);
  }, [timeout, isCompleted]);

  return { complete, isCompleted };
}

export function withPerformanceMonitoring<T extends object>(
  Component: React.ComponentType<T>,
  name: string
) {
  return function PerformanceMonitoredComponent(props: T) {
    return (
      <PerformanceMonitor name={name}>
        <Component {...props} />
      </PerformanceMonitor>
    );
  };
}
