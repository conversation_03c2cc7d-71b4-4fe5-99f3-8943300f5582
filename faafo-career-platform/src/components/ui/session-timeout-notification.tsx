/**
 * Session Timeout Notification Component
 * Displays warnings and handles session renewal
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Clock,
  AlertTriangle,
  RefreshCw,
  Shield,
  LogOut,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  SessionTimeoutManager,
  SessionTimeoutState,
  SessionTimeoutConfig,
  getSessionTimeoutManager
} from '@/lib/session-timeout-manager';
import { useSessionRenewal } from '@/lib/session-renewal';

export interface SessionTimeoutNotificationProps {
  config?: Partial<SessionTimeoutConfig>;
  onSessionExpired?: () => void;
  onSessionRenewed?: () => void;
  showInlineWarning?: boolean;
  showModalWarning?: boolean;
  autoRenewEnabled?: boolean;
  className?: string;
}

export function SessionTimeoutNotification({
  config,
  onSessionExpired,
  onSessionRenewed,
  showInlineWarning = true,
  showModalWarning = true,
  autoRenewEnabled = true,
  className
}: SessionTimeoutNotificationProps) {
  const [sessionState, setSessionState] = useState<SessionTimeoutState | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [isRenewing, setIsRenewing] = useState(false);
  const [sessionManager, setSessionManager] = useState<SessionTimeoutManager | null>(null);

  // Use session renewal hook
  const { renewSession: performRenewal } = useSessionRenewal();

  // Initialize session manager
  useEffect(() => {
    const manager = getSessionTimeoutManager(config, {
      onWarning: (timeRemaining) => {
        if (showModalWarning && timeRemaining <= 2 * 60 * 1000) { // Show modal 2 minutes before expiry
          setShowModal(true);
        }
      },
      onExpiry: () => {
        setShowModal(false);
        onSessionExpired?.();
      },
      onRenewal: () => {
        setShowModal(false);
        setIsRenewing(false);
        onSessionRenewed?.();
      },
      onStateChange: (state) => {
        setSessionState(state);
      }
    });

    setSessionManager(manager);

    // Subscribe to state changes
    const unsubscribe = manager.subscribe(setSessionState);

    return () => {
      unsubscribe();
    };
  }, [config, onSessionExpired, onSessionRenewed, showModalWarning]);

  const handleRenewSession = useCallback(async () => {
    if (!sessionManager) return;

    setIsRenewing(true);

    try {
      // Use the actual session renewal API
      const result = await performRenewal();

      if (result.success) {
        // Renew the local session manager
        sessionManager.renewSession();
      } else {
        throw new Error(result.error || 'Session renewal failed');
      }
    } catch (error) {
      console.error('Failed to renew session:', error);
      setIsRenewing(false);
    }
  }, [sessionManager, performRenewal]);

  const handleLogout = useCallback(() => {
    setShowModal(false);
    onSessionExpired?.();
  }, [onSessionExpired]);

  const handleExtendSession = useCallback((minutes: number) => {
    if (!sessionManager) return;
    
    const additionalTime = minutes * 60 * 1000;
    sessionManager.extendSession(additionalTime);
  }, [sessionManager]);

  if (!sessionState || !sessionState.isActive) {
    return null;
  }

  const timeRemainingFormatted = SessionTimeoutManager.formatTimeRemaining(sessionState.timeRemaining);
  const progressPercentage = sessionManager?.getSessionProgress() || 0;
  const isUrgent = sessionState.timeRemaining <= 2 * 60 * 1000; // Less than 2 minutes
  const isCritical = sessionState.timeRemaining <= 60 * 1000; // Less than 1 minute

  return (
    <>
      {/* Inline Warning */}
      {showInlineWarning && sessionState.showWarning && (
        <Alert 
          className={cn(
            "border-orange-200 bg-orange-50 dark:bg-orange-900/20",
            isUrgent && "border-red-200 bg-red-50 dark:bg-red-900/20",
            className
          )}
        >
          <Clock className={cn(
            "h-4 w-4",
            isUrgent ? "text-red-600" : "text-orange-600"
          )} />
          <AlertDescription className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className={cn(
                  "font-medium",
                  isUrgent ? "text-red-800 dark:text-red-200" : "text-orange-800 dark:text-orange-200"
                )}>
                  Session expires in {timeRemainingFormatted}
                </span>
                {isCritical && (
                  <Badge variant="destructive" className="animate-pulse">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Critical
                  </Badge>
                )}
              </div>
              <Progress 
                value={progressPercentage} 
                className={cn(
                  "h-2 w-32",
                  isUrgent && "bg-red-100 dark:bg-red-900"
                )}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRenewSession}
                disabled={isRenewing}
                className="h-7"
              >
                {isRenewing ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Renewing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Extend
                  </>
                )}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Modal Warning */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className={cn(
                "h-5 w-5",
                isCritical ? "text-red-600" : "text-orange-600"
              )} />
              Session Expiring Soon
            </DialogTitle>
            <DialogDescription>
              Your session will expire in{' '}
              <span className={cn(
                "font-mono font-bold",
                isCritical ? "text-red-600" : "text-orange-600"
              )}>
                {timeRemainingFormatted}
              </span>
              {' '}due to inactivity.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Session Progress</span>
                <span>{Math.round(progressPercentage)}%</span>
              </div>
              <Progress 
                value={progressPercentage} 
                className={cn(
                  "h-3",
                  isCritical && "bg-red-100 dark:bg-red-900"
                )}
              />
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExtendSession(15)}
                className="text-xs"
              >
                <Zap className="h-3 w-3 mr-1" />
                +15 min
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExtendSession(30)}
                className="text-xs"
              >
                <Zap className="h-3 w-3 mr-1" />
                +30 min
              </Button>
            </div>

            {/* Auto-renewal info */}
            {autoRenewEnabled && (
              <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/20">
                <AlertTriangle className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800 dark:text-blue-200 text-sm">
                  Your session will auto-renew if you're actively using the application.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={handleLogout}
              className="w-full sm:w-auto"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out Now
            </Button>
            <Button
              onClick={handleRenewSession}
              disabled={isRenewing}
              className="w-full sm:w-auto"
            >
              {isRenewing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Renewing Session...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Extend Session
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

/**
 * Hook for using session timeout functionality
 */
export function useSessionTimeout(config?: Partial<SessionTimeoutConfig>) {
  const [sessionState, setSessionState] = useState<SessionTimeoutState | null>(null);
  const [sessionManager, setSessionManager] = useState<SessionTimeoutManager | null>(null);

  useEffect(() => {
    const manager = getSessionTimeoutManager(config);
    setSessionManager(manager);

    const unsubscribe = manager.subscribe(setSessionState);
    setSessionState(manager.getState());

    return () => {
      unsubscribe();
    };
  }, [config]);

  const renewSession = useCallback(() => {
    return sessionManager?.renewSession() || false;
  }, [sessionManager]);

  const extendSession = useCallback((additionalMinutes: number) => {
    if (!sessionManager) return false;
    const additionalTime = additionalMinutes * 60 * 1000;
    return sessionManager.extendSession(additionalTime);
  }, [sessionManager]);

  const recordActivity = useCallback(() => {
    sessionManager?.recordActivity();
  }, [sessionManager]);

  return {
    sessionState,
    renewSession,
    extendSession,
    recordActivity,
    timeRemaining: sessionState?.timeRemaining || 0,
    isExpired: sessionState?.isExpired || false,
    showWarning: sessionState?.showWarning || false,
    canRenew: sessionState?.canRenew || false
  };
}
