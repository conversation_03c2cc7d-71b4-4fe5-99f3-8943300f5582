'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Target, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Award, 
  BarChart3, 
  Calendar, 
  Star,
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Brain,
  Users,
  Zap,
  Trophy,
  Flame
} from 'lucide-react';

interface InterviewProgressData {
  overallProgress: {
    totalSessions: number;
    completedSessions: number;
    totalQuestions: number;
    completedQuestions: number;
    averageScore?: number;
    bestScore?: number;
    totalPracticeTime: number;
    improvementRate?: number;
    currentStreak: number;
    longestStreak: number;
    completionRate: number;
  };
  skillAreaProgress: Array<{
    id: string;
    skillArea: string;
    competencyLevel: string;
    totalSessions: number;
    completedSessions: number;
    averageScore?: number;
    bestScore?: number;
    lastSessionScore?: number;
    totalPracticeTime: number;
    lastPracticed?: string;
    improvementRate?: number;
  }>;
  questionTypeStats: Array<{
    questionType: string;
    category: string;
    responseCount: number;
    averageScore: number;
    averageResponseTime: number;
  }>;
  recentSessions: Array<{
    id: string;
    sessionType: string;
    status: string;
    completedQuestions: number;
    totalQuestions: number;
    overallScore?: number;
    timeSpent: number;
    createdAt: string;
  }>;
  improvementTrend: number[];
}

interface InterviewProgressTrackerProps {
  className?: string;
}

export default function InterviewProgressTracker({ className }: InterviewProgressTrackerProps) {
  const { data: session } = useSession();
  const [progressData, setProgressData] = useState<InterviewProgressData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.id) {
      fetchProgressData();
    }
  }, [session]);

  const fetchProgressData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/interview-practice/progress');
      const data = await response.json();

      if (data.success) {
        setProgressData(data.data);
      } else {
        setError(data.error || 'Failed to fetch progress data');
      }
    } catch (error) {
      console.error('Error fetching interview progress:', error);
      setError('Failed to load interview practice progress');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600 dark:text-green-400';
    if (score >= 6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getCompetencyBadgeVariant = (level: string) => {
    switch (level) {
      case 'EXPERT':
        return 'default';
      case 'ADVANCED':
        return 'secondary';
      case 'INTERMEDIATE':
        return 'outline';
      default:
        return 'destructive';
    }
  };

  const getImprovementIcon = (rate?: number) => {
    if (!rate) return <Clock className="h-4 w-4 text-gray-400" />;
    if (rate > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (rate < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Clock className="h-4 w-4 text-gray-400" />;
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-64" />
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!progressData) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Interview Practice Data</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Start practicing interviews to see your progress here
        </p>
        <Button onClick={() => window.location.href = '/interview-practice'}>
          Start Practicing
        </Button>
      </div>
    );
  }

  const { overallProgress, skillAreaProgress, questionTypeStats, recentSessions, improvementTrend } = progressData;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Sessions</p>
                <p className="text-2xl font-bold">{overallProgress.totalSessions}</p>
                <p className="text-xs text-gray-500">
                  {overallProgress.completedSessions} completed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Average Score</p>
                <p className={`text-2xl font-bold ${getScoreColor(overallProgress.averageScore || 0)}`}>
                  {overallProgress.averageScore ? `${overallProgress.averageScore.toFixed(1)}/10` : 'N/A'}
                </p>
                <div className="flex items-center space-x-1">
                  {getImprovementIcon(overallProgress.improvementRate)}
                  <p className="text-xs text-gray-500">
                    {overallProgress.improvementRate 
                      ? `${overallProgress.improvementRate > 0 ? '+' : ''}${overallProgress.improvementRate.toFixed(1)}%`
                      : 'No trend'
                    }
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Practice Time</p>
                <p className="text-2xl font-bold">
                  {formatDuration(overallProgress.totalPracticeTime)}
                </p>
                <p className="text-xs text-gray-500">
                  {overallProgress.totalQuestions} questions answered
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Flame className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Streak</p>
                <p className="text-2xl font-bold">{overallProgress.currentStreak}</p>
                <p className="text-xs text-gray-500">
                  Best: {overallProgress.longestStreak} days
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Progress Tabs */}
      <Tabs defaultValue="skills" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="skills">Skill Areas</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="sessions">Recent Sessions</TabsTrigger>
        </TabsList>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Progress by Skill Area</CardTitle>
              <CardDescription>
                Your performance across different interview categories
              </CardDescription>
            </CardHeader>
            <CardContent>
              {skillAreaProgress.length > 0 ? (
                <div className="space-y-4">
                  {skillAreaProgress.map((skill) => (
                    <div key={skill.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">
                            {skill.skillArea.replace('_', ' ')}
                          </span>
                          <Badge variant={getCompetencyBadgeVariant(skill.competencyLevel)}>
                            {skill.competencyLevel}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          {skill.averageScore && (
                            <span className={`text-sm font-medium ${getScoreColor(skill.averageScore)}`}>
                              {skill.averageScore.toFixed(1)}/10
                            </span>
                          )}
                          {getImprovementIcon(skill.improvementRate)}
                        </div>
                      </div>
                      
                      {skill.averageScore && (
                        <Progress value={(skill.averageScore / 10) * 100} className="h-2" />
                      )}
                      
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{skill.completedSessions}/{skill.totalSessions} sessions</span>
                        <span>{formatDuration(skill.totalPracticeTime)} practiced</span>
                        {skill.lastPracticed && (
                          <span>Last: {new Date(skill.lastPracticed).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Complete some practice sessions to see skill area progress
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Question Type Performance</CardTitle>
              <CardDescription>
                How you perform on different types of interview questions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {questionTypeStats.length > 0 ? (
                <div className="space-y-4">
                  {questionTypeStats.map((stat, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{stat.questionType.replace('_', ' ')}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {stat.category.replace('_', ' ')} • {stat.responseCount} responses
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${getScoreColor(stat.averageScore)}`}>
                          {stat.averageScore.toFixed(1)}/10
                        </p>
                        <p className="text-xs text-gray-500">
                          Avg time: {Math.round(stat.averageResponseTime / 60)}m
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Complete some practice sessions to see performance analytics
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {improvementTrend.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Improvement Trend</CardTitle>
                <CardDescription>
                  Your score progression over recent sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 mb-4">
                  {improvementTrend.map((score, index) => (
                    <div key={index} className="flex-1">
                      <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded">
                        <div 
                          className="h-2 bg-blue-500 rounded"
                          style={{ width: `${(score / 10) * 100}%` }}
                        />
                      </div>
                      <p className="text-xs text-center mt-1">{score.toFixed(1)}</p>
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                  Last {improvementTrend.length} sessions
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Practice Sessions</CardTitle>
              <CardDescription>
                Your latest interview practice activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentSessions.length > 0 ? (
                <div className="space-y-3">
                  {recentSessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium">
                          {session.sessionType.replace('_', ' ')} Session
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {session.completedQuestions}/{session.totalQuestions} questions • {formatDuration(session.timeSpent)}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(session.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant={session.status === 'COMPLETED' ? 'default' : 'secondary'}>
                          {session.status.replace('_', ' ')}
                        </Badge>
                        {session.overallScore && (
                          <p className={`text-sm font-medium mt-1 ${getScoreColor(session.overallScore)}`}>
                            {session.overallScore.toFixed(1)}/10
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No recent sessions found
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
