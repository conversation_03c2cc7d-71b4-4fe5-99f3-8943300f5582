'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Pause, 
  Square, 
  SkipForward, 
  SkipBack, 
  Clock, 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Eye,
  EyeOff,
  Lightbulb,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Timer,
  Target
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { UnifiedValidationService } from '@/lib/unified-validation-service';
import { getUserFriendlyError } from '@/lib/user-friendly-errors';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ApiActionButton } from '@/components/ui/feedback-button';

interface InterviewQuestion {
  id: string;
  questionText: string;
  questionType: string;
  category: string;
  difficulty: string;
  expectedDuration: number;
  context?: string;
  hints?: any;
  followUpQuestions?: string[];
  questionOrder: number;
  responses?: InterviewResponse[];
}

interface InterviewResponse {
  id: string;
  responseText?: string;
  audioUrl?: string;
  responseTime: number;
  preparationTime: number;
  aiScore?: number;
  isCompleted: boolean;
  userNotes?: string;
}

interface InterviewSession {
  id: string;
  sessionType: string;
  status: string;
  totalQuestions: number;
  completedQuestions: number;
  questions: InterviewQuestion[];
  progress: {
    completed: number;
    total: number;
    percentage: number;
  };
}

interface InterviewPracticeInterfaceProps {
  session: InterviewSession;
  onResponseSubmit: (questionId: string, response: {
    responseText: string;
    responseTime: number;
    preparationTime: number;
    userNotes?: string;
  }) => Promise<void>;
  onSessionComplete: () => void;
  onSessionPause: () => void;
  onSessionExit: () => void;
}

export default function InterviewPracticeInterface({
  session,
  onResponseSubmit,
  onSessionComplete,
  onSessionPause,
  onSessionExit,
}: InterviewPracticeInterfaceProps) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [response, setResponse] = useState('');
  const [userNotes, setUserNotes] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isPreparing, setIsPreparing] = useState(true);
  const [preparationTime, setPreparationTime] = useState(0);
  const [responseTime, setResponseTime] = useState(0);
  const [showHints, setShowHints] = useState(false);
  const [showContext, setShowContext] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const preparationTimerRef = useRef<NodeJS.Timeout | null>(null);
  const responseTimerRef = useRef<NodeJS.Timeout | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const currentQuestion = session.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === session.questions.length - 1;
  const hasResponse = currentQuestion?.responses && currentQuestion.responses.length > 0;

  useEffect(() => {
    // Load existing response if available
    if (hasResponse) {
      const existingResponse = currentQuestion.responses![0];
      setResponse(existingResponse.responseText || '');
      setUserNotes(existingResponse.userNotes || '');
      setPreparationTime(existingResponse.preparationTime);
      setResponseTime(existingResponse.responseTime);
      setIsPreparing(false);
    } else {
      // Reset for new question
      setResponse('');
      setUserNotes('');
      setPreparationTime(0);
      setResponseTime(0);
      setIsPreparing(true);
      startPreparationTimer();
    }

    return () => {
      stopAllTimers();
    };
  }, [currentQuestionIndex, hasResponse]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      stopAllTimers();
      cleanupMediaRecorder();
    };
  }, []);

  const startPreparationTimer = () => {
    preparationTimerRef.current = setInterval(() => {
      setPreparationTime(prev => prev + 1);
    }, 1000);
  };

  const startResponseTimer = () => {
    responseTimerRef.current = setInterval(() => {
      setResponseTime(prev => prev + 1);
    }, 1000);
  };

  const stopAllTimers = () => {
    if (preparationTimerRef.current) {
      clearInterval(preparationTimerRef.current);
      preparationTimerRef.current = null;
    }
    if (responseTimerRef.current) {
      clearInterval(responseTimerRef.current);
      responseTimerRef.current = null;
    }
  };

  const cleanupMediaRecorder = () => {
    if (mediaRecorderRef.current) {
      try {
        if (mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop();
        }

        // Stop all tracks to release microphone
        if (mediaRecorderRef.current.stream) {
          mediaRecorderRef.current.stream.getTracks().forEach(track => {
            track.stop();
          });
        }
      } catch (error) {
        console.warn('Error cleaning up media recorder:', error);
      } finally {
        mediaRecorderRef.current = null;
        setIsRecording(false);
      }
    }
  };

  const handleStartResponse = () => {
    setIsPreparing(false);
    stopAllTimers();
    startResponseTimer();
  };

  const handlePauseResponse = () => {
    if (responseTimerRef.current) {
      clearInterval(responseTimerRef.current);
      responseTimerRef.current = null;
    } else {
      startResponseTimer();
    }
  };

  const startRecording = async () => {
    try {
      // Clean up any existing recorder first
      cleanupMediaRecorder();

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        setErrors({ recording: 'Recording error occurred. Please try again.' });
        cleanupMediaRecorder();
      };

      mediaRecorderRef.current.onstop = () => {
        try {
          if (audioChunksRef.current.length > 0) {
            const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
            // Here you would upload the audio blob to your storage service
            console.log('Audio recorded:', audioBlob);
          }
        } catch (error) {
          console.error('Error processing recorded audio:', error);
        }
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setErrors(prev => {
        const { recording, ...rest } = prev;
        return rest;
      });
    } catch (error) {
      console.error('Error starting recording:', error);
      setErrors({ recording: 'Failed to start recording. Please check microphone permissions.' });
      cleanupMediaRecorder();
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      try {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
      } catch (error) {
        console.error('Error stopping recording:', error);
        cleanupMediaRecorder();
      }
    }
  };

  const handleSubmitResponse = async () => {
    if (!response.trim()) {
      setErrors({ response: 'Please provide a response before submitting.' });
      throw new Error('Please provide a response before submitting.');
    }

    setErrors({});

    try {
      // Validate and sanitize input using unified validation service
      const validation = UnifiedValidationService.validateInterviewResponse({
        responseText: response,
        userNotes: userNotes || undefined,
        responseTime,
        preparationTime,
      });

      if (!validation.isValid) {
        const errorMessage = validation.errors.join(', ');
        setErrors({
          response: errorMessage,
          security: 'Your response contains potentially harmful content. Please revise and try again.'
        });
        throw new Error(errorMessage);
      }

      // Use sanitized data
      await onResponseSubmit(currentQuestion.id, {
        responseText: validation.sanitizedData?.responseText || response,
        responseTime: validation.sanitizedData?.responseTime || responseTime,
        preparationTime: validation.sanitizedData?.preparationTime || preparationTime,
        userNotes: validation.sanitizedData?.userNotes || userNotes,
      });

      // Move to next question or complete session
      if (isLastQuestion) {
        onSessionComplete();
      } else {
        setCurrentQuestionIndex(prev => prev + 1);
      }
    } catch (error) {
      console.error('Error submitting response:', error);
      const friendlyError = getUserFriendlyError(error, 'interview');
      setErrors({ submit: friendlyError.message });
      throw error; // Re-throw for SubmitButton to handle
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < session.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getQuestionTypeColor = (type: string) => {
    const colors = {
      BEHAVIORAL: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      TECHNICAL: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      SITUATIONAL: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      LEADERSHIP: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      COMMUNICATION: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  if (!currentQuestion) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Questions Available</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            This session doesn't have any questions yet. Please generate questions first.
          </p>
          <Button onClick={onSessionExit}>Return to Setup</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* Header with Progress */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Interview Practice Session</span>
              </CardTitle>
              <CardDescription>
                Question {currentQuestionIndex + 1} of {session.questions.length}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline" className="flex items-center space-x-1">
                <Timer className="h-3 w-3" />
                <span>
                  {isPreparing ? `Prep: ${formatTime(preparationTime)}` : `Response: ${formatTime(responseTime)}`}
                </span>
              </Badge>
              <Button variant="outline" size="sm" onClick={onSessionPause}>
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
              <Button variant="outline" size="sm" onClick={onSessionExit}>
                Exit
              </Button>
            </div>
          </div>
          <Progress value={session.progress.percentage} className="mt-4" />
        </CardHeader>
      </Card>

      {/* Question Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <Badge className={getQuestionTypeColor(currentQuestion.questionType)}>
                  {currentQuestion.questionType.replace('_', ' ')}
                </Badge>
                <Badge variant="outline">
                  {currentQuestion.category.replace('_', ' ')}
                </Badge>
                <Badge variant="secondary">
                  {currentQuestion.difficulty}
                </Badge>
              </div>
              <CardTitle className="text-xl leading-relaxed">
                {currentQuestion.questionText}
              </CardTitle>
              {currentQuestion.expectedDuration && (
                <CardDescription className="mt-2">
                  Expected response time: {Math.floor(currentQuestion.expectedDuration / 60)} minutes
                </CardDescription>
              )}
            </div>
            <div className="flex space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowContext(!showContext)}
                    >
                      {showContext ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {showContext ? 'Hide context' : 'Show context'}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHints(!showHints)}
                    >
                      <Lightbulb className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {showHints ? 'Hide hints' : 'Show hints'}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {showContext && currentQuestion.context && (
            <Alert className="mt-4">
              <MessageSquare className="h-4 w-4" />
              <AlertDescription>
                <strong>Context:</strong> {currentQuestion.context}
              </AlertDescription>
            </Alert>
          )}

          {showHints && currentQuestion.hints && (
            <Alert className="mt-4">
              <Lightbulb className="h-4 w-4" />
              <AlertDescription>
                <strong>Hints:</strong>
                {typeof currentQuestion.hints === 'object' ? (
                  <div className="mt-2 space-y-2">
                    {currentQuestion.hints.structure && (
                      <div><strong>Structure:</strong> {currentQuestion.hints.structure}</div>
                    )}
                    {currentQuestion.hints.keyPoints && (
                      <div>
                        <strong>Key Points:</strong>
                        <ul className="list-disc list-inside mt-1">
                          {currentQuestion.hints.keyPoints.map((point: string, index: number) => (
                            <li key={index}>{point}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  currentQuestion.hints
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardHeader>
      </Card>

      {/* Response Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Your Response</span>
            {hasResponse && (
              <Badge variant="secondary" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                Completed
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isPreparing && !hasResponse && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Take your time to think about your response. Click "Start Response" when you're ready to begin.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <Textarea
              value={response}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 5000) {
                  setResponse(value);
                }
              }}
              placeholder="Type your response here..."
              className="min-h-[200px]"
              disabled={isPreparing && !hasResponse}
              maxLength={5000}
            />

            <Textarea
              value={userNotes}
              onChange={(e) => {
                const value = e.target.value;
                if (value.length <= 1000) {
                  setUserNotes(value);
                }
              }}
              placeholder="Personal notes (optional)..."
              className="min-h-[100px]"
              maxLength={1000}
            />
          </div>

          {errors.response && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.response}</AlertDescription>
            </Alert>
          )}

          {errors.security && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.security}</AlertDescription>
            </Alert>
          )}

          {errors.submit && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {isPreparing && !hasResponse ? (
                <Button onClick={handleStartResponse}>
                  <Play className="h-4 w-4 mr-2" />
                  Start Response
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={handlePauseResponse}
                >
                  {responseTimerRef.current ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause Timer
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Resume Timer
                    </>
                  )}
                </Button>
              )}

              <Button
                variant="outline"
                onClick={isRecording ? stopRecording : startRecording}
                disabled={isPreparing && !hasResponse}
              >
                {isRecording ? (
                  <>
                    <Square className="h-4 w-4 mr-2" />
                    Stop Recording
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4 mr-2" />
                    Record Audio
                  </>
                )}
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
              >
                <SkipBack className="h-4 w-4 mr-2" />
                Previous
              </Button>

              {!hasResponse && (
                <FeedbackButton
                  onClick={handleSubmitResponse}
                  disabled={isPreparing || !response.trim()}
                  context="interview"
                  loadingText="Submitting..."
                  successText={isLastQuestion ? 'Session Complete!' : 'Submitted!'}
                  retryable={true}
                >
                  {isLastQuestion ? 'Complete Session' : 'Submit & Next'}
                </FeedbackButton>
              )}

              <Button
                variant="outline"
                onClick={handleNextQuestion}
                disabled={currentQuestionIndex === session.questions.length - 1}
              >
                Next
                <SkipForward className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
