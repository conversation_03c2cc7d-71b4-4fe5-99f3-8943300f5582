import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import InterviewConfigurationWizard from '../InterviewConfigurationWizard';

// Mock next-auth
jest.mock('next-auth/react');
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;

// Mock the configuration complete callback
const mockOnConfigurationComplete = jest.fn();
const mockOnCancel = jest.fn();

describe('InterviewConfigurationWizard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSession.mockReturnValue({
      data: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
        },
      },
      status: 'authenticated',
    });
  });

  it('renders the configuration wizard', () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Interview Practice Setup')).toBeInTheDocument();
    expect(screen.getByText('Choose Your Practice Type')).toBeInTheDocument();
  });

  it('displays session type options', () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Quick Practice')).toBeInTheDocument();
    expect(screen.getByText('Focused Session')).toBeInTheDocument();
    expect(screen.getByText('Mock Interview')).toBeInTheDocument();
    expect(screen.getByText('Behavioral Practice')).toBeInTheDocument();
    expect(screen.getByText('Technical Practice')).toBeInTheDocument();
  });

  it('allows selecting a session type', async () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    const quickPracticeCard = screen.getByText('Quick Practice').closest('[data-slot="card"]');
    expect(quickPracticeCard).toBeInTheDocument();

    if (quickPracticeCard) {
      fireEvent.click(quickPracticeCard);
    }

    // Verify the click was successful by checking if we can proceed to next step
    const nextButton = screen.getByText('Next');
    expect(nextButton).toBeInTheDocument();
    expect(nextButton).not.toBeDisabled();
  });

  it('shows validation error when trying to proceed without selecting session type', async () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('Please select a session type')).toBeInTheDocument();
    });
  });

  it('proceeds to next step when session type is selected', async () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    // Select a session type
    const quickPracticeOption = screen.getByText('Quick Practice').closest('div');
    if (quickPracticeOption) {
      fireEvent.click(quickPracticeOption);
    }

    // Click next
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('Experience & Difficulty')).toBeInTheDocument();
    });
  });

  it('allows going back to previous step', async () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    // Select session type and go to next step
    const quickPracticeOption = screen.getByText('Quick Practice').closest('div');
    if (quickPracticeOption) {
      fireEvent.click(quickPracticeOption);
    }

    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Experience & Difficulty')).toBeInTheDocument();
    });

    // Go back
    fireEvent.click(screen.getByText('Previous'));

    await waitFor(() => {
      expect(screen.getByText('Choose Your Practice Type')).toBeInTheDocument();
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('shows progress indicator', () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Step 1 of 4')).toBeInTheDocument();
  });

  it('validates experience level selection on step 2', async () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    // Go to step 2
    const quickPracticeOption = screen.getByText('Quick Practice').closest('div');
    if (quickPracticeOption) {
      fireEvent.click(quickPracticeOption);
    }
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Experience & Difficulty')).toBeInTheDocument();
    });

    // Try to proceed without selecting experience level
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Please select your experience level')).toBeInTheDocument();
    });
  });

  it('allows setting number of questions', async () => {
    render(
      <InterviewConfigurationWizard
        onConfigurationComplete={mockOnConfigurationComplete}
        onCancel={mockOnCancel}
      />
    );

    // Go to step 2
    const quickPracticeOption = screen.getByText('Quick Practice').closest('div');
    if (quickPracticeOption) {
      fireEvent.click(quickPracticeOption);
    }
    fireEvent.click(screen.getByText('Next'));

    await waitFor(() => {
      expect(screen.getByText('Experience & Difficulty')).toBeInTheDocument();
    });

    // Find and modify the number of questions input
    const questionsInput = screen.getByDisplayValue('10');
    fireEvent.change(questionsInput, { target: { value: '15' } });

    expect(questionsInput).toHaveValue(15);
  });
});
