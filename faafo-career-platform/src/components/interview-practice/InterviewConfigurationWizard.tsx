'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FeedbackButton } from '@/components/ui/feedback-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, Clock, Target, Users, Briefcase, Brain, MessageSquare, Code, ChevronRight, ChevronLeft } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SafeText, encodeHtml } from '@/lib/html-encoding';

interface InterviewConfigurationWizardProps {
  onConfigurationComplete: (config: InterviewSessionConfig) => void;
  onCancel: () => void;
  initialConfig?: Partial<InterviewSessionConfig>;
}

export interface InterviewSessionConfig {
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  industryFocus?: string;
  specificRole?: string;
  interviewType?: string;
  preparationTime?: string;
  focusAreas?: string[];
  difficulty: string;
  totalQuestions: number;
}

const SESSION_TYPES = [
  {
    id: 'QUICK_PRACTICE',
    name: 'Quick Practice',
    description: '5-10 common questions (15-20 mins)',
    icon: Clock,
    duration: '15-20 min',
    questions: '5-10',
  },
  {
    id: 'FOCUSED_SESSION',
    name: 'Focused Session',
    description: 'Specific skill/role focus (30-45 mins)',
    icon: Target,
    duration: '30-45 min',
    questions: '10-15',
  },
  {
    id: 'MOCK_INTERVIEW',
    name: 'Mock Interview',
    description: 'Full interview simulation (60-90 mins)',
    icon: Users,
    duration: '60-90 min',
    questions: '15-25',
  },
  {
    id: 'BEHAVIORAL_PRACTICE',
    name: 'Behavioral Practice',
    description: 'STAR method scenarios',
    icon: MessageSquare,
    duration: '30-45 min',
    questions: '8-12',
  },
  {
    id: 'TECHNICAL_PRACTICE',
    name: 'Technical Practice',
    description: 'Role-specific technical questions',
    icon: Code,
    duration: '45-60 min',
    questions: '10-15',
  },
];

const EXPERIENCE_LEVELS = [
  { value: 'BEGINNER', label: 'Entry Level (0-2 years)' },
  { value: 'INTERMEDIATE', label: 'Mid Level (2-5 years)' },
  { value: 'ADVANCED', label: 'Senior Level (5-10 years)' },
  { value: 'EXPERT', label: 'Executive Level (10+ years)' },
];

const DIFFICULTY_LEVELS = [
  { value: 'EASY', label: 'Easy - Basic questions' },
  { value: 'INTERMEDIATE', label: 'Intermediate - Standard questions' },
  { value: 'HARD', label: 'Hard - Challenging questions' },
  { value: 'EXPERT', label: 'Expert - Advanced questions' },
];

const COMPANY_TYPES = [
  { value: 'startup', label: 'Startup' },
  { value: 'corporate', label: 'Corporate/Enterprise' },
  { value: 'nonprofit', label: 'Non-profit' },
  { value: 'government', label: 'Government' },
  { value: 'consulting', label: 'Consulting' },
  { value: 'agency', label: 'Agency' },
  { value: 'freelance', label: 'Freelance/Contract' },
];

const INTERVIEW_TYPES = [
  { value: 'PHONE', label: 'Phone Interview' },
  { value: 'VIDEO', label: 'Video Interview' },
  { value: 'IN_PERSON', label: 'In-Person Interview' },
  { value: 'PANEL', label: 'Panel Interview' },
  { value: 'GROUP', label: 'Group Interview' },
  { value: 'TECHNICAL_SCREEN', label: 'Technical Screen' },
  { value: 'BEHAVIORAL', label: 'Behavioral Interview' },
  { value: 'CASE_STUDY', label: 'Case Study' },
];

const FOCUS_AREAS = [
  { id: 'technical', label: 'Technical Skills', description: 'Role-specific technical knowledge' },
  { id: 'behavioral', label: 'Behavioral Questions', description: 'STAR method and soft skills' },
  { id: 'leadership', label: 'Leadership', description: 'Management and team scenarios' },
  { id: 'problem_solving', label: 'Problem Solving', description: 'Analytical and critical thinking' },
  { id: 'communication', label: 'Communication', description: 'Presentation and interpersonal skills' },
  { id: 'cultural_fit', label: 'Cultural Fit', description: 'Values and company alignment' },
];

export default function InterviewConfigurationWizard({
  onConfigurationComplete,
  onCancel,
  initialConfig,
}: InterviewConfigurationWizardProps) {
  const { data: session } = useSession();
  const [currentStep, setCurrentStep] = useState(1);
  const [config, setConfig] = useState<InterviewSessionConfig>({
    sessionType: '',
    experienceLevel: 'INTERMEDIATE', // Pre-select common experience level
    difficulty: 'INTERMEDIATE', // Pre-select common difficulty
    totalQuestions: 10,
    focusAreas: ['technical', 'behavioral'], // Pre-select common focus areas
    ...initialConfig,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!config.sessionType) {
          newErrors.sessionType = 'Please select a session type';
        }
        break;
      case 2:
        if (!config.experienceLevel) {
          newErrors.experienceLevel = 'Please select your experience level';
        }
        if (!config.difficulty) {
          newErrors.difficulty = 'Please select difficulty level';
        }
        break;
      case 3:
        if (!config.interviewType) {
          newErrors.interviewType = 'Please select interview type';
        }
        break;
      case 4:
        if (config.focusAreas && config.focusAreas.length === 0) {
          newErrors.focusAreas = 'Please select at least one focus area';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < totalSteps) {
        setCurrentStep(currentStep + 1);
      } else {
        handleComplete();
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    if (!validateStep(currentStep)) {
      throw new Error('Please complete all required fields before continuing.');
    }

    // Validate final configuration
    if (!config.sessionType || !config.experienceLevel || !config.difficulty) {
      throw new Error('Please complete all required fields');
    }

    try {
      await onConfigurationComplete(config);
    } catch (error) {
      console.error('Configuration error:', error);
      setErrors({ general: 'Failed to create interview session. Please try again.' });
      throw error; // Re-throw for SubmitButton to handle
    }
  };

  const updateConfig = (updates: Partial<InterviewSessionConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
    // Clear related errors
    const newErrors = { ...errors };
    Object.keys(updates).forEach(key => {
      delete newErrors[key];
    });
    setErrors(newErrors);
  };

  const handleFocusAreaToggle = (areaId: string) => {
    const currentAreas = config.focusAreas || [];
    const newAreas = currentAreas.includes(areaId)
      ? currentAreas.filter(id => id !== areaId)
      : [...currentAreas, areaId];
    updateConfig({ focusAreas: newAreas });
  };

  const getSelectedSessionType = () => {
    return SESSION_TYPES.find(type => type.id === config.sessionType);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Choose Your Practice Type</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {SESSION_TYPES.map((type) => {
                  const Icon = type.icon;
                  return (
                    <Card
                      key={type.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        config.sessionType === type.id
                          ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                      onClick={() => updateConfig({ sessionType: type.id })}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-3">
                          <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400 mt-1" />
                          <div className="flex-1">
                            <h4 className="font-medium">{type.name}</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {type.description}
                            </p>
                            <div className="flex space-x-4 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {type.duration}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {type.questions} questions
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
              {errors.sessionType && (
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.sessionType}</AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Experience & Difficulty</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="experienceLevel">Experience Level</Label>
                  <Select
                    value={config.experienceLevel}
                    onValueChange={(value) => updateConfig({ experienceLevel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select your experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPERIENCE_LEVELS.map((level) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.experienceLevel && (
                    <p className="text-sm text-red-600">{errors.experienceLevel}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="difficulty">Question Difficulty</Label>
                  <Select
                    value={config.difficulty}
                    onValueChange={(value) => updateConfig({ difficulty: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      {DIFFICULTY_LEVELS.map((level) => (
                        <SelectItem key={level.value} value={level.value}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.difficulty && (
                    <p className="text-sm text-red-600">{errors.difficulty}</p>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <Label htmlFor="totalQuestions">Number of Questions</Label>
                <div className="flex items-center space-x-4">
                  <Input
                    type="number"
                    min="1"
                    max="20"
                    value={config.totalQuestions}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 10;
                      const clampedValue = Math.max(1, Math.min(20, value));
                      updateConfig({ totalQuestions: clampedValue });
                      // Show warning for extreme values
                      if (value > 20) {
                        setErrors(prev => ({ ...prev, totalQuestions: 'Maximum 20 questions allowed for optimal experience' }));
                      } else if (value < 1) {
                        setErrors(prev => ({ ...prev, totalQuestions: 'At least 1 question is required' }));
                      } else {
                        setErrors(prev => {
                          const { totalQuestions, ...rest } = prev;
                          return rest;
                        });
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Recommended: {getSelectedSessionType()?.questions || '5-15'} questions (Max: 20)
                  </span>
                </div>
                {errors.totalQuestions && (
                  <p className="text-sm text-red-600 mt-1">{errors.totalQuestions}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Interview Context</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="careerPath">Career Path (Optional)</Label>
                  <Input
                    value={config.careerPath || ''}
                    onChange={(e) => updateConfig({ careerPath: e.target.value })}
                    placeholder="e.g., Software Engineer, Marketing Manager"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="specificRole">Specific Role (Optional)</Label>
                  <Input
                    value={config.specificRole || ''}
                    onChange={(e) => updateConfig({ specificRole: e.target.value })}
                    placeholder="e.g., Frontend Developer, Product Manager"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="companyType">Company Type</Label>
                  <Select
                    value={config.companyType || ''}
                    onValueChange={(value) => updateConfig({ companyType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select company type" />
                    </SelectTrigger>
                    <SelectContent>
                      {COMPANY_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="industryFocus">Industry Focus (Optional)</Label>
                  <Input
                    value={config.industryFocus || ''}
                    onChange={(e) => updateConfig({ industryFocus: e.target.value })}
                    placeholder="e.g., Healthcare, Finance, Technology"
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="interviewType">Interview Type</Label>
                  <Select
                    value={config.interviewType || ''}
                    onValueChange={(value) => updateConfig({ interviewType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select interview type" />
                    </SelectTrigger>
                    <SelectContent>
                      {INTERVIEW_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.interviewType && (
                    <p className="text-sm text-red-600">{errors.interviewType}</p>
                  )}
                </div>

                <div className="space-y-3">
                  <Label htmlFor="preparationTime">Preparation Time (Optional)</Label>
                  <Select
                    value={config.preparationTime || ''}
                    onValueChange={(value) => updateConfig({ preparationTime: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="How much time do you have?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1_day">1 Day</SelectItem>
                      <SelectItem value="3_days">3 Days</SelectItem>
                      <SelectItem value="1_week">1 Week</SelectItem>
                      <SelectItem value="2_weeks">2 Weeks</SelectItem>
                      <SelectItem value="1_month">1 Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">Focus Areas</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Select the areas you want to focus on during your practice session
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {FOCUS_AREAS.map((area) => (
                  <Card
                    key={area.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      config.focusAreas?.includes(area.id)
                        ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                    onClick={() => handleFocusAreaToggle(area.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          checked={config.focusAreas?.includes(area.id) || false}
                          onChange={() => handleFocusAreaToggle(area.id)}
                        />
                        <div className="flex-1">
                          <h4 className="font-medium">{area.label}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {area.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {errors.focusAreas && (
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{errors.focusAreas}</AlertDescription>
                </Alert>
              )}

              {config.focusAreas && config.focusAreas.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium mb-2">Selected focus areas:</p>
                  <div className="flex flex-wrap gap-2">
                    {config.focusAreas.map((areaId) => {
                      const area = FOCUS_AREAS.find(a => a.id === areaId);
                      return area ? (
                        <Badge key={areaId} variant="secondary">
                          {area.label}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Session Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Session Type:</span>
                  <span>{getSelectedSessionType()?.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Experience Level:</span>
                  <span>{EXPERIENCE_LEVELS.find(l => l.value === config.experienceLevel)?.label}</span>
                </div>
                <div className="flex justify-between">
                  <span>Difficulty:</span>
                  <span>{DIFFICULTY_LEVELS.find(l => l.value === config.difficulty)?.label}</span>
                </div>
                <div className="flex justify-between">
                  <span>Questions:</span>
                  <span>{config.totalQuestions}</span>
                </div>
                <div className="flex justify-between">
                  <span>Estimated Duration:</span>
                  <span>{getSelectedSessionType()?.duration}</span>
                </div>
                {config.careerPath && (
                  <div className="flex justify-between">
                    <span>Career Path:</span>
                    <span>{encodeHtml(config.careerPath)}</span>
                  </div>
                )}
                {config.companyType && (
                  <div className="flex justify-between">
                    <span>Company Type:</span>
                    <span>{COMPANY_TYPES.find(t => t.value === config.companyType)?.label}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Interview Practice Setup</CardTitle>
            <CardDescription>
              Configure your practice session for the best learning experience
            </CardDescription>
          </div>
          <Badge variant="outline">
            Step {currentStep} of {totalSteps}
          </Badge>
        </div>
        <Progress value={progress} className="mt-4" />
      </CardHeader>

      <CardContent className="space-y-6">
        {errors.general && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{errors.general}</AlertDescription>
          </Alert>
        )}

        {renderStepContent()}

        <Separator />

        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={currentStep === 1 ? onCancel : handlePrevious}
            disabled={isLoading}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            {currentStep === 1 ? 'Cancel' : 'Previous'}
          </Button>

          {currentStep === totalSteps ? (
            <FeedbackButton
              onClick={handleComplete}
              context="interview"
              loadingText="Creating Session..."
              successText="Session Created!"
              errorText="Failed to Create"
              retryable={true}
            >
              Start Practice
            </FeedbackButton>
          ) : (
            <Button
              onClick={handleNext}
              disabled={isLoading}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
