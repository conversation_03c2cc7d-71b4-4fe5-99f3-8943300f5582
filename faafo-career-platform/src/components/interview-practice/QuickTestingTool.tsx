'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Zap, Play, RotateCcw, Settings } from 'lucide-react';

interface QuickTestConfig {
  sessionType: string;
  difficulty: string;
  questionCount: number;
  questionTypes: string[];
  categories: string[];
  experienceLevel: string;
}

const SESSION_TYPES = [
  'QUICK_PRACTICE',
  'FOCUSED_SESSION', 
  'MOCK_INTERVIEW',
  'BEHAVIORAL_PRACTICE',
  'TECHNICAL_PRACTICE'
];

const QUESTION_TYPES = [
  'BEHAVIORAL',
  'TECHNICAL', 
  'SITUATIONAL',
  'COMPANY_CULTURE',
  'LEADERSHIP',
  'PROBLEM_SOLVING',
  'COMMUNICATION',
  'STRESS_TEST',
  'CASE_STUDY',
  'ROLE_SPECIFIC'
];

const CATEGORIES = [
  'GENERAL',
  'TECHNICAL_SKILLS',
  'SOFT_SKILLS', 
  'LEADERSHIP',
  'PROBLEM_SOLVING',
  'COMMUNICATION',
  'TEAMWORK',
  'ADAPTABILITY',
  'CREATIVITY',
  'ANALYTICAL_THINKING'
];

const DIFFICULTY_LEVELS = ['EASY', 'INTERMEDIATE', 'HARD', 'EXPERT'];
const EXPERIENCE_LEVELS = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];

const PRESET_CONFIGS = [
  {
    name: 'Quick Behavioral',
    config: {
      sessionType: 'BEHAVIORAL_PRACTICE',
      difficulty: 'INTERMEDIATE',
      questionCount: 5,
      questionTypes: ['BEHAVIORAL'],
      categories: ['SOFT_SKILLS', 'COMMUNICATION'],
      experienceLevel: 'INTERMEDIATE'
    }
  },
  {
    name: 'Technical Deep Dive',
    config: {
      sessionType: 'TECHNICAL_PRACTICE', 
      difficulty: 'HARD',
      questionCount: 8,
      questionTypes: ['TECHNICAL', 'PROBLEM_SOLVING'],
      categories: ['TECHNICAL_SKILLS', 'ANALYTICAL_THINKING'],
      experienceLevel: 'ADVANCED'
    }
  },
  {
    name: 'Leadership Focus',
    config: {
      sessionType: 'FOCUSED_SESSION',
      difficulty: 'EXPERT',
      questionCount: 10,
      questionTypes: ['LEADERSHIP', 'SITUATIONAL'],
      categories: ['LEADERSHIP', 'TEAMWORK'],
      experienceLevel: 'EXPERT'
    }
  },
  {
    name: 'Entry Level Mix',
    config: {
      sessionType: 'QUICK_PRACTICE',
      difficulty: 'EASY',
      questionCount: 5,
      questionTypes: ['BEHAVIORAL', 'GENERAL'],
      categories: ['GENERAL', 'COMMUNICATION'],
      experienceLevel: 'BEGINNER'
    }
  }
];

interface QuickTestingToolProps {
  onCreateSession: (config: QuickTestConfig) => void;
  isCreating?: boolean;
}

export default function QuickTestingTool({ onCreateSession, isCreating = false }: QuickTestingToolProps) {
  const [config, setConfig] = useState<QuickTestConfig>({
    sessionType: 'QUICK_PRACTICE',
    difficulty: 'INTERMEDIATE', 
    questionCount: 5,
    questionTypes: ['BEHAVIORAL'],
    categories: ['GENERAL'],
    experienceLevel: 'INTERMEDIATE'
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handlePresetSelect = (preset: typeof PRESET_CONFIGS[0]) => {
    setConfig(preset.config);
  };

  const handleQuestionTypeToggle = (type: string) => {
    const current = config.questionTypes;
    const updated = current.includes(type) 
      ? current.filter(t => t !== type)
      : [...current, type];
    setConfig({ ...config, questionTypes: updated });
  };

  const handleCategoryToggle = (category: string) => {
    const current = config.categories;
    const updated = current.includes(category)
      ? current.filter(c => c !== category) 
      : [...current, category];
    setConfig({ ...config, categories: updated });
  };

  const handleCreateSession = () => {
    onCreateSession(config);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Zap className="h-5 w-5 text-yellow-500" />
          <span>Quick Testing Tool</span>
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Rapidly test different interview session configurations
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Preset Configurations */}
        <div>
          <Label className="text-sm font-medium mb-3 block">Quick Presets</Label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {PRESET_CONFIGS.map((preset) => (
              <Button
                key={preset.name}
                variant="outline"
                size="sm"
                onClick={() => handlePresetSelect(preset)}
                className="text-xs"
              >
                {preset.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Basic Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="sessionType">Session Type</Label>
            <Select value={config.sessionType} onValueChange={(value) => setConfig({ ...config, sessionType: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SESSION_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type.replace(/_/g, ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="difficulty">Difficulty</Label>
            <Select value={config.difficulty} onValueChange={(value) => setConfig({ ...config, difficulty: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {DIFFICULTY_LEVELS.map((level) => (
                  <SelectItem key={level} value={level}>
                    {level}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="questionCount">Question Count</Label>
            <Input
              type="number"
              min="1"
              max="20"
              value={config.questionCount}
              onChange={(e) => setConfig({ ...config, questionCount: parseInt(e.target.value) || 5 })}
            />
          </div>
        </div>

        {/* Advanced Configuration Toggle */}
        <div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center space-x-2"
          >
            <Settings className="h-4 w-4" />
            <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Options</span>
          </Button>
        </div>

        {/* Advanced Configuration */}
        {showAdvanced && (
          <div className="space-y-4 border-t pt-4">
            <div>
              <Label className="text-sm font-medium mb-3 block">Question Types</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {QUESTION_TYPES.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      checked={config.questionTypes.includes(type)}
                      onCheckedChange={() => handleQuestionTypeToggle(type)}
                    />
                    <Label className="text-xs">{type.replace(/_/g, ' ')}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium mb-3 block">Categories</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {CATEGORIES.map((category) => (
                  <div key={category} className="flex items-center space-x-2">
                    <Checkbox
                      checked={config.categories.includes(category)}
                      onCheckedChange={() => handleCategoryToggle(category)}
                    />
                    <Label className="text-xs">{category.replace(/_/g, ' ')}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="experienceLevel">Experience Level</Label>
              <Select value={config.experienceLevel} onValueChange={(value) => setConfig({ ...config, experienceLevel: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {EXPERIENCE_LEVELS.map((level) => (
                    <SelectItem key={level} value={level}>
                      {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Current Configuration Display */}
        <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
          <Label className="text-sm font-medium mb-2 block">Current Configuration</Label>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline">{config.sessionType.replace(/_/g, ' ')}</Badge>
            <Badge variant="outline">{config.difficulty}</Badge>
            <Badge variant="outline">{config.questionCount} questions</Badge>
            <Badge variant="outline">{config.experienceLevel}</Badge>
            {config.questionTypes.map((type) => (
              <Badge key={type} variant="secondary" className="text-xs">
                {type.replace(/_/g, ' ')}
              </Badge>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setConfig({
              sessionType: 'QUICK_PRACTICE',
              difficulty: 'INTERMEDIATE',
              questionCount: 5,
              questionTypes: ['BEHAVIORAL'],
              categories: ['GENERAL'],
              experienceLevel: 'INTERMEDIATE'
            })}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          
          <Button onClick={handleCreateSession} disabled={isCreating}>
            <Play className="h-4 w-4 mr-2" />
            {isCreating ? 'Creating...' : 'Create & Test Session'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
