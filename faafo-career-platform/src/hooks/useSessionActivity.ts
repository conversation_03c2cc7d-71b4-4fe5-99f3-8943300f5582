/**
 * React hook for session activity tracking
 * Provides easy integration with React components
 */

import { useState, useEffect, useCallback } from 'react';
import {
  SessionActivityTracker,
  ActivityMetrics,
  ActivityTrackerConfig,
  getSessionActivityTracker
} from '@/lib/session-activity-tracker';

export interface UseSessionActivityOptions {
  config?: Partial<ActivityTrackerConfig>;
  onActivity?: (event: any) => void;
  onIdle?: (idleDuration: number) => void;
  onActive?: () => void;
  enableAutoRenewal?: boolean;
  renewalThreshold?: number; // Time before idle to trigger renewal
}

/**
 * Hook for session activity tracking
 */
export function useSessionActivity(options: UseSessionActivityOptions = {}) {
  const {
    config,
    onActivity,
    onIdle,
    onActive,
    enableAutoRenewal = false,
    renewalThreshold = 2 * 60 * 1000 // 2 minutes before idle
  } = options;

  const [metrics, setMetrics] = useState<ActivityMetrics | null>(null);
  const [isIdle, setIsIdle] = useState(false);
  const [activityScore, setActivityScore] = useState(0);
  const [tracker, setTracker] = useState<SessionActivityTracker | null>(null);

  // Initialize activity tracker
  useEffect(() => {
    const activityTracker = getSessionActivityTracker(config, {
      onActivity: (event) => {
        onActivity?.(event);
        setIsIdle(false);
      },
      onIdle: (idleDuration) => {
        setIsIdle(true);
        onIdle?.(idleDuration);
      },
      onActive: () => {
        setIsIdle(false);
        onActive?.();
      },
      onMetricsUpdate: (newMetrics) => {
        setMetrics(newMetrics);
        setActivityScore(activityTracker.getActivityScore());
      }
    });

    setTracker(activityTracker);

    // Subscribe to metrics updates
    const unsubscribe = activityTracker.subscribe((newMetrics) => {
      setMetrics(newMetrics);
      setActivityScore(activityTracker.getActivityScore());
    });

    // Initial metrics
    setMetrics(activityTracker.getMetrics());
    setActivityScore(activityTracker.getActivityScore());

    return () => {
      unsubscribe();
    };
  }, [config, onActivity, onIdle, onActive]);

  // Auto-renewal logic
  useEffect(() => {
    if (!enableAutoRenewal || !tracker) return;

    const checkForRenewal = () => {
      const timeSinceActivity = tracker.getTimeSinceLastActivity();
      const idleThreshold = config?.idleThreshold || 5 * 60 * 1000;
      
      // If user is approaching idle state and has been active
      if (timeSinceActivity > (idleThreshold - renewalThreshold) && 
          timeSinceActivity < idleThreshold &&
          activityScore > 30) {
        
        // Trigger session renewal
        const renewalEvent = new CustomEvent('sessionRenewalNeeded', {
          detail: {
            timeSinceActivity,
            activityScore,
            reason: 'approaching_idle'
          }
        });
        window.dispatchEvent(renewalEvent);
      }
    };

    const interval = setInterval(checkForRenewal, 30000); // Check every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [enableAutoRenewal, tracker, renewalThreshold, config?.idleThreshold, activityScore]);

  const recordActivity = useCallback((type: string, details?: any) => {
    tracker?.recordActivity(type as any, details);
  }, [tracker]);

  const getTimeSinceLastActivity = useCallback(() => {
    return tracker?.getTimeSinceLastActivity() || 0;
  }, [tracker]);

  const isUserActive = useCallback(() => {
    return tracker?.isUserActive() || false;
  }, [tracker]);

  const resetActivity = useCallback(() => {
    tracker?.reset();
  }, [tracker]);

  return {
    // State
    metrics,
    isIdle,
    activityScore,
    isUserActive: isUserActive(),
    timeSinceLastActivity: getTimeSinceLastActivity(),
    
    // Actions
    recordActivity,
    resetActivity,
    
    // Utilities
    formatTimeSinceActivity: () => {
      const time = getTimeSinceLastActivity();
      const minutes = Math.floor(time / (1000 * 60));
      const seconds = Math.floor((time % (1000 * 60)) / 1000);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    },
    
    getActivityLevel: () => {
      if (activityScore >= 80) return 'very-active';
      if (activityScore >= 60) return 'active';
      if (activityScore >= 40) return 'moderate';
      if (activityScore >= 20) return 'low';
      return 'inactive';
    }
  };
}

/**
 * Hook for activity-based session management
 */
export function useActivityBasedSession(options: {
  renewalThreshold?: number;
  minActivityScore?: number;
  onRenewalNeeded?: () => void;
  onSessionExpiring?: (timeLeft: number) => void;
} = {}) {
  const {
    renewalThreshold = 2 * 60 * 1000,
    minActivityScore = 30,
    onRenewalNeeded,
    onSessionExpiring
  } = options;

  const activity = useSessionActivity({
    enableAutoRenewal: true,
    renewalThreshold,
    onIdle: (idleDuration) => {
      // User has gone idle
      console.log('User idle for:', idleDuration);
    }
  });

  // Listen for renewal events
  useEffect(() => {
    const handleRenewalNeeded = (event: CustomEvent) => {
      const { activityScore, timeSinceActivity } = event.detail;
      
      if (activityScore >= minActivityScore) {
        onRenewalNeeded?.();
      }
    };

    window.addEventListener('sessionRenewalNeeded', handleRenewalNeeded as EventListener);

    return () => {
      window.removeEventListener('sessionRenewalNeeded', handleRenewalNeeded as EventListener);
    };
  }, [minActivityScore, onRenewalNeeded]);

  const shouldRenewSession = useCallback(() => {
    return activity.activityScore >= minActivityScore && !activity.isIdle;
  }, [activity.activityScore, activity.isIdle, minActivityScore]);

  const getSessionHealth = useCallback(() => {
    const health = {
      score: activity.activityScore,
      level: activity.getActivityLevel(),
      isActive: activity.isUserActive,
      timeSinceActivity: activity.timeSinceLastActivity,
      shouldRenew: shouldRenewSession()
    };

    return health;
  }, [activity, shouldRenewSession]);

  return {
    ...activity,
    shouldRenewSession,
    getSessionHealth,
    sessionHealth: getSessionHealth()
  };
}

/**
 * Hook for form activity tracking
 */
export function useFormActivityTracking(formId: string) {
  const { recordActivity } = useSessionActivity();

  const trackFormStart = useCallback(() => {
    recordActivity('form', {
      action: 'form_start',
      element: formId
    });
  }, [recordActivity, formId]);

  const trackFieldInteraction = useCallback((fieldName: string, action: string) => {
    recordActivity('form', {
      action: `field_${action}`,
      element: `${formId}.${fieldName}`
    });
  }, [recordActivity, formId]);

  const trackFormSubmit = useCallback((success: boolean) => {
    recordActivity('form', {
      action: success ? 'form_submit_success' : 'form_submit_error',
      element: formId
    });
  }, [recordActivity, formId]);

  const trackFormAbandon = useCallback(() => {
    recordActivity('form', {
      action: 'form_abandon',
      element: formId
    });
  }, [recordActivity, formId]);

  return {
    trackFormStart,
    trackFieldInteraction,
    trackFormSubmit,
    trackFormAbandon
  };
}

/**
 * Hook for page activity tracking
 */
export function usePageActivityTracking(pageName: string) {
  const { recordActivity } = useSessionActivity();

  useEffect(() => {
    // Track page visit
    recordActivity('navigation', {
      action: 'page_visit',
      page: pageName
    });

    // Track page focus/blur
    const handleFocus = () => {
      recordActivity('focus', {
        action: 'page_focus',
        page: pageName
      });
    };

    const handleBlur = () => {
      recordActivity('focus', {
        action: 'page_blur',
        page: pageName
      });
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    // Track page unload
    const handleUnload = () => {
      recordActivity('navigation', {
        action: 'page_unload',
        page: pageName
      });
    };

    window.addEventListener('beforeunload', handleUnload);

    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      window.removeEventListener('beforeunload', handleUnload);
    };
  }, [pageName, recordActivity]);

  const trackPageAction = useCallback((action: string, details?: any) => {
    recordActivity('navigation', {
      action: `page_${action}`,
      page: pageName,
      ...details
    });
  }, [recordActivity, pageName]);

  return {
    trackPageAction
  };
}
