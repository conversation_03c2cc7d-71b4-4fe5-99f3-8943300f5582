/**
 * Enhanced Session Monitor Hook
 * Provides advanced session monitoring with security features
 */

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { EnhancedSessionManager, SessionMetadata, SessionSecurityEvent } from '@/lib/enhanced-session-manager';

export interface EnhancedSessionState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  metadata?: SessionMetadata;
  securityEvents: SessionSecurityEvent[];
  isOnline: boolean;
  isExpired: boolean;
  securityLevel: 'low' | 'medium' | 'high';
  hasSecurityWarnings: boolean;
  concurrentSessions: number;
}

export interface EnhancedSessionOptions {
  redirectOnExpiry?: boolean;
  redirectTo?: string;
  warningBeforeExpiry?: number; // minutes
  maxConcurrentSessions?: number;
  enableSecurityMonitoring?: boolean;
  enableDeviceTracking?: boolean;
  onSessionExpired?: () => void;
  onSessionWarning?: (minutesLeft: number) => void;
  onSecurityViolation?: (event: SessionSecurityEvent) => void;
  onConcurrentSessionDetected?: (count: number) => void;
}

export function useEnhancedSessionMonitor(options: EnhancedSessionOptions = {}) {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  
  const [sessionState, setSessionState] = useState<EnhancedSessionState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    securityEvents: [],
    isOnline: navigator?.onLine ?? true,
    isExpired: false,
    securityLevel: 'low',
    hasSecurityWarnings: false,
    concurrentSessions: 0
  });

  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [warningShown, setWarningShown] = useState(false);
  const sessionIdRef = useRef<string | null>(null);
  const securityCheckInterval = useRef<NodeJS.Timeout | null>(null);

  // Initialize enhanced session when authenticated
  useEffect(() => {
    const initializeSession = async () => {
      if (status === 'authenticated' && session?.user?.id && !sessionIdRef.current) {
        try {
          const metadata = await EnhancedSessionManager.initializeSession(
            session.user.id,
            undefined, // Request object not available in client
            {
              maxConcurrentSessions: options.maxConcurrentSessions || 5,
              securityChecks: options.enableSecurityMonitoring !== false,
              deviceTracking: options.enableDeviceTracking !== false
            }
          );

          sessionIdRef.current = metadata.sessionId;
          
          setSessionState(prev => ({
            ...prev,
            metadata,
            securityLevel: metadata.securityLevel
          }));
        } catch (error) {
          console.error('Failed to initialize enhanced session:', error);
        }
      }
    };

    initializeSession();
  }, [status, session, options]);

  // Update session state when session changes
  useEffect(() => {
    setSessionState(prev => ({
      ...prev,
      isAuthenticated: status === 'authenticated' && !!session,
      isLoading: status === 'loading',
      user: session?.user || null,
      isExpired: false
    }));
  }, [session, status]);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setSessionState(prev => ({ ...prev, isOnline: true }));
      if (sessionState.isAuthenticated) {
        update();
      }
    };

    const handleOffline = () => {
      setSessionState(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [sessionState.isAuthenticated, update]);

  // Track user activity with enhanced monitoring
  const updateActivity = useCallback(() => {
    setLastActivityTime(Date.now());
    setWarningShown(false);

    // Update session activity in enhanced manager
    if (sessionIdRef.current && sessionState.isAuthenticated) {
      EnhancedSessionManager.validateSession(sessionIdRef.current);
    }
  }, [sessionState.isAuthenticated]);

  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [updateActivity]);

  // Enhanced security monitoring
  useEffect(() => {
    if (!sessionState.isAuthenticated || !sessionIdRef.current || !options.enableSecurityMonitoring) {
      return;
    }

    const performSecurityCheck = async () => {
      try {
        const validation = await EnhancedSessionManager.validateSession(sessionIdRef.current!);
        
        if (!validation.isValid) {
          if (validation.reason === 'security_violation') {
            options.onSecurityViolation?.({
              type: 'security_violation',
              sessionId: sessionIdRef.current!,
              userId: session?.user?.id || '',
              timestamp: Date.now(),
              details: { reason: validation.reason },
              severity: 'critical'
            });
          }

          // Handle session termination
          await signOut({ redirect: false });
          if (options.redirectOnExpiry) {
            router.push(options.redirectTo || '/login');
          }
          return;
        }

        // Update session metadata
        if (validation.metadata) {
          setSessionState(prev => ({
            ...prev,
            metadata: validation.metadata,
            securityLevel: validation.metadata!.securityLevel,
            hasSecurityWarnings: validation.metadata!.flags.length > 0
          }));
        }

        // Check for concurrent sessions
        if (session?.user?.id) {
          const activeSessions = EnhancedSessionManager.getActiveSessions(session.user.id);
          const concurrentCount = activeSessions.length;
          
          setSessionState(prev => ({
            ...prev,
            concurrentSessions: concurrentCount
          }));

          if (concurrentCount > (options.maxConcurrentSessions || 5)) {
            options.onConcurrentSessionDetected?.(concurrentCount);
          }
        }

        // Get recent security events
        if (session?.user?.id) {
          const recentEvents = EnhancedSessionManager.getSecurityEvents(
            session.user.id,
            Date.now() - 24 * 60 * 60 * 1000 // Last 24 hours
          );
          
          setSessionState(prev => ({
            ...prev,
            securityEvents: recentEvents
          }));
        }

      } catch (error) {
        console.error('Security check failed:', error);
      }
    };

    // Perform initial check
    performSecurityCheck();

    // Set up periodic security checks
    securityCheckInterval.current = setInterval(performSecurityCheck, 2 * 60 * 1000); // Every 2 minutes

    return () => {
      if (securityCheckInterval.current) {
        clearInterval(securityCheckInterval.current);
      }
    };
  }, [sessionState.isAuthenticated, session, options, router]);

  // Session expiry monitoring with enhanced logic
  useEffect(() => {
    if (!sessionState.isAuthenticated || !sessionState.metadata) {
      return;
    }

    const checkSessionExpiry = () => {
      const now = Date.now();
      const sessionAge = now - sessionState.metadata!.createdAt;
      const inactivityTime = now - sessionState.metadata!.lastActivity;
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
      const maxInactivity = 24 * 60 * 60 * 1000; // 24 hours
      const warningTime = (options.warningBeforeExpiry || 5) * 60 * 1000; // Convert minutes to ms

      // Check if session is expired
      if (sessionAge >= maxAge || inactivityTime >= maxInactivity) {
        setSessionState(prev => ({ ...prev, isExpired: true }));
        options.onSessionExpired?.();
        
        // Terminate session in enhanced manager
        if (sessionIdRef.current) {
          EnhancedSessionManager.terminateSession(
            sessionIdRef.current,
            sessionAge >= maxAge ? 'expired' : 'timeout'
          );
        }
        
        if (options.redirectOnExpiry) {
          router.push(options.redirectTo || '/login');
        }
        return;
      }

      // Check if we should show warning
      const timeUntilExpiry = Math.min(maxAge - sessionAge, maxInactivity - inactivityTime);
      if (timeUntilExpiry <= warningTime && !warningShown) {
        const minutesLeft = Math.ceil(timeUntilExpiry / (60 * 1000));
        setWarningShown(true);
        options.onSessionWarning?.(minutesLeft);
      }
    };

    const interval = setInterval(checkSessionExpiry, 60000); // Check every minute
    checkSessionExpiry(); // Check immediately

    return () => clearInterval(interval);
  }, [
    sessionState.isAuthenticated,
    sessionState.metadata,
    warningShown,
    options,
    router
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (sessionIdRef.current) {
        EnhancedSessionManager.terminateSession(sessionIdRef.current, 'logout');
      }
    };
  }, []);

  const extendSession = useCallback(async () => {
    if (sessionState.isAuthenticated) {
      await update();
      updateActivity();
      setWarningShown(false);
    }
  }, [sessionState.isAuthenticated, update, updateActivity]);

  const forceRefresh = useCallback(async () => {
    await update();
  }, [update]);

  const terminateAllOtherSessions = useCallback(async () => {
    if (session?.user?.id && sessionIdRef.current) {
      await EnhancedSessionManager.terminateAllUserSessions(
        session.user.id,
        sessionIdRef.current
      );
      
      // Refresh session state
      const activeSessions = EnhancedSessionManager.getActiveSessions(session.user.id);
      setSessionState(prev => ({
        ...prev,
        concurrentSessions: activeSessions.length
      }));
    }
  }, [session]);

  const getSecurityReport = useCallback(() => {
    if (!session?.user?.id) return null;

    return {
      securityLevel: sessionState.securityLevel,
      hasWarnings: sessionState.hasSecurityWarnings,
      concurrentSessions: sessionState.concurrentSessions,
      recentEvents: sessionState.securityEvents,
      sessionAge: sessionState.metadata ? Date.now() - sessionState.metadata.createdAt : 0,
      lastActivity: sessionState.metadata ? Date.now() - sessionState.metadata.lastActivity : 0
    };
  }, [session, sessionState]);

  return {
    sessionState,
    extendSession,
    forceRefresh,
    updateActivity,
    terminateAllOtherSessions,
    getSecurityReport
  };
}

/**
 * Hook for session security monitoring
 */
export function useSessionSecurity() {
  const { sessionState, getSecurityReport } = useEnhancedSessionMonitor({
    enableSecurityMonitoring: true,
    enableDeviceTracking: true
  });

  return {
    securityLevel: sessionState.securityLevel,
    hasWarnings: sessionState.hasSecurityWarnings,
    concurrentSessions: sessionState.concurrentSessions,
    securityEvents: sessionState.securityEvents,
    getSecurityReport
  };
}
