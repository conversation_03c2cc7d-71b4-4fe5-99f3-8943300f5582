/**
 * Memory Management Hook
 * 
 * Provides automatic cleanup of timers, event listeners, and other resources
 * to prevent memory leaks in React components.
 */

import { useEffect, useRef, useCallback } from 'react';

interface TimerRef {
  id: NodeJS.Timeout;
  type: 'timeout' | 'interval';
  callback: () => void;
  delay: number;
}

interface EventListenerRef {
  element: EventTarget;
  event: string;
  handler: EventListener;
  options?: boolean | AddEventListenerOptions;
}

export function useMemoryManagement() {
  const timersRef = useRef<Map<string, TimerRef>>(new Map());
  const eventListenersRef = useRef<Map<string, EventListenerRef>>(new Map());
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map());
  const cleanupFunctionsRef = useRef<Map<string, () => void>>(new Map());

  /**
   * Create a managed timeout that will be automatically cleaned up
   */
  const createTimeout = useCallback((callback: () => void, delay: number, id?: string): string => {
    const timerId = id || `timeout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Clear existing timer with same ID
    if (timersRef.current.has(timerId)) {
      const existingTimer = timersRef.current.get(timerId)!;
      clearTimeout(existingTimer.id);
    }

    const timeoutId = setTimeout(() => {
      callback();
      timersRef.current.delete(timerId);
    }, delay);

    timersRef.current.set(timerId, {
      id: timeoutId,
      type: 'timeout',
      callback,
      delay
    });

    return timerId;
  }, []);

  /**
   * Create a managed interval that will be automatically cleaned up
   */
  const createInterval = useCallback((callback: () => void, delay: number, id?: string): string => {
    const timerId = id || `interval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Clear existing timer with same ID
    if (timersRef.current.has(timerId)) {
      const existingTimer = timersRef.current.get(timerId)!;
      clearInterval(existingTimer.id);
    }

    const intervalId = setInterval(callback, delay);

    timersRef.current.set(timerId, {
      id: intervalId,
      type: 'interval',
      callback,
      delay
    });

    return timerId;
  }, []);

  /**
   * Clear a specific timer
   */
  const clearTimer = useCallback((timerId: string) => {
    const timer = timersRef.current.get(timerId);
    if (timer) {
      if (timer.type === 'timeout') {
        clearTimeout(timer.id);
      } else {
        clearInterval(timer.id);
      }
      timersRef.current.delete(timerId);
    }
  }, []);

  /**
   * Add a managed event listener that will be automatically cleaned up
   */
  const addEventListener = useCallback((
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions,
    id?: string
  ): string => {
    const listenerId = id || `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Remove existing listener with same ID
    if (eventListenersRef.current.has(listenerId)) {
      const existingListener = eventListenersRef.current.get(listenerId)!;
      existingListener.element.removeEventListener(existingListener.event, existingListener.handler, existingListener.options);
    }

    element.addEventListener(event, handler, options);

    eventListenersRef.current.set(listenerId, {
      element,
      event,
      handler,
      options
    });

    return listenerId;
  }, []);

  /**
   * Remove a specific event listener
   */
  const removeEventListener = useCallback((listenerId: string) => {
    const listener = eventListenersRef.current.get(listenerId);
    if (listener) {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options);
      eventListenersRef.current.delete(listenerId);
    }
  }, []);

  /**
   * Create a managed AbortController for fetch requests
   */
  const createAbortController = useCallback((id?: string): { controller: AbortController; id: string } => {
    const controllerId = id || `controller_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Abort existing controller with same ID
    if (abortControllersRef.current.has(controllerId)) {
      const existingController = abortControllersRef.current.get(controllerId)!;
      existingController.abort();
    }

    const controller = new AbortController();
    abortControllersRef.current.set(controllerId, controller);

    return { controller, id: controllerId };
  }, []);

  /**
   * Abort a specific controller
   */
  const abortController = useCallback((controllerId: string) => {
    const controller = abortControllersRef.current.get(controllerId);
    if (controller) {
      controller.abort();
      abortControllersRef.current.delete(controllerId);
    }
  }, []);

  /**
   * Register a custom cleanup function
   */
  const registerCleanup = useCallback((cleanupFn: () => void, id?: string): string => {
    const cleanupId = id || `cleanup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Execute existing cleanup function with same ID
    if (cleanupFunctionsRef.current.has(cleanupId)) {
      const existingCleanup = cleanupFunctionsRef.current.get(cleanupId)!;
      existingCleanup();
    }

    cleanupFunctionsRef.current.set(cleanupId, cleanupFn);
    return cleanupId;
  }, []);

  /**
   * Execute and remove a specific cleanup function
   */
  const executeCleanup = useCallback((cleanupId: string) => {
    const cleanupFn = cleanupFunctionsRef.current.get(cleanupId);
    if (cleanupFn) {
      cleanupFn();
      cleanupFunctionsRef.current.delete(cleanupId);
    }
  }, []);

  /**
   * Get memory usage statistics
   */
  const getMemoryStats = useCallback(() => {
    return {
      timers: timersRef.current.size,
      eventListeners: eventListenersRef.current.size,
      abortControllers: abortControllersRef.current.size,
      cleanupFunctions: cleanupFunctionsRef.current.size,
      totalManagedResources: timersRef.current.size + eventListenersRef.current.size + abortControllersRef.current.size + cleanupFunctionsRef.current.size
    };
  }, []);

  /**
   * Force cleanup of all resources
   */
  const forceCleanup = useCallback(() => {
    // Clear all timers
    timersRef.current.forEach((timer) => {
      if (timer.type === 'timeout') {
        clearTimeout(timer.id);
      } else {
        clearInterval(timer.id);
      }
    });
    timersRef.current.clear();

    // Remove all event listeners
    eventListenersRef.current.forEach((listener) => {
      listener.element.removeEventListener(listener.event, listener.handler, listener.options);
    });
    eventListenersRef.current.clear();

    // Abort all controllers
    abortControllersRef.current.forEach((controller) => {
      controller.abort();
    });
    abortControllersRef.current.clear();

    // Execute all cleanup functions
    cleanupFunctionsRef.current.forEach((cleanupFn) => {
      try {
        cleanupFn();
      } catch (error) {
        console.error('Error executing cleanup function:', error);
      }
    });
    cleanupFunctionsRef.current.clear();
  }, []);

  // Automatic cleanup on component unmount
  useEffect(() => {
    return () => {
      forceCleanup();
    };
  }, [forceCleanup]);

  // Memory monitoring in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const monitoringInterval = setInterval(() => {
        const stats = getMemoryStats();
        if (stats.totalManagedResources > 50) {
          console.warn('High number of managed resources detected:', stats);
        }
      }, 30000); // Check every 30 seconds

      return () => clearInterval(monitoringInterval);
    }
  }, [getMemoryStats]);

  return {
    // Timer management
    createTimeout,
    createInterval,
    clearTimer,
    
    // Event listener management
    addEventListener,
    removeEventListener,
    
    // Fetch request management
    createAbortController,
    abortController,
    
    // Custom cleanup management
    registerCleanup,
    executeCleanup,
    
    // Utilities
    getMemoryStats,
    forceCleanup
  };
}

export default useMemoryManagement;
