/**
 * React hook for navigation state management
 * Provides persistent navigation state across page refreshes
 */

import { useState, useEffect, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { getNavigationStateManager, NavigationState } from '@/lib/navigation-state-manager';

export interface UseNavigationStateReturn {
  // Current state
  navigationState: NavigationState;
  
  // Mobile menu controls
  isMobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
  closeMobileMenu: () => void;
  
  // Tools dropdown controls
  isToolsDropdownOpen: boolean;
  toggleToolsDropdown: () => void;
  closeToolsDropdown: () => void;
  
  // Tab management
  activeTab: string | null;
  setActiveTab: (tab: string) => void;
  
  // Page tracking
  recentPages: string[];
  addRecentPage: (path: string) => void;
  clearRecentPages: () => void;
  
  // Preferences
  updatePreferences: (preferences: Partial<NavigationState['navigationPreferences']>) => void;
  
  // Utility functions
  closeAllMenus: () => void;
  isMenuOpen: boolean;
}

/**
 * Main navigation state hook
 */
export function useNavigationState(): UseNavigationStateReturn {
  const manager = getNavigationStateManager();
  const router = useRouter();
  const pathname = usePathname();
  const [navigationState, setNavigationState] = useState<NavigationState>(manager.getState());

  // Set up real-time state updates
  useEffect(() => {
    const unsubscribe = manager.subscribe((newState) => {
      setNavigationState(newState);
    });

    // Initial state sync
    setNavigationState(manager.getState());

    return () => {
      unsubscribe();
    };
  }, [manager]);

  // Track page changes
  useEffect(() => {
    if (pathname) {
      manager.addRecentPage(pathname);
    }
  }, [pathname, manager]);

  // Mobile menu controls
  const toggleMobileMenu = useCallback(() => {
    manager.toggleMobileMenu();
  }, [manager]);

  const closeMobileMenu = useCallback(() => {
    manager.closeMobileMenu();
  }, [manager]);

  // Tools dropdown controls
  const toggleToolsDropdown = useCallback(() => {
    manager.toggleToolsDropdown();
  }, [manager]);

  const closeToolsDropdown = useCallback(() => {
    manager.closeToolsDropdown();
  }, [manager]);

  // Tab management
  const setActiveTab = useCallback((tab: string) => {
    manager.setActiveTab(tab);
  }, [manager]);

  // Page tracking
  const addRecentPage = useCallback((path: string) => {
    manager.addRecentPage(path);
  }, [manager]);

  const clearRecentPages = useCallback(() => {
    manager.clearRecentPages();
  }, [manager]);

  // Preferences
  const updatePreferences = useCallback((preferences: Partial<NavigationState['navigationPreferences']>) => {
    manager.setState({
      navigationPreferences: {
        ...navigationState.navigationPreferences,
        ...preferences
      }
    });
  }, [manager, navigationState.navigationPreferences]);

  // Utility functions
  const closeAllMenus = useCallback(() => {
    manager.setState({
      isMobileMenuOpen: false,
      isToolsDropdownOpen: false
    });
  }, [manager]);

  const isMenuOpen = navigationState.isMobileMenuOpen || navigationState.isToolsDropdownOpen;

  return {
    navigationState,
    
    // Mobile menu
    isMobileMenuOpen: navigationState.isMobileMenuOpen,
    toggleMobileMenu,
    closeMobileMenu,
    
    // Tools dropdown
    isToolsDropdownOpen: navigationState.isToolsDropdownOpen,
    toggleToolsDropdown,
    closeToolsDropdown,
    
    // Tab management
    activeTab: navigationState.lastActiveTab,
    setActiveTab,
    
    // Page tracking
    recentPages: navigationState.recentlyVisitedPages,
    addRecentPage,
    clearRecentPages,
    
    // Preferences
    updatePreferences,
    
    // Utilities
    closeAllMenus,
    isMenuOpen
  };
}

/**
 * Hook for components that only need mobile menu state
 */
export function useMobileMenu() {
  const { isMobileMenuOpen, toggleMobileMenu, closeMobileMenu } = useNavigationState();
  
  return {
    isMobileMenuOpen,
    toggleMobileMenu,
    closeMobileMenu
  };
}

/**
 * Hook for components that only need dropdown state
 */
export function useDropdownState(dropdownId: string) {
  const { isToolsDropdownOpen, toggleToolsDropdown, closeToolsDropdown } = useNavigationState();
  
  // For now, we only have tools dropdown, but this can be extended
  const isOpen = dropdownId === 'tools' ? isToolsDropdownOpen : false;
  const toggle = dropdownId === 'tools' ? toggleToolsDropdown : () => {};
  const close = dropdownId === 'tools' ? closeToolsDropdown : () => {};
  
  return {
    isOpen,
    toggle,
    close
  };
}

/**
 * Hook for recent pages functionality
 */
export function useRecentPages() {
  const { recentPages, addRecentPage, clearRecentPages } = useNavigationState();
  
  const getRecentPagesWithTitles = useCallback(() => {
    return recentPages.map(path => ({
      path,
      title: getPageTitle(path),
      timestamp: Date.now() // This could be enhanced to track actual visit times
    }));
  }, [recentPages]);
  
  return {
    recentPages,
    recentPagesWithTitles: getRecentPagesWithTitles(),
    addRecentPage,
    clearRecentPages
  };
}

/**
 * Hook for navigation preferences
 */
export function useNavigationPreferences() {
  const { navigationState, updatePreferences } = useNavigationState();
  
  return {
    preferences: navigationState.navigationPreferences,
    updatePreferences,
    
    // Convenience methods
    toggleTooltips: () => updatePreferences({ 
      showTooltips: !navigationState.navigationPreferences.showTooltips 
    }),
    toggleCompactMode: () => updatePreferences({ 
      compactMode: !navigationState.navigationPreferences.compactMode 
    }),
    toggleAutoCloseMenus: () => updatePreferences({ 
      autoCloseMenus: !navigationState.navigationPreferences.autoCloseMenus 
    })
  };
}

/**
 * Utility function to get page title from path
 */
function getPageTitle(path: string): string {
  const titleMap: Record<string, string> = {
    '/': 'Home',
    '/dashboard': 'Dashboard',
    '/career-paths': 'Career Paths',
    '/assessment': 'Assessment',
    '/interview-practice': 'Interview Practice',
    '/resume-builder': 'Resume Builder',
    '/learning-resources': 'Learning Resources',
    '/forum': 'Forum',
    '/profile': 'Profile',
    '/settings': 'Settings',
    '/progress': 'Progress Tracking',
    '/salary-calculator': 'Salary Calculator'
  };
  
  return titleMap[path] || path.split('/').pop()?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown Page';
}

/**
 * Hook for keyboard navigation shortcuts
 */
export function useNavigationShortcuts() {
  const { closeAllMenus, toggleMobileMenu } = useNavigationState();
  
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Escape key closes all menus
      if (event.key === 'Escape') {
        closeAllMenus();
      }
      
      // Alt + M toggles mobile menu (for accessibility)
      if (event.altKey && event.key === 'm') {
        event.preventDefault();
        toggleMobileMenu();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [closeAllMenus, toggleMobileMenu]);
}
