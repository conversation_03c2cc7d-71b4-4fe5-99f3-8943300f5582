/**
 * Simple Resume Builder Tests
 *
 * Basic tests for Resume Builder functionality without complex dependencies
 */

import { z } from 'zod';

// Define the validation schemas (copied from API route)
const personalInfoSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  email: z.string().email('Valid email is required'),
  phone: z.string().optional(),
  location: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  linkedIn: z.string().url().optional().or(z.literal(''))
});

const experienceSchema = z.object({
  company: z.string().min(1, 'Company name is required'),
  position: z.string().min(1, 'Position is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  description: z.string().optional(),
  achievements: z.array(z.string()).optional()
});

const educationSchema = z.object({
  institution: z.string().min(1, 'Institution is required'),
  degree: z.string().min(1, 'Degree is required'),
  field: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  gpa: z.string().optional(),
  honors: z.string().optional()
});

const skillSchema = z.object({
  name: z.string().min(1, 'Skill name is required'),
  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  category: z.string().optional()
});

const resumeValidationSchema = z.object({
  title: z.string().min(1, 'Resume title is required'),
  personalInfo: personalInfoSchema,
  summary: z.string().optional(),
  experience: z.array(experienceSchema).optional(),
  education: z.array(educationSchema).optional(),
  skills: z.array(skillSchema).optional(),
  sections: z.record(z.any()).optional(),
  template: z.enum(['modern', 'classic', 'minimal', 'creative']).default('modern'),
  isPublic: z.boolean().default(false)
});

describe('Resume Builder - Simple Tests', () => {
  describe('Resume Validation Schema', () => {
    it('should validate a complete resume object', () => {
      const validResume = {
        title: 'Software Engineer Resume',
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          location: 'San Francisco, CA',
          website: 'https://johndoe.com',
          linkedIn: 'https://linkedin.com/in/johndoe'
        },
        summary: 'Experienced software engineer with 5+ years of experience.',
        experience: [
          {
            company: 'Tech Corp',
            position: 'Senior Software Engineer',
            startDate: '2020-01',
            endDate: '2023-12',
            description: 'Led development of web applications.',
            achievements: ['Improved performance by 40%', 'Mentored 3 developers']
          }
        ],
        education: [
          {
            institution: 'University of California',
            degree: 'Bachelor of Science',
            field: 'Computer Science',
            startDate: '2016-09',
            endDate: '2020-05',
            gpa: '3.8'
          }
        ],
        skills: [
          {
            name: 'JavaScript',
            level: 'ADVANCED',
            category: 'Programming Languages'
          }
        ],
        template: 'modern',
        isPublic: false
      };

      const result = resumeValidationSchema.safeParse(validResume);
      expect(result.success).toBe(true);
    });

    it('should reject resume with missing required fields', () => {
      const invalidResume = {
        // Missing title
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: 'invalid-email' // Invalid email format
        },
        experience: [],
        education: [],
        skills: []
      };

      const result = resumeValidationSchema.safeParse(invalidResume);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const errors = result.error.issues;
        expect(errors.some(error => error.path.includes('title'))).toBe(true);
        expect(errors.some(error => error.path.includes('email'))).toBe(true);
      }
    });

    it('should validate personal info fields correctly', () => {
      const validPersonalInfo = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '******-0456',
        location: 'New York, NY',
        website: 'https://janesmith.dev',
        linkedIn: 'https://linkedin.com/in/janesmith'
      };

      // Test as part of a minimal resume
      const resume = {
        title: 'Test Resume',
        personalInfo: validPersonalInfo,
        experience: [],
        education: [],
        skills: []
      };

      const result = resumeValidationSchema.safeParse(resume);
      expect(result.success).toBe(true);
    });

    it('should validate experience entries correctly', () => {
      const validExperience = [
        {
          company: 'StartupCo',
          position: 'Full Stack Developer',
          startDate: '2021-06',
          endDate: '2023-12',
          description: 'Developed web applications using React and Node.js.',
          achievements: [
            'Built authentication system for 10,000+ users',
            'Reduced page load times by 60%'
          ]
        },
        {
          company: 'Current Company',
          position: 'Senior Developer',
          startDate: '2024-01',
          // No end date for current position
          description: 'Leading frontend development team.'
        }
      ];

      const resume = {
        title: 'Developer Resume',
        personalInfo: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: validExperience,
        education: [],
        skills: []
      };

      const result = resumeValidationSchema.safeParse(resume);
      expect(result.success).toBe(true);
    });

    it('should validate education entries correctly', () => {
      const validEducation = [
        {
          institution: 'State University',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          startDate: '2017-09',
          endDate: '2021-05',
          gpa: '3.7',
          honors: 'Magna Cum Laude'
        },
        {
          institution: 'Online Academy',
          degree: 'Certificate',
          field: 'Web Development',
          startDate: '2021-01',
          endDate: '2021-03'
        }
      ];

      const resume = {
        title: 'Graduate Resume',
        personalInfo: {
          firstName: 'Test',
          lastName: 'Graduate',
          email: '<EMAIL>'
        },
        experience: [],
        education: validEducation,
        skills: []
      };

      const result = resumeValidationSchema.safeParse(resume);
      expect(result.success).toBe(true);
    });

    it('should validate skills entries correctly', () => {
      const validSkills = [
        {
          name: 'React',
          level: 'ADVANCED',
          category: 'Frontend Frameworks'
        },
        {
          name: 'Node.js',
          level: 'INTERMEDIATE',
          category: 'Backend Technologies'
        },
        {
          name: 'PostgreSQL',
          level: 'INTERMEDIATE',
          category: 'Databases'
        },
        {
          name: 'Communication',
          level: 'ADVANCED',
          category: 'Soft Skills'
        }
      ];

      const resume = {
        title: 'Skills-focused Resume',
        personalInfo: {
          firstName: 'Skilled',
          lastName: 'Developer',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: validSkills
      };

      const result = resumeValidationSchema.safeParse(resume);
      expect(result.success).toBe(true);
    });

    it('should validate template options', () => {
      const templates = ['modern', 'classic', 'minimal', 'creative'];
      
      templates.forEach(template => {
        const resume = {
          title: `${template} Resume`,
          personalInfo: {
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>'
          },
          experience: [],
          education: [],
          skills: [],
          template
        };

        const result = resumeValidationSchema.safeParse(resume);
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid template options', () => {
      const resume = {
        title: 'Invalid Template Resume',
        personalInfo: {
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: [],
        template: 'invalid-template'
      };

      const result = resumeValidationSchema.safeParse(resume);
      expect(result.success).toBe(false);
    });

    it('should validate skill levels correctly', () => {
      const validLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
      
      validLevels.forEach(level => {
        const resume = {
          title: 'Skill Level Test',
          personalInfo: {
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>'
          },
          experience: [],
          education: [],
          skills: [
            {
              name: 'Test Skill',
              level,
              category: 'Test Category'
            }
          ]
        };

        const result = resumeValidationSchema.safeParse(resume);
        expect(result.success).toBe(true);
      });
    });

    it('should handle optional fields correctly', () => {
      const minimalResume = {
        title: 'Minimal Resume',
        personalInfo: {
          firstName: 'Min',
          lastName: 'User',
          email: '<EMAIL>'
        },
        experience: [],
        education: [],
        skills: []
      };

      const result = resumeValidationSchema.safeParse(minimalResume);
      expect(result.success).toBe(true);
    });

    it('should validate URL formats for website and LinkedIn', () => {
      const resumeWithUrls = {
        title: 'URL Test Resume',
        personalInfo: {
          firstName: 'URL',
          lastName: 'Tester',
          email: '<EMAIL>',
          website: 'https://example.com',
          linkedIn: 'https://linkedin.com/in/urltester'
        },
        experience: [],
        education: [],
        skills: []
      };

      const result = resumeValidationSchema.safeParse(resumeWithUrls);
      expect(result.success).toBe(true);
    });

    it('should reject invalid URL formats', () => {
      const resumeWithInvalidUrls = {
        title: 'Invalid URL Resume',
        personalInfo: {
          firstName: 'Invalid',
          lastName: 'URL',
          email: '<EMAIL>',
          website: 'not-a-url',
          linkedIn: 'also-not-a-url'
        },
        experience: [],
        education: [],
        skills: []
      };

      const result = resumeValidationSchema.safeParse(resumeWithInvalidUrls);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const errors = result.error.issues;
        expect(errors.some(error => error.path.includes('website'))).toBe(true);
        expect(errors.some(error => error.path.includes('linkedIn'))).toBe(true);
      }
    });
  });
});
