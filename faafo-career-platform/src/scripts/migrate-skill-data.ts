#!/usr/bin/env tsx
/**
 * Skill Data Migration Script
 * Migrates existing user skill data to new Skill Gap Analyzer schema
 * Run with: npm run migrate:skills [userId] or npm run migrate:skills --all
 */

import { SkillDataMigrator } from '@/lib/migration/skill-data-migrator';
import { prisma } from '@/lib/prisma';

interface MigrationOptions {
  userId?: string;
  all?: boolean;
  dryRun?: boolean;
  verbose?: boolean;
  batchSize?: number;
}

async function parseArgs(): Promise<MigrationOptions> {
  const args = process.argv.slice(2);
  const options: MigrationOptions = {
    batchSize: 10,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--all':
        options.all = true;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--verbose':
        options.verbose = true;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]) || 10;
        break;
      default:
        if (!arg.startsWith('--') && !options.userId) {
          options.userId = arg;
        }
        break;
    }
  }

  return options;
}

async function getAllUserIds(): Promise<string[]> {
  const users = await prisma.user.findMany({
    select: { id: true },
    orderBy: { createdAt: 'asc' },
  });
  
  return users.map(user => user.id);
}

async function migrateUser(migrator: SkillDataMigrator, userId: string, options: MigrationOptions): Promise<void> {
  try {
    if (options.verbose) {
      console.log(`\n🔄 Migrating user: ${userId}`);
    }

    // Check if user already migrated
    const migrationStatus = await migrator.getMigrationStatus(userId);
    if (migrationStatus.isMigrated && !options.dryRun) {
      if (options.verbose) {
        console.log(`  ⏭️  User ${userId} already migrated (${migrationStatus.totalRecordsMigrated} records)`);
      }
      return;
    }

    if (options.dryRun) {
      console.log(`  🔍 DRY RUN: Would migrate user ${userId}`);
      
      // Show what would be migrated
      const userSkillProgress = await prisma.userSkillProgress.findMany({
        where: { userId },
        include: { skill: true },
      });
      
      const assessments = await prisma.assessment.findMany({
        where: { userId, status: 'COMPLETED' },
        include: { responses: true },
      });
      
      const interviewProgress = await prisma.interviewProgress.findMany({
        where: { userId },
      });

      console.log(`    - UserSkillProgress records: ${userSkillProgress.length}`);
      console.log(`    - Legacy assessments: ${assessments.length}`);
      console.log(`    - Interview progress records: ${interviewProgress.length}`);
      
      return;
    }

    // Perform actual migration
    const result = await migrator.migrateUserData(userId);
    
    if (result.success) {
      console.log(`  ✅ User ${userId} migrated successfully:`);
      console.log(`    - Total records migrated: ${result.totalMigrated}`);
      console.log(`    - UserSkillProgress: ${result.userSkillProgress.migratedCount} migrated, ${result.userSkillProgress.skippedCount} skipped`);
      console.log(`    - Legacy assessments: ${result.legacyAssessments.migratedCount} migrated, ${result.legacyAssessments.skippedCount} skipped`);
      console.log(`    - Interview progress: ${result.interviewProgress.migratedCount} migrated, ${result.interviewProgress.skippedCount} skipped`);
      
      if (result.legacyAssessments.createdSkills) {
        console.log(`    - New skills created: ${result.legacyAssessments.createdSkills}`);
      }
    } else {
      console.error(`  ❌ Migration failed for user ${userId}:`);
      if (result.errors) {
        result.errors.forEach(error => console.error(`    - ${error}`));
      }
    }

    // Validate migrated data
    if (result.success && options.verbose) {
      const validation = await migrator.validateMigratedData(userId);
      if (validation.isValid) {
        console.log(`    ✅ Data validation passed (score: ${validation.dataConsistencyScore}%)`);
      } else {
        console.warn(`    ⚠️  Data validation issues found:`);
        validation.validationErrors.forEach(error => console.warn(`      - ${error}`));
      }
    }

  } catch (error) {
    console.error(`  ❌ Error migrating user ${userId}:`, error);
  }
}

async function generateMigrationReport(migrator: SkillDataMigrator, userIds: string[]): Promise<void> {
  console.log('\n📊 Migration Report');
  console.log('==================');
  
  let totalUsers = 0;
  let migratedUsers = 0;
  let totalRecords = 0;
  let errors = 0;

  for (const userId of userIds) {
    try {
      totalUsers++;
      const status = await migrator.getMigrationStatus(userId);
      
      if (status.isMigrated) {
        migratedUsers++;
        totalRecords += status.totalRecordsMigrated;
      }
    } catch (error) {
      errors++;
    }
  }

  console.log(`Total users: ${totalUsers}`);
  console.log(`Migrated users: ${migratedUsers}`);
  console.log(`Migration rate: ${((migratedUsers / totalUsers) * 100).toFixed(1)}%`);
  console.log(`Total records migrated: ${totalRecords}`);
  console.log(`Errors encountered: ${errors}`);
  
  // Check for data inconsistencies
  if (migratedUsers > 0) {
    console.log('\n🔍 Data Consistency Check');
    console.log('=========================');
    
    let inconsistencyCount = 0;
    for (const userId of userIds.slice(0, 10)) { // Check first 10 users
      try {
        const inconsistencies = await migrator.detectDataInconsistencies(userId);
        if (inconsistencies.length > 0) {
          inconsistencyCount += inconsistencies.length;
          console.log(`User ${userId}: ${inconsistencies.length} inconsistencies found`);
        }
      } catch (error) {
        // Skip users with errors
      }
    }
    
    if (inconsistencyCount === 0) {
      console.log('✅ No data inconsistencies detected in sample');
    } else {
      console.log(`⚠️  ${inconsistencyCount} data inconsistencies detected in sample`);
    }
  }
}

async function main(): Promise<void> {
  console.log('🚀 Starting Skill Data Migration...\n');
  
  const options = await parseArgs();
  const migrator = new SkillDataMigrator();
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN MODE - No data will be modified\n');
  }
  
  if (options.verbose) {
    console.log('📝 Verbose mode enabled\n');
  }

  try {
    let userIds: string[] = [];
    
    if (options.all) {
      console.log('📋 Getting all user IDs...');
      userIds = await getAllUserIds();
      console.log(`Found ${userIds.length} users to migrate\n`);
    } else if (options.userId) {
      userIds = [options.userId];
      console.log(`Migrating single user: ${options.userId}\n`);
    } else {
      console.error('❌ Please specify --all or provide a userId');
      console.log('\nUsage:');
      console.log('  npm run migrate:skills --all [--dry-run] [--verbose]');
      console.log('  npm run migrate:skills <userId> [--dry-run] [--verbose]');
      process.exit(1);
    }

    const startTime = Date.now();
    
    // Process users in batches
    const batchSize = options.batchSize || 10;
    for (let i = 0; i < userIds.length; i += batchSize) {
      const batch = userIds.slice(i, i + batchSize);
      
      if (options.verbose) {
        console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(userIds.length / batchSize)} (${batch.length} users)`);
      }
      
      // Process batch in parallel
      await Promise.all(
        batch.map(userId => migrateUser(migrator, userId, options))
      );
      
      // Small delay between batches to avoid overwhelming the database
      if (i + batchSize < userIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const duration = Date.now() - startTime;
    
    if (!options.dryRun) {
      await generateMigrationReport(migrator, userIds);
    }
    
    console.log(`\n✅ Migration completed in ${duration}ms`);
    
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as migrateSkillData };
