import { z } from 'zod';

export interface BusinessLogicValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
  securityFlags?: string[];
}

export class BusinessLogicSecurity {
  // Score validation constants
  private static readonly MIN_SCORE = 0;
  private static readonly MAX_SCORE = 10;
  private static readonly REASONABLE_TIME_LIMITS = {
    preparationTime: { min: 0, max: 30 * 60 }, // 0-30 minutes
    responseTime: { min: 0, max: 60 * 60 }, // 0-60 minutes
    totalSessionTime: { min: 0, max: 4 * 60 * 60 }, // 0-4 hours
  };

  /**
   * Validate AI scores to prevent manipulation and ensure reasonable ranges
   */
  static validateAIScore(score: number, context: {
    questionType?: string;
    responseLength?: number;
    responseTime?: number;
  }): BusinessLogicValidationResult {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    // Basic range validation
    if (typeof score !== 'number' || isNaN(score)) {
      errors.push('Score must be a valid number');
      return { isValid: false, errors, securityFlags };
    }

    if (score < this.MIN_SCORE || score > this.MAX_SCORE) {
      errors.push(`Score must be between ${this.MIN_SCORE} and ${this.MAX_SCORE}`);
      securityFlags.push('score_out_of_range');
    }

    // Contextual validation
    if (context.responseLength !== undefined) {
      // Very short responses shouldn't get high scores
      if (context.responseLength < 50 && score > 8) {
        securityFlags.push('high_score_short_response');
      }
      
      // Very long responses might indicate copy-paste
      if (context.responseLength > 5000 && score > 9) {
        securityFlags.push('high_score_very_long_response');
      }
    }

    if (context.responseTime !== undefined) {
      // Suspiciously fast responses with high scores
      if (context.responseTime < 30 && score > 8) {
        securityFlags.push('high_score_fast_response');
      }
    }

    // Round to reasonable precision (1 decimal place)
    const sanitizedScore = Math.round(score * 10) / 10;

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: sanitizedScore,
      securityFlags
    };
  }

  /**
   * Validate time tracking to prevent manipulation
   */
  static validateTimeTracking(timeData: {
    preparationTime: number;
    responseTime: number;
    totalTime?: number;
  }): BusinessLogicValidationResult {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    // Validate preparation time
    if (timeData.preparationTime < this.REASONABLE_TIME_LIMITS.preparationTime.min ||
        timeData.preparationTime > this.REASONABLE_TIME_LIMITS.preparationTime.max) {
      errors.push('Preparation time is outside reasonable limits');
      securityFlags.push('suspicious_preparation_time');
    }

    // Validate response time
    if (timeData.responseTime < this.REASONABLE_TIME_LIMITS.responseTime.min ||
        timeData.responseTime > this.REASONABLE_TIME_LIMITS.responseTime.max) {
      errors.push('Response time is outside reasonable limits');
      securityFlags.push('suspicious_response_time');
    }

    // Validate total time consistency
    if (timeData.totalTime !== undefined) {
      const expectedTotal = timeData.preparationTime + timeData.responseTime;
      const timeDifference = Math.abs(timeData.totalTime - expectedTotal);
      
      if (timeDifference > 60) { // Allow 1 minute tolerance
        securityFlags.push('time_inconsistency');
      }
    }

    // Check for impossible time values (negative or zero when should be positive)
    if (timeData.preparationTime < 0 || timeData.responseTime < 0) {
      errors.push('Time values cannot be negative');
      securityFlags.push('negative_time_values');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: {
        preparationTime: Math.max(0, Math.round(timeData.preparationTime)),
        responseTime: Math.max(0, Math.round(timeData.responseTime)),
        totalTime: timeData.totalTime ? Math.round(timeData.totalTime) : undefined,
      },
      securityFlags
    };
  }

  /**
   * Validate session progress to prevent manipulation
   */
  static validateSessionProgress(progressData: {
    completedQuestions: number;
    totalQuestions: number;
    currentQuestionIndex: number;
    sessionStatus: string;
  }): BusinessLogicValidationResult {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    // Basic validation
    if (progressData.completedQuestions < 0 || progressData.totalQuestions <= 0) {
      errors.push('Invalid question counts');
      return { isValid: false, errors, securityFlags };
    }

    if (progressData.completedQuestions > progressData.totalQuestions) {
      errors.push('Completed questions cannot exceed total questions');
      securityFlags.push('impossible_progress');
    }

    if (progressData.currentQuestionIndex < 0 || 
        progressData.currentQuestionIndex >= progressData.totalQuestions) {
      errors.push('Current question index is out of bounds');
      securityFlags.push('invalid_question_index');
    }

    // Status consistency validation
    const validStatuses = ['NOT_STARTED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED'];
    if (!validStatuses.includes(progressData.sessionStatus)) {
      errors.push('Invalid session status');
      securityFlags.push('invalid_status');
    }

    // Logic validation
    if (progressData.sessionStatus === 'COMPLETED' && 
        progressData.completedQuestions < progressData.totalQuestions) {
      securityFlags.push('completed_status_incomplete_questions');
    }

    if (progressData.sessionStatus === 'NOT_STARTED' && 
        progressData.completedQuestions > 0) {
      securityFlags.push('not_started_with_progress');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: {
        completedQuestions: Math.max(0, Math.min(progressData.completedQuestions, progressData.totalQuestions)),
        totalQuestions: Math.max(1, progressData.totalQuestions),
        currentQuestionIndex: Math.max(0, Math.min(progressData.currentQuestionIndex, progressData.totalQuestions - 1)),
        sessionStatus: validStatuses.includes(progressData.sessionStatus) ? progressData.sessionStatus : 'IN_PROGRESS',
      },
      securityFlags
    };
  }

  /**
   * Validate response content for security and quality
   */
  static validateResponseContent(content: {
    responseText: string;
    userNotes?: string;
    questionId: string;
    userId: string;
  }): BusinessLogicValidationResult {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    // Content length validation
    if (content.responseText.length < 10) {
      errors.push('Response is too short');
    }

    if (content.responseText.length > 10000) {
      errors.push('Response is too long');
      securityFlags.push('extremely_long_response');
    }

    // Security patterns detection
    const securityPatterns = [
      { pattern: /<script/gi, flag: 'script_injection_attempt' },
      { pattern: /javascript:/gi, flag: 'javascript_url_attempt' },
      { pattern: /data:.*base64/gi, flag: 'base64_data_url' },
      { pattern: /\bon\w+\s*=/gi, flag: 'event_handler_attempt' },
      { pattern: /eval\s*\(/gi, flag: 'eval_attempt' },
      { pattern: /document\./gi, flag: 'dom_access_attempt' },
      { pattern: /window\./gi, flag: 'window_access_attempt' },
    ];

    securityPatterns.forEach(({ pattern, flag }) => {
      if (pattern.test(content.responseText)) {
        securityFlags.push(flag);
      }
    });

    // Detect potential copy-paste indicators
    if (this.detectCopyPasteIndicators(content.responseText)) {
      securityFlags.push('potential_copy_paste');
    }

    // Sanitize content
    const sanitizedResponseText = this.sanitizeResponseText(content.responseText);
    const sanitizedUserNotes = content.userNotes ? 
      this.sanitizeResponseText(content.userNotes) : undefined;

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: {
        responseText: sanitizedResponseText,
        userNotes: sanitizedUserNotes,
        questionId: content.questionId,
        userId: content.userId,
      },
      securityFlags
    };
  }

  /**
   * Validate question order integrity
   */
  static validateQuestionOrder(questions: Array<{
    id: string;
    questionOrder: number;
    sessionId: string;
  }>): BusinessLogicValidationResult {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    if (questions.length === 0) {
      errors.push('No questions provided');
      return { isValid: false, errors, securityFlags };
    }

    // Check for duplicate orders
    const orders = questions.map(q => q.questionOrder);
    const uniqueOrders = new Set(orders);
    
    if (orders.length !== uniqueOrders.size) {
      errors.push('Duplicate question orders detected');
      securityFlags.push('duplicate_question_orders');
    }

    // Check for sequential order (should start from 1)
    const sortedOrders = [...orders].sort((a, b) => a - b);
    for (let i = 0; i < sortedOrders.length; i++) {
      if (sortedOrders[i] !== i + 1) {
        securityFlags.push('non_sequential_question_order');
        break;
      }
    }

    // Check for reasonable order values
    const maxOrder = Math.max(...orders);
    const minOrder = Math.min(...orders);
    
    if (minOrder < 1 || maxOrder > 100) {
      securityFlags.push('unreasonable_question_order_range');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: questions.map(q => ({
        ...q,
        questionOrder: Math.max(1, Math.min(100, q.questionOrder))
      })),
      securityFlags
    };
  }

  /**
   * Detect potential copy-paste indicators
   */
  private static detectCopyPasteIndicators(text: string): boolean {
    // Check for unusual formatting patterns
    const indicators = [
      /\u00A0/g, // Non-breaking spaces (common in copy-paste)
      /\u2018|\u2019/g, // Smart quotes
      /\u201C|\u201D/g, // Smart double quotes
      /\u2013|\u2014/g, // En/em dashes
      /\u2026/g, // Ellipsis character
      /[\u0080-\u00FF]/g, // Extended ASCII characters
    ];

    let indicatorCount = 0;
    indicators.forEach(pattern => {
      if (pattern.test(text)) {
        indicatorCount++;
      }
    });

    // Multiple indicators suggest copy-paste
    return indicatorCount >= 2;
  }

  /**
   * Sanitize response text
   */
  private static sanitizeResponseText(text: string): string {
    return text
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: URLs
      .replace(/data:/gi, '') // Remove data: URLs
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/\u00A0/g, ' ') // Replace non-breaking spaces
      .replace(/[\u0080-\u00FF]/g, '') // Remove extended ASCII
      .trim()
      .substring(0, 10000); // Limit length
  }

  /**
   * Comprehensive validation for interview response submission
   */
  static validateInterviewResponseSubmission(data: {
    responseText: string;
    userNotes?: string;
    responseTime: number;
    preparationTime: number;
    aiScore?: number;
    questionId: string;
    userId: string;
    sessionId: string;
  }): BusinessLogicValidationResult {
    const errors: string[] = [];
    const securityFlags: string[] = [];
    const sanitizedData: any = {};

    // Validate content
    const contentValidation = this.validateResponseContent({
      responseText: data.responseText,
      userNotes: data.userNotes,
      questionId: data.questionId,
      userId: data.userId,
    });

    if (!contentValidation.isValid) {
      errors.push(...contentValidation.errors);
    }
    securityFlags.push(...(contentValidation.securityFlags || []));
    Object.assign(sanitizedData, contentValidation.sanitizedData);

    // Validate time tracking
    const timeValidation = this.validateTimeTracking({
      preparationTime: data.preparationTime,
      responseTime: data.responseTime,
    });

    if (!timeValidation.isValid) {
      errors.push(...timeValidation.errors);
    }
    securityFlags.push(...(timeValidation.securityFlags || []));
    Object.assign(sanitizedData, timeValidation.sanitizedData);

    // Validate AI score if provided
    if (data.aiScore !== undefined) {
      const scoreValidation = this.validateAIScore(data.aiScore, {
        responseLength: data.responseText.length,
        responseTime: data.responseTime,
      });

      if (!scoreValidation.isValid) {
        errors.push(...scoreValidation.errors);
      }
      securityFlags.push(...(scoreValidation.securityFlags || []));
      sanitizedData.aiScore = scoreValidation.sanitizedData;
    }

    // Add session and question IDs
    sanitizedData.sessionId = data.sessionId;
    sanitizedData.questionId = data.questionId;
    sanitizedData.userId = data.userId;

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData,
      securityFlags
    };
  }
}

export default BusinessLogicSecurity;
