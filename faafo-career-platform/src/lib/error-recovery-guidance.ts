/**
 * Error Recovery Guidance System
 * Provides contextual recovery instructions for different types of errors
 */

export interface RecoveryStep {
  step: number;
  instruction: string;
  action?: 'refresh' | 'retry' | 'navigate' | 'contact' | 'wait' | 'check';
  actionLabel?: string;
  actionUrl?: string;
  technical?: boolean; // Show only to technical users
}

export interface ErrorRecoveryGuide {
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'auth' | 'network' | 'validation' | 'permission' | 'system' | 'data';
  estimatedTime: string;
  steps: RecoveryStep[];
  preventionTips?: string[];
  contactSupport?: boolean;
  escalationPath?: string;
}

/**
 * Comprehensive error recovery guidance database
 */
export class ErrorRecoveryGuidance {
  private static readonly recoveryGuides: Record<string, ErrorRecoveryGuide> = {
    // Authentication Errors
    'UNAUTHORIZED': {
      title: 'Sign In Required',
      description: 'You need to sign in to access this feature.',
      severity: 'medium',
      category: 'auth',
      estimatedTime: '1-2 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Click the "Sign In" button in the top navigation',
          action: 'navigate',
          actionLabel: 'Go to Sign In',
          actionUrl: '/login'
        },
        {
          step: 2,
          instruction: 'Enter your email and password',
          action: 'check'
        },
        {
          step: 3,
          instruction: 'If you forgot your password, click "Forgot Password"',
          action: 'navigate',
          actionLabel: 'Reset Password',
          actionUrl: '/forgot-password'
        },
        {
          step: 4,
          instruction: 'After signing in, you\'ll be redirected back to this page'
        }
      ],
      preventionTips: [
        'Keep your session active by using the application regularly',
        'Use "Remember Me" option for trusted devices',
        'Bookmark important pages after signing in'
      ]
    },

    'TOKEN_EXPIRED': {
      title: 'Session Expired',
      description: 'Your session has expired for security reasons.',
      severity: 'medium',
      category: 'auth',
      estimatedTime: '1-2 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Refresh the page to clear the expired session',
          action: 'refresh',
          actionLabel: 'Refresh Page'
        },
        {
          step: 2,
          instruction: 'Sign in again with your credentials',
          action: 'navigate',
          actionLabel: 'Sign In',
          actionUrl: '/login'
        },
        {
          step: 3,
          instruction: 'Your work may have been auto-saved - check for saved progress'
        }
      ],
      preventionTips: [
        'Save your work frequently',
        'Use the auto-save feature when available',
        'Extend your session when prompted'
      ]
    },

    'INVALID_CREDENTIALS': {
      title: 'Sign In Failed',
      description: 'The email or password you entered is incorrect.',
      severity: 'medium',
      category: 'auth',
      estimatedTime: '2-5 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Double-check your email address for typos'
        },
        {
          step: 2,
          instruction: 'Verify your password is correct (check caps lock)'
        },
        {
          step: 3,
          instruction: 'If you forgot your password, reset it',
          action: 'navigate',
          actionLabel: 'Reset Password',
          actionUrl: '/forgot-password'
        },
        {
          step: 4,
          instruction: 'Try signing in with a different method if available'
        },
        {
          step: 5,
          instruction: 'Contact support if the problem persists',
          action: 'contact',
          actionLabel: 'Contact Support'
        }
      ],
      preventionTips: [
        'Use a password manager to avoid typos',
        'Write down your password in a secure location',
        'Set up account recovery options'
      ]
    },

    // Network Errors
    'NETWORK_ERROR': {
      title: 'Connection Problem',
      description: 'We\'re having trouble connecting to our servers.',
      severity: 'medium',
      category: 'network',
      estimatedTime: '2-10 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Check your internet connection',
          action: 'check'
        },
        {
          step: 2,
          instruction: 'Try refreshing the page',
          action: 'refresh',
          actionLabel: 'Refresh Page'
        },
        {
          step: 3,
          instruction: 'Wait a few minutes and try again',
          action: 'wait'
        },
        {
          step: 4,
          instruction: 'Try using a different browser or device'
        },
        {
          step: 5,
          instruction: 'Check if other websites are working'
        },
        {
          step: 6,
          instruction: 'Contact your internet service provider if needed'
        }
      ],
      preventionTips: [
        'Use a stable internet connection',
        'Keep your browser updated',
        'Clear browser cache regularly'
      ]
    },

    'TIMEOUT': {
      title: 'Request Timeout',
      description: 'The operation is taking longer than expected.',
      severity: 'medium',
      category: 'network',
      estimatedTime: '1-5 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Wait for the operation to complete (it may still be processing)'
        },
        {
          step: 2,
          instruction: 'If nothing happens after 2 minutes, try again',
          action: 'retry',
          actionLabel: 'Try Again'
        },
        {
          step: 3,
          instruction: 'Check your internet connection speed'
        },
        {
          step: 4,
          instruction: 'Try during off-peak hours if the service is busy'
        }
      ],
      preventionTips: [
        'Use a faster internet connection for large operations',
        'Avoid peak usage times',
        'Break large tasks into smaller parts'
      ]
    },

    // Validation Errors
    'VALIDATION_ERROR': {
      title: 'Invalid Information',
      description: 'Some of the information you provided is not valid.',
      severity: 'low',
      category: 'validation',
      estimatedTime: '1-3 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Look for red error messages near form fields'
        },
        {
          step: 2,
          instruction: 'Fix any highlighted fields with invalid data'
        },
        {
          step: 3,
          instruction: 'Make sure all required fields are filled'
        },
        {
          step: 4,
          instruction: 'Check that email addresses and phone numbers are in the correct format'
        },
        {
          step: 5,
          instruction: 'Try submitting the form again',
          action: 'retry',
          actionLabel: 'Submit Again'
        }
      ],
      preventionTips: [
        'Double-check your information before submitting',
        'Use the suggested formats for dates and phone numbers',
        'Save drafts frequently to avoid losing work'
      ]
    },

    // Permission Errors
    'FORBIDDEN': {
      title: 'Access Denied',
      description: 'You don\'t have permission to access this feature.',
      severity: 'medium',
      category: 'permission',
      estimatedTime: '5-15 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Make sure you\'re signed in to the correct account'
        },
        {
          step: 2,
          instruction: 'Check if your account has the necessary permissions'
        },
        {
          step: 3,
          instruction: 'Contact your administrator to request access',
          action: 'contact',
          actionLabel: 'Contact Admin'
        },
        {
          step: 4,
          instruction: 'Try accessing a different part of the application'
        }
      ],
      preventionTips: [
        'Understand your account permissions',
        'Request necessary access in advance',
        'Use the correct account for different tasks'
      ],
      contactSupport: true
    },

    // System Errors
    '500': {
      title: 'Server Error',
      description: 'Something went wrong on our end. We\'re working to fix this.',
      severity: 'high',
      category: 'system',
      estimatedTime: '5-30 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Wait a few minutes - this is usually temporary'
        },
        {
          step: 2,
          instruction: 'Try refreshing the page',
          action: 'refresh',
          actionLabel: 'Refresh Page'
        },
        {
          step: 3,
          instruction: 'Try the action again in a few minutes',
          action: 'retry',
          actionLabel: 'Try Again'
        },
        {
          step: 4,
          instruction: 'Check our status page for known issues',
          action: 'navigate',
          actionLabel: 'Check Status',
          actionUrl: '/status'
        },
        {
          step: 5,
          instruction: 'Report the issue if it persists',
          action: 'contact',
          actionLabel: 'Report Issue'
        }
      ],
      preventionTips: [
        'Save your work frequently',
        'Try during off-peak hours',
        'Keep important data backed up'
      ],
      contactSupport: true,
      escalationPath: 'If the error persists for more than 30 minutes, contact technical support with the error details.'
    },

    'DATABASE_ERROR': {
      title: 'Data Error',
      description: 'We\'re having trouble accessing your data right now.',
      severity: 'high',
      category: 'system',
      estimatedTime: '10-60 minutes',
      steps: [
        {
          step: 1,
          instruction: 'Don\'t worry - your data is safe and will be restored'
        },
        {
          step: 2,
          instruction: 'Wait 10-15 minutes before trying again'
        },
        {
          step: 3,
          instruction: 'Try refreshing the page',
          action: 'refresh',
          actionLabel: 'Refresh Page'
        },
        {
          step: 4,
          instruction: 'Check if other features are working'
        },
        {
          step: 5,
          instruction: 'Contact support if the issue persists',
          action: 'contact',
          actionLabel: 'Contact Support'
        }
      ],
      preventionTips: [
        'Export important data regularly',
        'Use the auto-save features',
        'Keep local copies of critical information'
      ],
      contactSupport: true,
      escalationPath: 'If data access is not restored within 1 hour, escalate to technical support immediately.'
    }
  };

  /**
   * Get recovery guidance for a specific error
   */
  static getGuidance(errorCode: string, context?: string): ErrorRecoveryGuide | null {
    // Try exact match first
    if (this.recoveryGuides[errorCode]) {
      return this.customizeForContext(this.recoveryGuides[errorCode], context);
    }

    // Try pattern matching for HTTP status codes
    if (/^[45]\d{2}$/.test(errorCode)) {
      const statusCode = errorCode;
      if (this.recoveryGuides[statusCode]) {
        return this.customizeForContext(this.recoveryGuides[statusCode], context);
      }
    }

    // Try fuzzy matching for common patterns
    const lowerError = errorCode.toLowerCase();
    
    if (lowerError.includes('unauthorized') || lowerError.includes('401')) {
      return this.customizeForContext(this.recoveryGuides['UNAUTHORIZED'], context);
    }
    
    if (lowerError.includes('forbidden') || lowerError.includes('403')) {
      return this.customizeForContext(this.recoveryGuides['FORBIDDEN'], context);
    }
    
    if (lowerError.includes('network') || lowerError.includes('connection')) {
      return this.customizeForContext(this.recoveryGuides['NETWORK_ERROR'], context);
    }
    
    if (lowerError.includes('timeout')) {
      return this.customizeForContext(this.recoveryGuides['TIMEOUT'], context);
    }
    
    if (lowerError.includes('validation') || lowerError.includes('invalid')) {
      return this.customizeForContext(this.recoveryGuides['VALIDATION_ERROR'], context);
    }

    return null;
  }

  /**
   * Customize guidance based on context
   */
  private static customizeForContext(guide: ErrorRecoveryGuide, context?: string): ErrorRecoveryGuide {
    if (!context) return guide;

    const customized = { ...guide };

    // Customize based on context
    switch (context) {
      case 'assessment':
        customized.preventionTips = [
          ...(customized.preventionTips || []),
          'Save your assessment progress frequently',
          'Complete assessments in one session when possible'
        ];
        break;
        
      case 'interview':
        customized.preventionTips = [
          ...(customized.preventionTips || []),
          'Test your microphone and camera before starting',
          'Use a stable internet connection for interview practice'
        ];
        break;
        
      case 'resume':
        customized.preventionTips = [
          ...(customized.preventionTips || []),
          'Export your resume regularly as backup',
          'Keep a local copy of your resume content'
        ];
        break;
    }

    return customized;
  }

  /**
   * Get all available recovery guides
   */
  static getAllGuides(): Record<string, ErrorRecoveryGuide> {
    return { ...this.recoveryGuides };
  }

  /**
   * Get guides by category
   */
  static getGuidesByCategory(category: string): ErrorRecoveryGuide[] {
    return Object.values(this.recoveryGuides).filter(guide => guide.category === category);
  }

  /**
   * Get guides by severity
   */
  static getGuidesBySeverity(severity: string): ErrorRecoveryGuide[] {
    return Object.values(this.recoveryGuides).filter(guide => guide.severity === severity);
  }
}
