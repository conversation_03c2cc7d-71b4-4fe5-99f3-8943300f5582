/**
 * Feature Flag Provider
 * Core feature flag management system with A/B testing and gradual rollout capabilities
 */

import { CacheService } from '@/lib/services/cacheService';

interface FlagMetadata {
  emergency?: boolean;
  disabledAt?: string;
  rollback?: boolean;
  timestamp?: string;
  description?: string;
  owner?: string;
  experiment?: boolean;
}

interface FlagValue {
  value: boolean | string | number;
  metadata?: FlagMetadata;
}

interface UserContext {
  userTier?: string;
  skillLevel?: string;
  careerPath?: string;
  organizationId?: string;
  experimentsOptIn?: boolean;
  [key: string]: any;
}

interface ABTestConfig {
  variants: string[];
  weights?: number[];
  enabled: boolean;
  startDate?: Date;
  endDate?: Date;
}

export class FeatureFlagProvider {
  private cacheService: CacheService;
  private flags: Map<string, FlagValue> = new Map();
  private userContexts: Map<string, UserContext> = new Map();
  private abTests: Map<string, ABTestConfig> = new Map();
  private flagUsageStats: Map<string, { count: number; lastUsed: Date }> = new Map();

  constructor() {
    this.cacheService = new CacheService();
    this.initializeDefaultFlags();
    this.initializeABTests();
  }

  /**
   * Get a feature flag value
   */
  async getFlag(flagName: string): Promise<FlagValue | null> {
    try {
      // Check cache first
      const cacheKey = `feature_flag:${flagName}`;
      const cachedFlag = await this.cacheService.getJSON(cacheKey);
      if (cachedFlag) {
        return cachedFlag;
      }

      // Get from in-memory store
      const flag = this.flags.get(flagName);
      if (flag) {
        // Cache for 5 minutes
        await this.cacheService.setJSON(cacheKey, flag, 300);
        return flag;
      }

      return null;
    } catch (error) {
      console.error(`Error getting flag ${flagName}:`, error);
      return null;
    }
  }

  /**
   * Set a feature flag value
   */
  async setFlag(flagName: string, value: boolean | string | number, metadata?: FlagMetadata): Promise<boolean> {
    try {
      const flagValue: FlagValue = {
        value,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
        },
      };

      // Update in-memory store
      this.flags.set(flagName, flagValue);

      // Update cache
      const cacheKey = `feature_flag:${flagName}`;
      await this.cacheService.setJSON(cacheKey, flagValue, 300);

      return true;
    } catch (error) {
      console.error(`Error setting flag ${flagName}:`, error);
      return false;
    }
  }

  /**
   * Get all flags for a user
   */
  async getAllFlags(userId: string, flagNames?: string[]): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    const flagsToGet = flagNames || Array.from(this.flags.keys());

    for (const flagName of flagsToGet) {
      try {
        const value = await this.evaluateFlag(flagName, userId, false);
        result[flagName] = value;
      } catch (error) {
        console.error(`Error evaluating flag ${flagName} for user ${userId}:`, error);
        result[flagName] = false; // Safe default
      }
    }

    return result;
  }

  /**
   * Evaluate a feature flag for a specific user
   */
  async evaluateFlag(
    flagName: string,
    userId: string,
    defaultValue: boolean | string | number,
    context?: Record<string, any>
  ): Promise<any> {
    try {
      const flag = await this.getFlag(flagName);
      if (!flag) {
        return defaultValue;
      }

      // Check if flag is in emergency mode
      if (flag.metadata?.emergency) {
        return flag.value;
      }

      // Get user context
      const userContext = this.userContexts.get(userId) || {};
      const combinedContext = { ...userContext, ...context };

      // Apply user-specific logic
      const evaluatedValue = this.applyUserLogic(flag.value, userId, combinedContext);

      // Track usage
      await this.trackFlagUsage(userId, flagName, evaluatedValue, {
        source: 'evaluation',
        timestamp: Date.now(),
      });

      return evaluatedValue;
    } catch (error) {
      console.error(`Error evaluating flag ${flagName} for user ${userId}:`, error);
      return defaultValue;
    }
  }

  /**
   * Get A/B test variant for a user
   */
  async getABTestVariant(
    testName: string,
    userId: string,
    variants: string[],
    defaultVariant: string
  ): Promise<string> {
    try {
      const testConfig = this.abTests.get(testName);
      if (!testConfig || !testConfig.enabled) {
        return defaultVariant;
      }

      // Check if test is within date range
      const now = new Date();
      if (testConfig.startDate && now < testConfig.startDate) {
        return defaultVariant;
      }
      if (testConfig.endDate && now > testConfig.endDate) {
        return defaultVariant;
      }

      // Use consistent hashing to assign variant
      const userHash = this.hashUserId(userId);
      const variantIndex = userHash % variants.length;
      const selectedVariant = variants[variantIndex];

      // Track A/B test participation
      await this.trackFlagUsage(userId, `ab_test:${testName}`, selectedVariant, {
        source: 'ab_test',
        variants,
        timestamp: Date.now(),
      });

      return selectedVariant;
    } catch (error) {
      console.error(`Error getting A/B test variant for ${testName}:`, error);
      return defaultVariant;
    }
  }

  /**
   * Get user segment for targeting
   */
  async getUserSegment(userId: string): Promise<string> {
    const userContext = this.userContexts.get(userId);
    if (!userContext) {
      return 'default';
    }

    // Determine segment based on user context
    if (userContext.userTier === 'premium') {
      return 'premium';
    }
    if (userContext.organizationId) {
      return 'enterprise';
    }
    if (userContext.experimentsOptIn) {
      return 'beta';
    }

    return 'default';
  }

  /**
   * Update user context for better targeting
   */
  async updateUserContext(userId: string, context: UserContext): Promise<void> {
    try {
      const existingContext = this.userContexts.get(userId) || {};
      const updatedContext = { ...existingContext, ...context };
      
      this.userContexts.set(userId, updatedContext);

      // Cache user context
      const cacheKey = `user_context:${userId}`;
      await this.cacheService.setJSON(cacheKey, updatedContext, 3600); // 1 hour
    } catch (error) {
      console.error(`Error updating user context for ${userId}:`, error);
    }
  }

  /**
   * Track feature flag usage for analytics
   */
  async trackFlagUsage(
    userId: string,
    flagName: string,
    value: any,
    context?: Record<string, any>
  ): Promise<void> {
    try {
      // Update usage stats
      const stats = this.flagUsageStats.get(flagName) || { count: 0, lastUsed: new Date() };
      stats.count++;
      stats.lastUsed = new Date();
      this.flagUsageStats.set(flagName, stats);

      // Log usage for analytics (in production, this would go to analytics service)
      const usageData = {
        userId,
        flagName,
        value,
        timestamp: new Date().toISOString(),
        context,
      };

      // Cache usage data for analytics
      const cacheKey = `flag_usage:${flagName}:${userId}:${Date.now()}`;
      await this.cacheService.setJSON(cacheKey, usageData, 86400); // 24 hours
    } catch (error) {
      console.error(`Error tracking flag usage for ${flagName}:`, error);
    }
  }

  // Private helper methods

  private initializeDefaultFlags(): void {
    // Skill Gap Analyzer flags
    this.flags.set('skill_gap_analyzer_enabled', { value: true });
    this.flags.set('comprehensive_analysis_enabled', { value: false });
    this.flags.set('ai_recommendations_enabled', { value: true });
    this.flags.set('market_data_integration', { value: false });
    this.flags.set('enhanced_ui_v3', { value: false });
    this.flags.set('interactive_tutorials', { value: true });
    this.flags.set('realtime_collaboration', { value: false });
    this.flags.set('beta_features_enabled', { value: false });
    this.flags.set('enterprise_analytics', { value: false });
    this.flags.set('caching_optimization_v2', { value: false });
    this.flags.set('parallel_processing_enabled', { value: false });
    this.flags.set('advanced_ai_models', { value: false });
    this.flags.set('new_skill_matching_algorithm', { value: false });
  }

  private initializeABTests(): void {
    // A/B test configurations
    this.abTests.set('skill_assessment_ui_v2', {
      variants: ['control', 'variant_a', 'variant_b'],
      enabled: true,
    });

    this.abTests.set('analysis_algorithm_v3', {
      variants: ['standard', 'enhanced_ai', 'hybrid'],
      enabled: true,
    });

    this.abTests.set('recommendation_engine_v2', {
      variants: ['basic', 'ml_enhanced', 'collaborative'],
      enabled: true,
    });
  }

  private applyUserLogic(
    flagValue: boolean | string | number,
    userId: string,
    context: UserContext
  ): any {
    // For boolean flags, apply percentage rollout logic
    if (typeof flagValue === 'boolean') {
      // Special handling for gradual rollout flags
      if (flagValue === false) {
        // Check if user should be in rollout based on user ID hash
        const userHash = this.hashUserId(userId);
        
        // Example: 25% rollout for new_skill_matching_algorithm
        if (context.flagName === 'new_skill_matching_algorithm') {
          return userHash % 100 < 25;
        }
        
        // Premium users get beta features
        if (context.userTier === 'premium' && context.flagName === 'beta_features_enabled') {
          return true;
        }
      }
    }

    return flagValue;
  }

  private hashUserId(userId: string): number {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}
