/**
 * Unified Validation Service
 * 
 * Consolidates all validation logic to eliminate semantic duplicates
 * and provide consistent validation across frontend and backend.
 */

import { z } from 'zod';

export interface ValidationResult<T = any> {
  isValid: boolean;
  sanitizedData?: T;
  errors: string[];
  securityFlags: string[];
}

export interface InterviewResponseData {
  responseText: string;
  responseTime: number;
  preparationTime: number;
  userNotes?: string;
}

export interface InterviewSessionConfig {
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  industryFocus?: string;
  specificRole?: string;
  interviewType?: string;
  preparationTime?: string;
  focusAreas?: string[];
  difficulty: string;
  totalQuestions: number;
}

export class UnifiedValidationService {
  // Common validation schemas
  private static readonly sessionConfigSchema = z.object({
    sessionType: z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),
    careerPath: z.string().optional(),
    experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
    companyType: z.string().optional(),
    industryFocus: z.string().optional(),
    specificRole: z.string().optional(),
    interviewType: z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),
    preparationTime: z.string().optional(),
    focusAreas: z.array(z.string()).optional(),
    difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),
    totalQuestions: z.number().min(1).max(50).default(10),
  });

  private static readonly responseSchema = z.object({
    responseText: z.string().min(10).max(5000),
    responseTime: z.number().min(0).max(3600),
    preparationTime: z.number().min(0).max(1800),
    userNotes: z.string().max(1000).optional(),
  });

  // Security patterns to detect
  private static readonly securityPatterns = [
    { pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, flag: 'XSS_SCRIPT_TAG' },
    { pattern: /javascript:/gi, flag: 'JAVASCRIPT_PROTOCOL' },
    { pattern: /on\w+\s*=/gi, flag: 'EVENT_HANDLER' },
    { pattern: /\b(eval|setTimeout|setInterval)\s*\(/gi, flag: 'DANGEROUS_FUNCTION' },
    { pattern: /\b(union|select|insert|update|delete|drop|create|alter)\b/gi, flag: 'SQL_INJECTION' },
    { pattern: /\.\.\//g, flag: 'PATH_TRAVERSAL' },
    { pattern: /<iframe\b[^>]*>/gi, flag: 'IFRAME_INJECTION' },
  ];

  /**
   * Validate interview session configuration
   */
  static validateSessionConfig(data: any): ValidationResult<InterviewSessionConfig> {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    try {
      // Schema validation
      const validation = this.sessionConfigSchema.safeParse(data);
      
      if (!validation.success) {
        validation.error.errors.forEach(err => {
          errors.push(`${err.path.join('.')}: ${err.message}`);
        });
        return { isValid: false, errors, securityFlags };
      }

      const sanitizedData = validation.data;

      // Security validation
      const stringFields = ['careerPath', 'companyType', 'industryFocus', 'specificRole', 'preparationTime'];
      stringFields.forEach(field => {
        const value = sanitizedData[field as keyof InterviewSessionConfig];
        if (typeof value === 'string') {
          const flags = this.detectSecurityThreats(value);
          securityFlags.push(...flags);
        }
      });

      // Focus areas security check
      if (sanitizedData.focusAreas) {
        sanitizedData.focusAreas.forEach(area => {
          const flags = this.detectSecurityThreats(area);
          securityFlags.push(...flags);
        });
      }

      // Business logic validation
      if (sanitizedData.totalQuestions > 20 && sanitizedData.sessionType === 'QUICK_PRACTICE') {
        errors.push('Quick practice sessions should have 20 or fewer questions');
      }

      if (securityFlags.length > 0) {
        errors.push('Security threats detected in input data');
        return { isValid: false, errors, securityFlags };
      }

      return {
        isValid: true,
        sanitizedData,
        errors: [],
        securityFlags: []
      };

    } catch (error) {
      errors.push('Validation error occurred');
      return { isValid: false, errors, securityFlags };
    }
  }

  /**
   * Validate interview response data
   */
  static validateInterviewResponse(data: any): ValidationResult<InterviewResponseData> {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    try {
      // Schema validation
      const validation = this.responseSchema.safeParse(data);
      
      if (!validation.success) {
        validation.error.errors.forEach(err => {
          errors.push(`${err.path.join('.')}: ${err.message}`);
        });
        return { isValid: false, errors, securityFlags };
      }

      const sanitizedData = validation.data;

      // Security validation
      const responseFlags = this.detectSecurityThreats(sanitizedData.responseText);
      securityFlags.push(...responseFlags);

      if (sanitizedData.userNotes) {
        const notesFlags = this.detectSecurityThreats(sanitizedData.userNotes);
        securityFlags.push(...notesFlags);
      }

      // Business logic validation
      if (sanitizedData.responseTime > sanitizedData.preparationTime + 1800) {
        errors.push('Response time seems unreasonably long');
      }

      if (sanitizedData.responseText.length < 20) {
        errors.push('Response is too short to provide meaningful analysis');
      }

      // Content quality checks
      const wordCount = sanitizedData.responseText.split(/\s+/).length;
      if (wordCount < 5) {
        errors.push('Response must contain at least 5 words');
      }

      if (securityFlags.length > 0) {
        errors.push('Security threats detected in response content');
        return { isValid: false, errors, securityFlags };
      }

      return {
        isValid: true,
        sanitizedData: {
          ...sanitizedData,
          responseText: this.sanitizeText(sanitizedData.responseText),
          userNotes: sanitizedData.userNotes ? this.sanitizeText(sanitizedData.userNotes) : undefined
        },
        errors: [],
        securityFlags: []
      };

    } catch (error) {
      errors.push('Validation error occurred');
      return { isValid: false, errors, securityFlags };
    }
  }

  /**
   * Validate AI score
   */
  static validateAIScore(score: any, context: { responseLength: number; responseTime: number; questionType: string }): ValidationResult<number> {
    const errors: string[] = [];
    const securityFlags: string[] = [];

    // Type validation
    if (typeof score !== 'number' || isNaN(score)) {
      errors.push('AI score must be a valid number');
      return { isValid: false, errors, securityFlags };
    }

    // Range validation
    if (score < 0 || score > 10) {
      errors.push('AI score must be between 0 and 10');
      return { isValid: false, errors, securityFlags };
    }

    // Business logic validation
    if (context.responseLength < 20 && score > 7) {
      errors.push('High score inconsistent with short response length');
      securityFlags.push('SUSPICIOUS_SCORE_LENGTH_MISMATCH');
    }

    if (context.responseTime < 30 && score > 8) {
      errors.push('High score inconsistent with very short response time');
      securityFlags.push('SUSPICIOUS_SCORE_TIME_MISMATCH');
    }

    // Round to 1 decimal place
    const sanitizedScore = Math.round(score * 10) / 10;

    return {
      isValid: errors.length === 0,
      sanitizedData: sanitizedScore,
      errors,
      securityFlags
    };
  }

  /**
   * Detect security threats in text input
   */
  private static detectSecurityThreats(text: string): string[] {
    const flags: string[] = [];
    
    this.securityPatterns.forEach(({ pattern, flag }) => {
      if (pattern.test(text)) {
        flags.push(flag);
      }
    });

    return flags;
  }

  /**
   * Sanitize text input
   */
  private static sanitizeText(text: string): string {
    return text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .replace(/<iframe\b[^>]*>/gi, '')
      .trim();
  }

  /**
   * Validate question type enum
   */
  static validateQuestionType(type: string): string {
    const validTypes = ['BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC'];
    if (validTypes.includes(type)) return type;

    // Map common invalid values
    const typeMapping: { [key: string]: string } = {
      'TECHNICAL_SKILLS': 'TECHNICAL',
      'SOFT_SKILLS': 'BEHAVIORAL',
      'TEAMWORK': 'BEHAVIORAL',
      'ADAPTABILITY': 'BEHAVIORAL',
      'GENERAL': 'BEHAVIORAL'
    };

    return typeMapping[type] || 'BEHAVIORAL';
  }

  /**
   * Validate category enum
   */
  static validateCategory(category: string): string {
    const validCategories = ['GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE'];
    return validCategories.includes(category) ? category : 'GENERAL';
  }

  /**
   * Validate difficulty enum
   */
  static validateDifficulty(difficulty: string): string {
    const validDifficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
    return validDifficulties.includes(difficulty) ? difficulty : 'INTERMEDIATE';
  }
}

export default UnifiedValidationService;
