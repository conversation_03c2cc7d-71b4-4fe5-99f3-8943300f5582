/**
 * Session Activity Tracker
 * Comprehensive user activity tracking to prevent premature session timeouts
 */

export interface ActivityEvent {
  type: 'mouse' | 'keyboard' | 'scroll' | 'touch' | 'focus' | 'api' | 'form' | 'navigation';
  timestamp: number;
  details?: {
    element?: string;
    action?: string;
    page?: string;
    duration?: number;
  };
}

export interface ActivityMetrics {
  totalEvents: number;
  lastActivity: number;
  activeDuration: number;
  idleDuration: number;
  eventsByType: Record<string, number>;
  pagesVisited: string[];
  formsInteracted: string[];
  apiCallsMade: number;
}

export interface ActivityTrackerConfig {
  trackMouse: boolean;
  trackKeyboard: boolean;
  trackScroll: boolean;
  trackTouch: boolean;
  trackFocus: boolean;
  trackApiCalls: boolean;
  trackFormInteractions: boolean;
  trackNavigation: boolean;
  idleThreshold: number; // Time in ms to consider user idle
  throttleInterval: number; // Throttle events to prevent spam
  maxEvents: number; // Maximum events to store
  persistActivity: boolean; // Store activity in localStorage
}

export interface ActivityTrackerCallbacks {
  onActivity?: (event: ActivityEvent) => void;
  onIdle?: (idleDuration: number) => void;
  onActive?: () => void;
  onMetricsUpdate?: (metrics: ActivityMetrics) => void;
}

/**
 * Default activity tracker configuration
 */
export const DEFAULT_ACTIVITY_CONFIG: ActivityTrackerConfig = {
  trackMouse: true,
  trackKeyboard: true,
  trackScroll: true,
  trackTouch: true,
  trackFocus: true,
  trackApiCalls: true,
  trackFormInteractions: true,
  trackNavigation: true,
  idleThreshold: 5 * 60 * 1000, // 5 minutes
  throttleInterval: 1000, // 1 second
  maxEvents: 1000,
  persistActivity: true
};

/**
 * Session Activity Tracker Class
 */
export class SessionActivityTracker {
  private config: ActivityTrackerConfig;
  private callbacks: ActivityTrackerCallbacks;
  private events: ActivityEvent[] = [];
  private metrics: ActivityMetrics;
  private isIdle = false;
  private idleTimer: NodeJS.Timeout | null = null;
  private lastEventTime = 0;
  private listeners: Set<(metrics: ActivityMetrics) => void> = new Set();
  private sessionStartTime = Date.now();

  constructor(config: Partial<ActivityTrackerConfig> = {}, callbacks: ActivityTrackerCallbacks = {}) {
    this.config = { ...DEFAULT_ACTIVITY_CONFIG, ...config };
    this.callbacks = callbacks;
    
    this.metrics = {
      totalEvents: 0,
      lastActivity: Date.now(),
      activeDuration: 0,
      idleDuration: 0,
      eventsByType: {},
      pagesVisited: [window.location.pathname],
      formsInteracted: [],
      apiCallsMade: 0
    };

    this.setupEventListeners();
    this.restoreActivity();
    this.startIdleTimer();
  }

  /**
   * Get current activity metrics
   */
  getMetrics(): ActivityMetrics {
    return { ...this.metrics };
  }

  /**
   * Subscribe to metrics updates
   */
  subscribe(listener: (metrics: ActivityMetrics) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Record an activity event
   */
  recordActivity(type: ActivityEvent['type'], details?: ActivityEvent['details']) {
    const now = Date.now();
    
    // Throttle events
    if (now - this.lastEventTime < this.config.throttleInterval) {
      return;
    }

    const event: ActivityEvent = {
      type,
      timestamp: now,
      details
    };

    // Add event to history
    this.events.push(event);
    
    // Limit event history
    if (this.events.length > this.config.maxEvents) {
      this.events = this.events.slice(-this.config.maxEvents);
    }

    // Update metrics
    this.updateMetrics(event);
    
    // Reset idle state
    if (this.isIdle) {
      this.isIdle = false;
      this.callbacks.onActive?.();
    }
    
    // Reset idle timer
    this.resetIdleTimer();
    
    // Trigger callbacks
    this.callbacks.onActivity?.(event);
    this.notifyListeners();
    
    this.lastEventTime = now;
    
    // Persist activity if enabled
    if (this.config.persistActivity) {
      this.persistActivity();
    }
  }

  /**
   * Update activity metrics
   */
  private updateMetrics(event: ActivityEvent) {
    this.metrics.totalEvents++;
    this.metrics.lastActivity = event.timestamp;
    
    // Update event type counts
    this.metrics.eventsByType[event.type] = (this.metrics.eventsByType[event.type] || 0) + 1;
    
    // Update active duration
    const sessionDuration = event.timestamp - this.sessionStartTime;
    this.metrics.activeDuration = sessionDuration - this.metrics.idleDuration;
    
    // Track page visits
    if (event.type === 'navigation' && event.details?.page) {
      if (!this.metrics.pagesVisited.includes(event.details.page)) {
        this.metrics.pagesVisited.push(event.details.page);
      }
    }
    
    // Track form interactions
    if (event.type === 'form' && event.details?.element) {
      if (!this.metrics.formsInteracted.includes(event.details.element)) {
        this.metrics.formsInteracted.push(event.details.element);
      }
    }
    
    // Track API calls
    if (event.type === 'api') {
      this.metrics.apiCallsMade++;
    }
  }

  /**
   * Setup event listeners for activity tracking
   */
  private setupEventListeners() {
    if (typeof window === 'undefined') return;

    // Mouse events
    if (this.config.trackMouse) {
      ['mousedown', 'mousemove', 'click'].forEach(event => {
        document.addEventListener(event, (e) => {
          this.recordActivity('mouse', {
            action: event,
            element: (e.target as Element)?.tagName?.toLowerCase()
          });
        }, { passive: true });
      });
    }

    // Keyboard events
    if (this.config.trackKeyboard) {
      ['keydown', 'keypress'].forEach(event => {
        document.addEventListener(event, (e) => {
          this.recordActivity('keyboard', {
            action: event,
            element: (e.target as Element)?.tagName?.toLowerCase()
          });
        }, { passive: true });
      });
    }

    // Scroll events
    if (this.config.trackScroll) {
      document.addEventListener('scroll', () => {
        this.recordActivity('scroll', {
          action: 'scroll',
          element: 'window'
        });
      }, { passive: true });
    }

    // Touch events
    if (this.config.trackTouch) {
      ['touchstart', 'touchmove', 'touchend'].forEach(event => {
        document.addEventListener(event, (e) => {
          this.recordActivity('touch', {
            action: event,
            element: (e.target as Element)?.tagName?.toLowerCase()
          });
        }, { passive: true });
      });
    }

    // Focus events
    if (this.config.trackFocus) {
      ['focus', 'blur'].forEach(event => {
        window.addEventListener(event, () => {
          this.recordActivity('focus', {
            action: event,
            element: 'window'
          });
        });
      });
    }

    // Form interactions
    if (this.config.trackFormInteractions) {
      ['input', 'change', 'submit'].forEach(event => {
        document.addEventListener(event, (e) => {
          const target = e.target as HTMLElement;
          if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT' || target.tagName === 'FORM') {
            const formElement = target as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | HTMLFormElement;
            this.recordActivity('form', {
              action: event,
              element: target.id || (formElement as any).name || target.tagName.toLowerCase()
            });
          }
        }, { passive: true });
      });
    }

    // Navigation tracking
    if (this.config.trackNavigation) {
      // Track page changes
      let currentPage = window.location.pathname;
      const checkPageChange = () => {
        if (window.location.pathname !== currentPage) {
          currentPage = window.location.pathname;
          this.recordActivity('navigation', {
            action: 'page_change',
            page: currentPage
          });
        }
      };

      // Check for page changes periodically
      setInterval(checkPageChange, 1000);

      // Track popstate events
      window.addEventListener('popstate', () => {
        this.recordActivity('navigation', {
          action: 'popstate',
          page: window.location.pathname
        });
      });
    }

    // API call tracking (if enabled)
    if (this.config.trackApiCalls) {
      this.setupApiTracking();
    }
  }

  /**
   * Setup API call tracking
   */
  private setupApiTracking() {
    // Intercept fetch calls
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      this.recordActivity('api', {
        action: 'fetch',
        element: args[0]?.toString()
      });
      return originalFetch.apply(window, args);
    };

    // Intercept XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, async?: boolean, username?: string | null, password?: string | null) {
      this.addEventListener('loadstart', () => {
        (window as any).sessionActivityTracker?.recordActivity('api', {
          action: 'xhr',
          element: url?.toString()
        });
      });
      return originalXHROpen.call(this, method, url, async ?? true, username, password);
    };
  }

  /**
   * Start idle timer
   */
  private startIdleTimer() {
    this.resetIdleTimer();
  }

  /**
   * Reset idle timer
   */
  private resetIdleTimer() {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }

    this.idleTimer = setTimeout(() => {
      this.handleIdle();
    }, this.config.idleThreshold);
  }

  /**
   * Handle idle state
   */
  private handleIdle() {
    if (!this.isIdle) {
      this.isIdle = true;
      const idleStart = Date.now();
      this.callbacks.onIdle?.(idleStart - this.metrics.lastActivity);
      
      // Update idle duration
      this.metrics.idleDuration += idleStart - this.metrics.lastActivity;
      this.notifyListeners();
    }
  }

  /**
   * Check if user is currently active
   */
  isUserActive(): boolean {
    const timeSinceLastActivity = Date.now() - this.metrics.lastActivity;
    return timeSinceLastActivity < this.config.idleThreshold;
  }

  /**
   * Get time since last activity
   */
  getTimeSinceLastActivity(): number {
    return Date.now() - this.metrics.lastActivity;
  }

  /**
   * Get activity score (0-100)
   */
  getActivityScore(): number {
    const sessionDuration = Date.now() - this.sessionStartTime;
    const activeRatio = this.metrics.activeDuration / sessionDuration;
    const eventDensity = this.metrics.totalEvents / (sessionDuration / 60000); // Events per minute
    
    // Combine active ratio and event density for score
    const score = Math.min(100, (activeRatio * 70) + (Math.min(eventDensity, 10) * 3));
    return Math.round(score);
  }

  /**
   * Persist activity to localStorage
   */
  private persistActivity() {
    try {
      const data = {
        metrics: this.metrics,
        sessionStartTime: this.sessionStartTime,
        lastUpdate: Date.now()
      };
      localStorage.setItem('session_activity', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to persist activity data:', error);
    }
  }

  /**
   * Restore activity from localStorage
   */
  private restoreActivity() {
    if (!this.config.persistActivity) return;

    try {
      const data = localStorage.getItem('session_activity');
      if (data) {
        const parsed = JSON.parse(data);
        
        // Only restore if from recent session (within 1 hour)
        if (Date.now() - parsed.lastUpdate < 60 * 60 * 1000) {
          this.metrics = { ...this.metrics, ...parsed.metrics };
          this.sessionStartTime = parsed.sessionStartTime;
        }
      }
    } catch (error) {
      console.error('Failed to restore activity data:', error);
    }
  }

  /**
   * Notify all listeners
   */
  private notifyListeners() {
    this.callbacks.onMetricsUpdate?.(this.metrics);
    this.listeners.forEach(listener => {
      try {
        listener(this.metrics);
      } catch (error) {
        console.error('Error in activity tracker listener:', error);
      }
    });
  }

  /**
   * Stop tracking and cleanup
   */
  stop() {
    if (this.idleTimer) {
      clearTimeout(this.idleTimer);
    }
    
    // Remove event listeners would require storing references
    // For now, just clear the timer
    
    if (this.config.persistActivity) {
      this.persistActivity();
    }
  }

  /**
   * Reset all activity data
   */
  reset() {
    this.events = [];
    this.metrics = {
      totalEvents: 0,
      lastActivity: Date.now(),
      activeDuration: 0,
      idleDuration: 0,
      eventsByType: {},
      pagesVisited: [window.location.pathname],
      formsInteracted: [],
      apiCallsMade: 0
    };
    this.sessionStartTime = Date.now();
    this.isIdle = false;
    this.resetIdleTimer();
    this.notifyListeners();
  }
}

/**
 * Global activity tracker instance
 */
let globalActivityTracker: SessionActivityTracker | null = null;

/**
 * Get or create global activity tracker
 */
export function getSessionActivityTracker(
  config?: Partial<ActivityTrackerConfig>,
  callbacks?: ActivityTrackerCallbacks
): SessionActivityTracker {
  if (!globalActivityTracker) {
    globalActivityTracker = new SessionActivityTracker(config, callbacks);
    
    // Make it available globally for API tracking
    (window as any).sessionActivityTracker = globalActivityTracker;
  }
  return globalActivityTracker;
}

/**
 * Reset global activity tracker
 */
export function resetSessionActivityTracker() {
  if (globalActivityTracker) {
    globalActivityTracker.stop();
    globalActivityTracker = null;
    delete (window as any).sessionActivityTracker;
  }
}
