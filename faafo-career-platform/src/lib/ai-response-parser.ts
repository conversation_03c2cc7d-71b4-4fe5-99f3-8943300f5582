/**
 * Robust AI response parsing and validation
 * Handles edge cases, malformed responses, and ensures data integrity
 */

interface ParseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  warnings: string[];
  metadata: {
    parseMethod: string;
    originalLength: number;
    parsedLength: number;
    confidence: number;
  };
}

interface ValidationSchema {
  type: 'object' | 'array' | 'string' | 'number' | 'boolean';
  required?: string[];
  properties?: Record<string, ValidationSchema>;
  items?: ValidationSchema;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: any[];
}

export class AIResponseParser {
  /**
   * Parse AI response with multiple fallback strategies
   */
  static parseResponse<T>(
    responseText: string,
    schema?: ValidationSchema,
    fallbackData?: T
  ): ParseResult<T> {
    const warnings: string[] = [];
    const originalLength = responseText.length;

    // Strategy 1: Direct JSON parsing
    let result = this.tryDirectJsonParse<T>(responseText);
    if (result.success) {
      return this.validateAndFinalize(result.data!, schema, {
        parseMethod: 'direct_json',
        originalLength,
        parsedLength: JSON.stringify(result.data).length,
        confidence: 0.95
      }, warnings);
    }
    warnings.push('Direct JSON parsing failed');

    // Strategy 2: Extract JSON from markdown code blocks
    result = this.tryMarkdownJsonExtraction<T>(responseText);
    if (result.success) {
      return this.validateAndFinalize(result.data!, schema, {
        parseMethod: 'markdown_extraction',
        originalLength,
        parsedLength: JSON.stringify(result.data).length,
        confidence: 0.9
      }, warnings);
    }
    warnings.push('Markdown JSON extraction failed');

    // Strategy 3: Find JSON objects in text
    result = this.tryJsonObjectExtraction<T>(responseText);
    if (result.success) {
      return this.validateAndFinalize(result.data!, schema, {
        parseMethod: 'object_extraction',
        originalLength,
        parsedLength: JSON.stringify(result.data).length,
        confidence: 0.8
      }, warnings);
    }
    warnings.push('JSON object extraction failed');

    // Strategy 4: Intelligent text parsing
    result = this.tryIntelligentTextParsing<T>(responseText, schema);
    if (result.success) {
      return this.validateAndFinalize(result.data!, schema, {
        parseMethod: 'intelligent_parsing',
        originalLength,
        parsedLength: JSON.stringify(result.data).length,
        confidence: 0.7
      }, warnings);
    }
    warnings.push('Intelligent text parsing failed');

    // Strategy 5: Use fallback data if provided
    if (fallbackData) {
      warnings.push('Using fallback data');
      return {
        success: true,
        data: fallbackData,
        warnings,
        metadata: {
          parseMethod: 'fallback',
          originalLength,
          parsedLength: JSON.stringify(fallbackData).length,
          confidence: 0.5
        }
      };
    }

    // All strategies failed
    return {
      success: false,
      error: 'Failed to parse AI response with all available strategies',
      warnings,
      metadata: {
        parseMethod: 'none',
        originalLength,
        parsedLength: 0,
        confidence: 0
      }
    };
  }

  /**
   * Strategy 1: Direct JSON parsing
   */
  private static tryDirectJsonParse<T>(text: string): { success: boolean; data?: T } {
    try {
      const data = JSON.parse(text.trim());
      return { success: true, data };
    } catch {
      return { success: false };
    }
  }

  /**
   * Strategy 2: Extract JSON from markdown code blocks
   */
  private static tryMarkdownJsonExtraction<T>(text: string): { success: boolean; data?: T } {
    try {
      // Try different markdown patterns
      const patterns = [
        /```json\s*(\{[\s\S]*?\})\s*```/gi,
        /```\s*(\{[\s\S]*?\})\s*```/gi,
        /`(\{[\s\S]*?\})`/gi
      ];

      for (const pattern of patterns) {
        const matches = text.match(pattern);
        if (matches) {
          for (const match of matches) {
            const jsonText = match.replace(/```(json)?\s*|\s*```|`/g, '').trim();
            try {
              const data = JSON.parse(jsonText);
              return { success: true, data };
            } catch {
              continue;
            }
          }
        }
      }
      return { success: false };
    } catch {
      return { success: false };
    }
  }

  /**
   * Strategy 3: Find JSON objects in text
   */
  private static tryJsonObjectExtraction<T>(text: string): { success: boolean; data?: T } {
    try {
      // Find potential JSON objects by looking for balanced braces
      const openBrace = text.indexOf('{');
      const closeBrace = text.lastIndexOf('}');
      
      if (openBrace !== -1 && closeBrace !== -1 && closeBrace > openBrace) {
        const jsonText = text.substring(openBrace, closeBrace + 1);
        try {
          const data = JSON.parse(jsonText);
          return { success: true, data };
        } catch {
          // Try to fix common JSON issues
          const fixedJson = this.fixCommonJsonIssues(jsonText);
          try {
            const data = JSON.parse(fixedJson);
            return { success: true, data };
          } catch {
            return { success: false };
          }
        }
      }
      return { success: false };
    } catch {
      return { success: false };
    }
  }

  /**
   * Strategy 4: Intelligent text parsing based on schema
   */
  private static tryIntelligentTextParsing<T>(
    text: string, 
    schema?: ValidationSchema
  ): { success: boolean; data?: T } {
    try {
      if (!schema || schema.type !== 'object') {
        return { success: false };
      }

      const result: any = {};
      const lines = text.split('\n').map(line => line.trim()).filter(line => line);

      // Try to extract key-value pairs from text
      for (const line of lines) {
        // Look for patterns like "key: value" or "key = value"
        const keyValueMatch = line.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\s*[:=]\s*(.+)$/);
        if (keyValueMatch) {
          const [, key, value] = keyValueMatch;
          result[key] = this.parseValue(value.trim());
        }
      }

      if (Object.keys(result).length > 0) {
        return { success: true, data: result as T };
      }

      return { success: false };
    } catch {
      return { success: false };
    }
  }

  /**
   * Fix common JSON formatting issues
   */
  private static fixCommonJsonIssues(jsonText: string): string {
    return jsonText
      // Fix trailing commas
      .replace(/,(\s*[}\]])/g, '$1')
      // Fix missing quotes around keys
      .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
      // Fix single quotes to double quotes
      .replace(/'/g, '"')
      // Fix unescaped quotes in strings
      .replace(/"([^"]*)"([^"]*)"([^"]*)"/g, '"$1\\"$2\\"$3"');
  }

  /**
   * Parse a string value to appropriate type
   */
  private static parseValue(value: string): any {
    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) || 
        (value.startsWith("'") && value.endsWith("'"))) {
      return value.slice(1, -1);
    }

    // Try to parse as number
    if (/^\d+(\.\d+)?$/.test(value)) {
      return parseFloat(value);
    }

    // Try to parse as boolean
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;

    // Try to parse as array
    if (value.startsWith('[') && value.endsWith(']')) {
      try {
        return JSON.parse(value);
      } catch {
        // Parse as comma-separated values
        const items = value.slice(1, -1).split(',').map(item => item.trim());
        return items.map(item => this.parseValue(item));
      }
    }

    // Return as string
    return value;
  }

  /**
   * Validate parsed data against schema and finalize result
   */
  private static validateAndFinalize<T>(
    data: T,
    schema: ValidationSchema | undefined,
    metadata: any,
    warnings: string[]
  ): ParseResult<T> {
    if (schema) {
      const validation = this.validateAgainstSchema(data, schema);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Schema validation failed: ${validation.errors.join(', ')}`,
          warnings,
          metadata
        };
      }
      warnings.push(...validation.warnings);
    }

    return {
      success: true,
      data,
      warnings,
      metadata
    };
  }

  /**
   * Validate data against schema
   */
  private static validateAgainstSchema(
    data: any,
    schema: ValidationSchema
  ): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Type validation
    const actualType = Array.isArray(data) ? 'array' : typeof data;
    if (actualType !== schema.type) {
      errors.push(`Expected type ${schema.type}, got ${actualType}`);
      return { isValid: false, errors, warnings };
    }

    // Object validation
    if (schema.type === 'object' && schema.properties) {
      // Check required properties
      if (schema.required) {
        for (const requiredProp of schema.required) {
          if (!(requiredProp in data)) {
            errors.push(`Missing required property: ${requiredProp}`);
          }
        }
      }

      // Validate properties
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (propName in data) {
          const propValidation = this.validateAgainstSchema(data[propName], propSchema);
          if (!propValidation.isValid) {
            errors.push(...propValidation.errors.map(err => `${propName}: ${err}`));
          }
          warnings.push(...propValidation.warnings);
        }
      }
    }

    // Array validation
    if (schema.type === 'array' && schema.items && Array.isArray(data)) {
      for (let i = 0; i < data.length; i++) {
        const itemValidation = this.validateAgainstSchema(data[i], schema.items);
        if (!itemValidation.isValid) {
          errors.push(...itemValidation.errors.map(err => `[${i}]: ${err}`));
        }
        warnings.push(...itemValidation.warnings);
      }
    }

    // String validation
    if (schema.type === 'string' && typeof data === 'string') {
      if (schema.minLength && data.length < schema.minLength) {
        errors.push(`String too short (min: ${schema.minLength})`);
      }
      if (schema.maxLength && data.length > schema.maxLength) {
        errors.push(`String too long (max: ${schema.maxLength})`);
      }
      if (schema.pattern && !schema.pattern.test(data)) {
        errors.push('String does not match required pattern');
      }
    }

    // Number validation
    if (schema.type === 'number' && typeof data === 'number') {
      if (schema.min !== undefined && data < schema.min) {
        errors.push(`Number too small (min: ${schema.min})`);
      }
      if (schema.max !== undefined && data > schema.max) {
        errors.push(`Number too large (max: ${schema.max})`);
      }
    }

    // Enum validation
    if (schema.enum && !schema.enum.includes(data)) {
      errors.push(`Value not in allowed enum: ${schema.enum.join(', ')}`);
    }

    return { isValid: errors.length === 0, errors, warnings };
  }
}

export default AIResponseParser;
