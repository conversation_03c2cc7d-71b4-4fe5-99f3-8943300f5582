/**
 * Navigation State Manager
 * Manages and persists navigation UI state across page refreshes
 */

export interface NavigationState {
  isMobileMenuOpen: boolean;
  isToolsDropdownOpen: boolean;
  lastActiveTab: string | null;
  sidebarCollapsed: boolean;
  preferredViewMode: 'grid' | 'list' | 'card';
  recentlyVisitedPages: string[];
  navigationPreferences: {
    showTooltips: boolean;
    compactMode: boolean;
    autoCloseMenus: boolean;
  };
}

export interface NavigationStateManager {
  getState(): NavigationState;
  setState(updates: Partial<NavigationState>): void;
  toggleMobileMenu(): void;
  closeMobileMenu(): void;
  toggleToolsDropdown(): void;
  closeToolsDropdown(): void;
  setActiveTab(tab: string): void;
  addRecentPage(path: string): void;
  clearRecentPages(): void;
  subscribe(listener: (state: NavigationState) => void): () => void;
  persistState(): void;
  restoreState(): void;
}

class NavigationStateManagerImpl implements NavigationStateManager {
  private state: NavigationState = {
    isMobileMenuOpen: false,
    isToolsDropdownOpen: false,
    lastActiveTab: null,
    sidebarCollapsed: false,
    preferredViewMode: 'card',
    recentlyVisitedPages: [],
    navigationPreferences: {
      showTooltips: true,
      compactMode: false,
      autoCloseMenus: true
    }
  };

  private listeners: Set<(state: NavigationState) => void> = new Set();
  private readonly STORAGE_KEY = 'faafo_navigation_state';
  private readonly MAX_RECENT_PAGES = 10;

  constructor() {
    this.restoreState();
    this.setupEventListeners();
  }

  /**
   * Get current navigation state
   */
  getState(): NavigationState {
    return { ...this.state };
  }

  /**
   * Update navigation state
   */
  setState(updates: Partial<NavigationState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
    this.persistState();
  }

  /**
   * Toggle mobile menu state
   */
  toggleMobileMenu(): void {
    this.setState({ 
      isMobileMenuOpen: !this.state.isMobileMenuOpen,
      // Auto-close other menus if preference is set
      isToolsDropdownOpen: this.state.navigationPreferences.autoCloseMenus ? false : this.state.isToolsDropdownOpen
    });
  }

  /**
   * Close mobile menu
   */
  closeMobileMenu(): void {
    if (this.state.isMobileMenuOpen) {
      this.setState({ isMobileMenuOpen: false });
    }
  }

  /**
   * Toggle tools dropdown state
   */
  toggleToolsDropdown(): void {
    this.setState({ 
      isToolsDropdownOpen: !this.state.isToolsDropdownOpen,
      // Auto-close mobile menu if preference is set
      isMobileMenuOpen: this.state.navigationPreferences.autoCloseMenus ? false : this.state.isMobileMenuOpen
    });
  }

  /**
   * Close tools dropdown
   */
  closeToolsDropdown(): void {
    if (this.state.isToolsDropdownOpen) {
      this.setState({ isToolsDropdownOpen: false });
    }
  }

  /**
   * Set active tab
   */
  setActiveTab(tab: string): void {
    this.setState({ lastActiveTab: tab });
  }

  /**
   * Add page to recent pages list
   */
  addRecentPage(path: string): void {
    const recentPages = [...this.state.recentlyVisitedPages];
    
    // Remove if already exists
    const existingIndex = recentPages.indexOf(path);
    if (existingIndex > -1) {
      recentPages.splice(existingIndex, 1);
    }
    
    // Add to beginning
    recentPages.unshift(path);
    
    // Limit to max recent pages
    if (recentPages.length > this.MAX_RECENT_PAGES) {
      recentPages.splice(this.MAX_RECENT_PAGES);
    }
    
    this.setState({ recentlyVisitedPages: recentPages });
  }

  /**
   * Clear recent pages
   */
  clearRecentPages(): void {
    this.setState({ recentlyVisitedPages: [] });
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: NavigationState) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Persist state to localStorage
   */
  persistState(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const stateToSave = {
        ...this.state,
        // Don't persist temporary UI states
        isMobileMenuOpen: false,
        isToolsDropdownOpen: false
      };
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Failed to persist navigation state:', error);
    }
  }

  /**
   * Restore state from localStorage
   */
  restoreState(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const savedState = localStorage.getItem(this.STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        
        // Merge with default state, ensuring all required properties exist
        this.state = {
          ...this.state,
          ...parsedState,
          // Always start with menus closed
          isMobileMenuOpen: false,
          isToolsDropdownOpen: false
        };
      }
    } catch (error) {
      console.error('Failed to restore navigation state:', error);
    }
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Error in navigation state listener:', error);
      }
    });
  }

  /**
   * Set up event listeners for automatic state management
   */
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // Close menus when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as Element;
      
      // Check if click is outside navigation elements
      if (!target.closest('[data-navigation-menu]') && 
          !target.closest('[data-tools-dropdown]')) {
        if (this.state.isMobileMenuOpen || this.state.isToolsDropdownOpen) {
          this.setState({
            isMobileMenuOpen: false,
            isToolsDropdownOpen: false
          });
        }
      }
    });

    // Close menus on escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        if (this.state.isMobileMenuOpen || this.state.isToolsDropdownOpen) {
          this.setState({
            isMobileMenuOpen: false,
            isToolsDropdownOpen: false
          });
        }
      }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      // Close mobile menu on desktop breakpoint
      if (window.innerWidth >= 768 && this.state.isMobileMenuOpen) {
        this.setState({ isMobileMenuOpen: false });
      }
    });

    // Track page navigation
    window.addEventListener('popstate', () => {
      this.addRecentPage(window.location.pathname);
    });

    // Persist state before page unload
    window.addEventListener('beforeunload', () => {
      this.persistState();
    });
  }
}

// Singleton instance
let navigationStateManager: NavigationStateManagerImpl | null = null;

/**
 * Get the global navigation state manager instance
 */
export function getNavigationStateManager(): NavigationStateManager {
  if (!navigationStateManager) {
    navigationStateManager = new NavigationStateManagerImpl();
  }
  return navigationStateManager;
}

/**
 * Hook for components to use navigation state
 */
export function useNavigationState() {
  const manager = getNavigationStateManager();
  return {
    state: manager.getState(),
    toggleMobileMenu: () => manager.toggleMobileMenu(),
    closeMobileMenu: () => manager.closeMobileMenu(),
    toggleToolsDropdown: () => manager.toggleToolsDropdown(),
    closeToolsDropdown: () => manager.closeToolsDropdown(),
    setActiveTab: (tab: string) => manager.setActiveTab(tab),
    addRecentPage: (path: string) => manager.addRecentPage(path),
    subscribe: (listener: (state: NavigationState) => void) => manager.subscribe(listener)
  };
}
