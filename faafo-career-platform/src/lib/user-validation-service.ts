/**
 * User Validation Service
 * 
 * Provides comprehensive user validation and session synchronization
 * to prevent foreign key constraint violations and authentication issues.
 */

import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NextRequest } from 'next/server';

export interface UserValidationResult {
  isValid: boolean;
  userId?: string;
  error?: string;
  statusCode?: number;
  user?: {
    id: string;
    email: string;
    name: string | null;
    emailVerified: Date | null;
  };
}

export interface SessionValidationOptions {
  requireEmailVerification?: boolean;
  checkAccountLock?: boolean;
  validateUserExists?: boolean;
  refreshSession?: boolean;
}

export class UserValidationService {
  /**
   * Comprehensive user validation with automatic session repair
   */
  static async validateUserSession(
    request: NextRequest,
    options: SessionValidationOptions = {}
  ): Promise<UserValidationResult> {
    const {
      requireEmailVerification = false,
      checkAccountLock = true,
      validateUserExists = true,
      refreshSession = false
    } = options;

    try {
      // Get session from NextAuth
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return {
          isValid: false,
          error: 'Authentication required',
          statusCode: 401
        };
      }

      const userId = session.user.id;

      // Validate user exists in database if requested
      if (validateUserExists) {
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            email: true,
            name: true,
            emailVerified: true,
            lockedUntil: true,
            failedLoginAttempts: true,
            createdAt: true,
            updatedAt: true
          }
        });

        if (!user) {
          // User doesn't exist in database but has valid session
          // This indicates a synchronization issue
          console.error(`User session exists but user not found in database: ${userId}`);
          
          // Attempt to repair by creating user record if we have enough info
          if (session.user.email) {
            try {
              const repairedUser = await this.repairUserRecord(session.user);
              return {
                isValid: true,
                userId: repairedUser.id,
                user: {
                  id: repairedUser.id,
                  email: repairedUser.email,
                  name: repairedUser.name,
                  emailVerified: repairedUser.emailVerified
                }
              };
            } catch (repairError) {
              console.error('Failed to repair user record:', repairError);
              return {
                isValid: false,
                error: 'User account synchronization error',
                statusCode: 500
              };
            }
          }

          return {
            isValid: false,
            error: 'User account not found',
            statusCode: 404
          };
        }

        // Check account lock status
        if (checkAccountLock && user.lockedUntil && user.lockedUntil > new Date()) {
          const lockTimeRemaining = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000);
          return {
            isValid: false,
            error: `Account is locked. Try again in ${lockTimeRemaining} minutes.`,
            statusCode: 423
          };
        }

        // Check email verification if required
        if (requireEmailVerification && !user.emailVerified && process.env.NODE_ENV === 'production') {
          return {
            isValid: false,
            error: 'Email verification required',
            statusCode: 403
          };
        }

        return {
          isValid: true,
          userId: user.id,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            emailVerified: user.emailVerified
          }
        };
      }

      // Basic validation without database check
      return {
        isValid: true,
        userId: userId
      };

    } catch (error) {
      console.error('User validation error:', error);
      return {
        isValid: false,
        error: 'Authentication validation failed',
        statusCode: 500
      };
    }
  }

  /**
   * Repair user record when session exists but database record is missing
   */
  private static async repairUserRecord(sessionUser: any) {
    console.log('Attempting to repair user record for:', sessionUser.email);
    
    // Create user record with session data
    const repairedUser = await prisma.user.create({
      data: {
        id: sessionUser.id,
        email: sessionUser.email,
        name: sessionUser.name || null,
        emailVerified: sessionUser.emailVerified || null,
        // Set reasonable defaults
        failedLoginAttempts: 0,
        lockedUntil: null,
        password: '', // Will need to be set through password reset
      },
      select: {
        id: true,
        email: true,
        name: true,
        emailVerified: true
      }
    });

    console.log('Successfully repaired user record:', repairedUser.id);
    return repairedUser;
  }

  /**
   * Validate user exists and has permission for specific resource
   */
  static async validateUserResourceAccess(
    userId: string,
    resourceType: 'interview_session' | 'interview_response' | 'interview_progress',
    resourceId: string
  ): Promise<UserValidationResult> {
    try {
      let hasAccess = false;

      switch (resourceType) {
        case 'interview_session':
          const session = await prisma.interviewSession.findFirst({
            where: {
              id: resourceId,
              userId: userId
            },
            select: { id: true }
          });
          hasAccess = !!session;
          break;

        case 'interview_response':
          const response = await prisma.interviewResponse.findFirst({
            where: {
              id: resourceId,
              userId: userId
            },
            select: { id: true }
          });
          hasAccess = !!response;
          break;

        case 'interview_progress':
          const progress = await prisma.interviewProgress.findFirst({
            where: {
              id: resourceId,
              userId: userId
            },
            select: { id: true }
          });
          hasAccess = !!progress;
          break;
      }

      if (!hasAccess) {
        return {
          isValid: false,
          error: 'Resource not found or access denied',
          statusCode: 404
        };
      }

      return {
        isValid: true,
        userId: userId
      };

    } catch (error) {
      console.error('Resource access validation error:', error);
      return {
        isValid: false,
        error: 'Resource validation failed',
        statusCode: 500
      };
    }
  }

  /**
   * Ensure user has required database relationships
   */
  static async ensureUserRelationships(userId: string): Promise<void> {
    try {
      // Check if user has a profile
      const profile = await prisma.profile.findUnique({
        where: { userId },
        select: { id: true }
      });

      if (!profile) {
        // Create basic profile
        await prisma.profile.create({
          data: {
            userId,
            bio: null,
            profilePictureUrl: null,
            socialMediaLinks: undefined,
            firstName: null,
            lastName: null,
            location: null,
            website: null,
            careerInterests: undefined,
            skillsToLearn: undefined,
            profileVisibility: 'PRIVATE',
            profileCompletionScore: 0
          }
        });
        console.log('Created profile for user:', userId);
      }

    } catch (error) {
      console.error('Error ensuring user relationships:', error);
      // Don't throw - this is a best-effort operation
    }
  }
}

export default UserValidationService;
