// Static Resources Library
// Centralized location for all static resources used across the application

interface Resource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  author?: string;
  duration?: string;
  skillLevel?: string;
  cost?: string;
  averageRating?: number;
  totalRatings?: number;
  careerPaths?: {
    id: string;
    name: string;
    slug: string;
  }[];
}

// Mindset Resources for Career Transitions
const mindsetResources: Resource[] = [
  // Fear & Anxiety Resources
  {
    id: '1',
    title: 'Overcoming Six Fears of Midlife Career Change',
    description: 'A comprehensive guide to understanding and conquering the fears that hold you back from making a career transition.',
    url: 'https://www.linkedin.com/pulse/overcoming-six-fears-midlife-career-change-guide-joanne-savoie-malone-xwpme',
    type: 'article',
    category: 'fear',
    author: '<PERSON>',
    duration: '8 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '2',
    title: 'Why Career Transition Is So Hard',
    description: 'Understanding the emotional journey of career transitions and how to navigate psychological challenges.',
    url: 'https://hbr.org/2023/11/why-career-transition-is-so-hard',
    type: 'article',
    category: 'fear',
    author: 'Harvard Business Review',
    duration: '12 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '3',
    title: 'Mindfulness Exercises for Stress Reduction',
    description: 'Meditation and mindfulness techniques to manage anxiety and stress during career changes.',
    url: 'https://www.mayoclinic.org/healthy-lifestyle/consumer-health/in-depth/mindfulness-exercises/art-20046356',
    type: 'article',
    category: 'fear',
    author: 'Mayo Clinic',
    duration: '6 min read',
    averageRating: 0,
    totalRatings: 0
  },

  // Financial Planning Resources
  {
    id: '4',
    title: 'How to Think Strategically About a Career Transition',
    description: 'Learn strategic approaches to planning and executing your career transition with financial considerations.',
    url: 'https://hbr.org/2023/09/how-to-think-strategically-about-a-career-transition',
    type: 'article',
    category: 'financial',
    author: 'Harvard Business Review',
    duration: '10 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '5',
    title: 'Financial Planning for Transition (TAP)',
    description: 'Comprehensive guide for financial planning during career transitions from military and government resources.',
    url: 'https://www.tapevents.mil/resources',
    type: 'article',
    category: 'financial',
    author: 'U.S. Department of Defense',
    duration: '5 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '6',
    title: 'Side Hustle to Full-Time: A Transition Guide',
    description: 'How to successfully transition from a side project to a full-time career with minimal risk.',
    url: 'https://www.entrepreneur.com/growing-a-business/how-to-turn-your-side-hustle-into-a-full-time-business/325890',
    type: 'article',
    category: 'financial',
    author: 'Entrepreneur',
    duration: '11 min read',
    averageRating: 0,
    totalRatings: 0
  },

  // Confidence & Imposter Syndrome Resources
  {
    id: '7',
    title: 'Overcoming Career Fears: Building Confidence',
    description: 'Practical strategies to build self-confidence and overcome self-doubt when changing careers.',
    url: 'https://www.linkedin.com/pulse/overcoming-career-fears-guide-building-confidence-moving-sulista-1amwe',
    type: 'article',
    category: 'imposter',
    author: 'Vaclav Sulista',
    duration: '7 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '8',
    title: 'Dealing with Imposter Syndrome',
    description: 'Strategies to overcome self-doubt and imposter syndrome when pursuing new career opportunities.',
    url: 'https://hbr.org/2021/02/stop-telling-women-they-have-imposter-syndrome',
    type: 'article',
    category: 'imposter',
    author: 'Harvard Business Review',
    duration: '12 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '9',
    title: 'The Art of Self-Compassion in Career Transitions',
    description: 'Learning to be kind to yourself during the ups and downs of career change.',
    url: 'https://self-compassion.org/the-three-elements-of-self-compassion-2/',
    type: 'article',
    category: 'imposter',
    author: 'Dr. Kristin Neff',
    duration: '10 min read',
    averageRating: 0,
    totalRatings: 0
  },

  // Strategic Planning Resources
  {
    id: '10',
    title: 'The Complete Guide to Career Pivoting',
    description: 'A comprehensive resource for planning and executing a successful career pivot at any stage of life.',
    url: 'https://www.linkedin.com/pulse/complete-guide-career-pivoting-jenny-foss/',
    type: 'article',
    category: 'planning',
    author: 'LinkedIn Learning',
    duration: '15 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '11',
    title: 'Designing Your Life: Career Design Thinking',
    description: 'Key insights from the Stanford course on applying design thinking to career and life decisions.',
    url: 'https://designingyour.life/',
    type: 'article',
    category: 'planning',
    author: 'Bill Burnett & Dave Evans',
    duration: '15 min read',
    averageRating: 0,
    totalRatings: 0
  },

  // Motivation & Inspiration Resources
  {
    id: '12',
    title: 'TED Talk: How to Find Work You Love',
    description: 'Scott Dinsmore shares insights on discovering meaningful work and overcoming fear.',
    url: 'https://www.ted.com/talks/scott_dinsmore_how_to_find_work_you_love',
    type: 'video',
    category: 'motivation',
    author: 'TED',
    duration: '18 min watch',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '13',
    title: 'The Career Change Podcast',
    description: 'Weekly interviews with people who have successfully changed careers, sharing their stories and strategies.',
    url: 'https://thecareerchangepodcast.com/',
    type: 'podcast',
    category: 'motivation',
    author: 'Anna Lundberg',
    duration: 'Various episodes',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '14',
    title: 'Overcoming Job Search Fear in Midlife Career Change',
    description: 'Strategies for building professional networks and overcoming fear during career transitions.',
    url: 'https://www.nocodeinstitute.io/post/overcome-fear-of-job-search-in-midlife-career-change',
    type: 'article',
    category: 'motivation',
    author: 'No Code Institute',
    duration: '9 min read',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: '15',
    title: 'Overcoming the Fear of Career Change: Balancing Risk and Reward',
    description: 'How to handle uncertainty and make strategic decisions when making career transitions.',
    url: 'https://www.linkedin.com/pulse/overcoming-fear-career-change-balancing-risk-reward-theresa-white-ghpgf',
    type: 'article',
    category: 'motivation',
    author: 'Theresa White',
    duration: '8 min read',
    averageRating: 0,
    totalRatings: 0
  }
];

// Learning Resources for Skill Development
const learningResources: Resource[] = [
  // Cybersecurity
  {
    id: 'cyber-1',
    title: 'Ethical Hacking Essentials (E|HE)',
    description: 'Strong foundations in ethical hacking and penetration testing for entry-level careers',
    url: 'https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/',
    type: 'course',
    category: 'cybersecurity',
    author: 'EC-Council',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: 'cyber-2',
    title: 'Network Defense Essentials (N|DE)',
    description: 'Fundamentals of network security, protocols, controls, and identity/access management',
    url: 'https://www.eccouncil.org/train-certify/network-defense-essentials-nde/',
    type: 'course',
    category: 'cybersecurity',
    author: 'EC-Council',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: 'cyber-3',
    title: 'CISA Cybersecurity Training',
    description: 'Government-backed cybersecurity training from beginner to advanced levels',
    url: 'https://www.cisa.gov/cybersecurity-training-exercises',
    type: 'course',
    category: 'cybersecurity',
    author: 'CISA',
    duration: 'Various modules',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  // Data Science
  {
    id: 'ds-1',
    title: 'Data Science: Machine Learning',
    description: 'Basics of machine learning, cross-validation, popular algorithms, and avoiding overtraining',
    url: 'https://pll.harvard.edu/course/data-science-machine-learning',
    type: 'course',
    category: 'data-science',
    author: 'Harvard University',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: 'ds-2',
    title: 'Machine Learning Crash Course',
    description: 'Fast-paced introduction with animated videos, interactive visualizations, and hands-on practice',
    url: 'https://developers.google.com/machine-learning/crash-course',
    type: 'course',
    category: 'data-science',
    author: 'Google',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  // AI
  {
    id: 'ai-1',
    title: 'AI For Everyone',
    description: 'Non-technical introduction to AI concepts and applications',
    url: 'https://www.coursera.org/learn/ai-for-everyone',
    type: 'course',
    category: 'ai',
    author: 'DeepLearning.AI',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'freemium',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: 'ai-2',
    title: 'Elements of AI',
    description: 'Introduction to AI concepts and their practical applications',
    url: 'https://www.elementsofai.com/',
    type: 'course',
    category: 'ai',
    author: 'University of Helsinki',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  // Digital Marketing
  {
    id: 'dm-1',
    title: 'Fundamentals of Digital Marketing',
    description: 'Comprehensive introduction to digital marketing concepts and tools',
    url: 'https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing',
    type: 'course',
    category: 'digital-marketing',
    author: 'Google Digital Garage',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  {
    id: 'dm-2',
    title: 'Social Media Marketing',
    description: 'Introduction to social media marketing strategies and platforms',
    url: 'https://academy.hubspot.com/courses/social-media',
    type: 'course',
    category: 'digital-marketing',
    author: 'HubSpot Academy',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free',
    averageRating: 0,
    totalRatings: 0
  },
  // Blockchain
  {
    id: 'bc-1',
    title: 'Blockchain Basics',
    description: 'Introduction to blockchain technology, cryptocurrency, and smart contracts',
    url: 'https://www.coursera.org/learn/blockchain-basics',
    type: 'course',
    category: 'blockchain',
    author: 'University at Buffalo',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'freemium',
    averageRating: 0,
    totalRatings: 0
  },
  // Project Management
  {
    id: 'pm-1',
    title: 'Project Management Foundations',
    description: 'Introduction to project management principles and methodologies',
    url: 'https://www.linkedin.com/learning/project-management-foundations-2019',
    type: 'course',
    category: 'project-management',
    author: 'LinkedIn Learning',
    duration: 'Video course',
    skillLevel: 'beginner',
    cost: 'freemium',
    averageRating: 0,
    totalRatings: 0
  }
];

// Combined resources
const allStaticResources = [...mindsetResources, ...learningResources];

// Utility functions
export function getStaticResourceById(id: string): Resource | null {
  return allStaticResources.find(resource => resource.id === id) || null;
}

export function getAllStaticResources(): Resource[] {
  return allStaticResources;
}

export function getMindsetResources(): Resource[] {
  return mindsetResources;
}

export function getLearningResources(): Resource[] {
  return learningResources;
}

export function getStaticResourcesByCategory(category: string): Resource[] {
  return allStaticResources.filter(resource => resource.category === category);
}

export function getStaticResourcesByType(type: string): Resource[] {
  return allStaticResources.filter(resource => resource.type === type);
}
