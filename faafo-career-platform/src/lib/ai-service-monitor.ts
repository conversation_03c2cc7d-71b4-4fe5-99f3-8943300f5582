/**
 * AI Service Health Monitoring and Analytics
 * Provides real-time monitoring, performance metrics, and usage analytics
 */

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  responseTime: number;
  error?: string;
}

interface ServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  rateLimitHits: number;
  lastHealthCheck: number;
  uptime: number;
}

interface PerformanceMetric {
  timestamp: number;
  operation: string;
  responseTime: number;
  success: boolean;
  cacheHit: boolean;
  userId?: string;
  error?: string;
}

interface UsageAnalytics {
  dailyRequests: Record<string, number>;
  operationBreakdown: Record<string, number>;
  userActivity: Record<string, number>;
  errorPatterns: Record<string, number>;
  peakUsageHours: number[];
}

export class AIServiceMonitor {
  private static instance: AIServiceMonitor;
  private metrics: ServiceMetrics;
  private performanceHistory: PerformanceMetric[] = [];
  private healthHistory: HealthStatus[] = [];
  private startTime: number;
  private readonly MAX_HISTORY_SIZE = 1000;
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private healthCheckTimer?: NodeJS.Timeout;

  private constructor() {
    this.startTime = Date.now();
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      rateLimitHits: 0,
      lastHealthCheck: 0,
      uptime: 0
    };
    
    this.startHealthChecking();
  }

  static getInstance(): AIServiceMonitor {
    if (!AIServiceMonitor.instance) {
      AIServiceMonitor.instance = new AIServiceMonitor();
    }
    return AIServiceMonitor.instance;
  }

  /**
   * Record a service operation
   */
  recordOperation(
    operation: string,
    responseTime: number,
    success: boolean,
    cacheHit: boolean = false,
    userId?: string,
    error?: string
  ): void {
    const metric: PerformanceMetric = {
      timestamp: Date.now(),
      operation,
      responseTime,
      success,
      cacheHit,
      userId,
      error
    };

    this.performanceHistory.push(metric);
    this.trimHistory();
    this.updateMetrics(metric);
  }

  /**
   * Record rate limit hit
   */
  recordRateLimitHit(userId: string): void {
    this.metrics.rateLimitHits++;
    console.warn(`[AI-Monitor] Rate limit hit for user: ${userId}`);
  }

  /**
   * Get current service health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    try {
      const startTime = Date.now();
      
      // Perform health checks on all components
      const aiHealthy = await this.checkAIServiceHealth();
      const cacheHealthy = await this.checkCacheHealth();
      const dbHealthy = await this.checkDatabaseHealth();
      
      const responseTime = Date.now() - startTime;
      const allHealthy = aiHealthy && cacheHealthy && dbHealthy;
      
      const status: HealthStatus = {
        status: allHealthy ? 'healthy' : 'degraded',
        timestamp: Date.now(),
        responseTime
      };

      this.healthHistory.push(status);
      this.trimHealthHistory();
      this.metrics.lastHealthCheck = status.timestamp;

      return status;
    } catch (error) {
      const status: HealthStatus = {
        status: 'unhealthy',
        timestamp: Date.now(),
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      this.healthHistory.push(status);
      this.trimHealthHistory();
      
      return status;
    }
  }

  /**
   * Get comprehensive service metrics
   */
  getMetrics(): ServiceMetrics & { 
    recentPerformance: PerformanceMetric[];
    healthTrend: HealthStatus[];
  } {
    this.metrics.uptime = Date.now() - this.startTime;
    
    return {
      ...this.metrics,
      recentPerformance: this.performanceHistory.slice(-50), // Last 50 operations
      healthTrend: this.healthHistory.slice(-20) // Last 20 health checks
    };
  }

  /**
   * Get usage analytics
   */
  getUsageAnalytics(): UsageAnalytics {
    const now = new Date();
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    });

    const dailyRequests: Record<string, number> = {};
    const operationBreakdown: Record<string, number> = {};
    const userActivity: Record<string, number> = {};
    const errorPatterns: Record<string, number> = {};
    const hourlyUsage = new Array(24).fill(0);

    // Initialize daily requests
    last7Days.forEach(date => {
      dailyRequests[date] = 0;
    });

    // Analyze performance history
    this.performanceHistory.forEach(metric => {
      const date = new Date(metric.timestamp).toISOString().split('T')[0];
      const hour = new Date(metric.timestamp).getHours();
      
      // Daily requests
      if (dailyRequests.hasOwnProperty(date)) {
        dailyRequests[date]++;
      }
      
      // Operation breakdown
      operationBreakdown[metric.operation] = (operationBreakdown[metric.operation] || 0) + 1;
      
      // User activity
      if (metric.userId) {
        userActivity[metric.userId] = (userActivity[metric.userId] || 0) + 1;
      }
      
      // Error patterns
      if (!metric.success && metric.error) {
        errorPatterns[metric.error] = (errorPatterns[metric.error] || 0) + 1;
      }
      
      // Hourly usage
      hourlyUsage[hour]++;
    });

    // Find peak usage hours
    const peakUsageHours = hourlyUsage
      .map((count, hour) => ({ hour, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3)
      .map(item => item.hour);

    return {
      dailyRequests,
      operationBreakdown,
      userActivity,
      errorPatterns,
      peakUsageHours
    };
  }

  /**
   * Get performance insights
   */
  getPerformanceInsights(): {
    slowestOperations: Array<{ operation: string; avgResponseTime: number }>;
    errorRateByOperation: Array<{ operation: string; errorRate: number }>;
    cacheEffectiveness: { hitRate: number; avgResponseTimeWithCache: number; avgResponseTimeWithoutCache: number };
    recommendations: string[];
  } {
    const operationStats = new Map<string, { 
      totalTime: number; 
      count: number; 
      errors: number; 
      cacheHits: number;
      cacheHitTime: number;
      cacheMissTime: number;
    }>();

    // Analyze performance data
    this.performanceHistory.forEach(metric => {
      if (!operationStats.has(metric.operation)) {
        operationStats.set(metric.operation, {
          totalTime: 0,
          count: 0,
          errors: 0,
          cacheHits: 0,
          cacheHitTime: 0,
          cacheMissTime: 0
        });
      }

      const stats = operationStats.get(metric.operation)!;
      stats.totalTime += metric.responseTime;
      stats.count++;
      
      if (!metric.success) {
        stats.errors++;
      }
      
      if (metric.cacheHit) {
        stats.cacheHits++;
        stats.cacheHitTime += metric.responseTime;
      } else {
        stats.cacheMissTime += metric.responseTime;
      }
    });

    // Calculate insights
    const slowestOperations = Array.from(operationStats.entries())
      .map(([operation, stats]) => ({
        operation,
        avgResponseTime: stats.totalTime / stats.count
      }))
      .sort((a, b) => b.avgResponseTime - a.avgResponseTime)
      .slice(0, 5);

    const errorRateByOperation = Array.from(operationStats.entries())
      .map(([operation, stats]) => ({
        operation,
        errorRate: (stats.errors / stats.count) * 100
      }))
      .filter(item => item.errorRate > 0)
      .sort((a, b) => b.errorRate - a.errorRate);

    // Cache effectiveness
    const totalCacheHits = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheHits, 0);
    const totalOperations = this.performanceHistory.length;
    const totalCacheHitTime = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheHitTime, 0);
    const totalCacheMissTime = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheMissTime, 0);

    const cacheEffectiveness = {
      hitRate: totalOperations > 0 ? (totalCacheHits / totalOperations) * 100 : 0,
      avgResponseTimeWithCache: totalCacheHits > 0 ? totalCacheHitTime / totalCacheHits : 0,
      avgResponseTimeWithoutCache: (totalOperations - totalCacheHits) > 0 ? totalCacheMissTime / (totalOperations - totalCacheHits) : 0
    };

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (this.metrics.averageResponseTime > 5000) {
      recommendations.push('Consider optimizing AI prompts to reduce response times');
    }
    
    if (cacheEffectiveness.hitRate < 30) {
      recommendations.push('Improve caching strategy to increase cache hit rate');
    }
    
    if (this.metrics.failedRequests / this.metrics.totalRequests > 0.05) {
      recommendations.push('Investigate and address high error rate');
    }
    
    if (this.metrics.rateLimitHits > 10) {
      recommendations.push('Consider implementing request queuing to reduce rate limit hits');
    }

    return {
      slowestOperations,
      errorRateByOperation,
      cacheEffectiveness,
      recommendations
    };
  }

  /**
   * Start automatic health checking
   */
  private startHealthChecking(): void {
    this.healthCheckTimer = setInterval(async () => {
      await this.getHealthStatus();
    }, this.HEALTH_CHECK_INTERVAL);
  }

  /**
   * Stop health checking
   */
  stopHealthChecking(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }
  }

  /**
   * Update metrics based on new performance data
   */
  private updateMetrics(metric: PerformanceMetric): void {
    this.metrics.totalRequests++;
    
    if (metric.success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }

    // Update average response time
    const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + metric.responseTime;
    this.metrics.averageResponseTime = totalResponseTime / this.metrics.totalRequests;

    // Update cache hit rate
    const cacheHits = this.performanceHistory.filter(m => m.cacheHit).length;
    this.metrics.cacheHitRate = (cacheHits / this.metrics.totalRequests) * 100;
  }

  /**
   * Trim performance history to prevent memory issues
   */
  private trimHistory(): void {
    if (this.performanceHistory.length > this.MAX_HISTORY_SIZE) {
      this.performanceHistory = this.performanceHistory.slice(-this.MAX_HISTORY_SIZE);
    }
  }

  /**
   * Trim health history
   */
  private trimHealthHistory(): void {
    if (this.healthHistory.length > 100) {
      this.healthHistory = this.healthHistory.slice(-100);
    }
  }

  /**
   * Check AI service health
   */
  private async checkAIServiceHealth(): Promise<boolean> {
    try {
      // This would typically make a lightweight request to the AI service
      // For now, we'll simulate a health check
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check cache health
   */
  private async checkCacheHealth(): Promise<boolean> {
    try {
      // Check if cache is responsive
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      // Check if database is responsive
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Export metrics for external monitoring systems
   */
  exportMetrics(): string {
    const metrics = this.getMetrics();
    const analytics = this.getUsageAnalytics();
    const insights = this.getPerformanceInsights();

    return JSON.stringify({
      timestamp: Date.now(),
      service: 'ai-service',
      metrics,
      analytics,
      insights
    }, null, 2);
  }

  /**
   * Reset all metrics (useful for testing)
   */
  reset(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      rateLimitHits: 0,
      lastHealthCheck: 0,
      uptime: 0
    };
    this.performanceHistory = [];
    this.healthHistory = [];
    this.startTime = Date.now();
  }
}

// Export singleton instance
export const aiServiceMonitor = AIServiceMonitor.getInstance();
export default aiServiceMonitor;
