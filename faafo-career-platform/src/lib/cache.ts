// Caching utilities for improved performance

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

interface CacheOptions {
  ttl?: number; // Default TTL in milliseconds
  maxSize?: number; // Maximum number of entries
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL: number;
  private maxSize: number;

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default
    this.maxSize = options.maxSize || 1000;
  }

  set<T>(key: string, data: T, ttl?: number): void {
    // Remove expired entries if cache is full
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
      
      // If still full after cleanup, remove oldest entry
      if (this.cache.size >= this.maxSize) {
        const oldestKey = this.cache.keys().next().value;
        if (oldestKey) {
          this.cache.delete(oldestKey);
        }
      }
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    };

    this.cache.set(key, entry);
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Remove expired entries
  cleanup(): void {
    const now = Date.now();
    Array.from(this.cache.entries()).forEach(([key, entry]) => {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    });
  }

  // Get cache statistics
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      defaultTTL: this.defaultTTL
    };
  }

  // JSON-specific methods for convenience
  getJSON<T>(key: string): T | null {
    return this.get<T>(key);
  }

  setJSON<T>(key: string, data: T, ttl?: number): void {
    this.set(key, data, ttl);
  }
}

// Create cache instances for different data types
export const apiCache = new MemoryCache({ ttl: 5 * 60 * 1000, maxSize: 500 }); // 5 minutes
export const userCache = new MemoryCache({ ttl: 15 * 60 * 1000, maxSize: 100 }); // 15 minutes
export const staticCache = new MemoryCache({ ttl: 60 * 60 * 1000, maxSize: 200 }); // 1 hour

// Cache key generators
export const cacheKeys = {
  user: (id: string) => `user:${id}`,
  userProfile: (id: string) => `user:profile:${id}`,
  assessment: (userId: string) => `assessment:${userId}`,
  careerPaths: () => 'career-paths:all',
  learningResources: (filters: string) => `learning-resources:${filters}`,
  userProgress: (userId: string) => `progress:${userId}`,
  freedomFund: (userId: string) => `freedom-fund:${userId}`,
  recommendations: (userId: string) => `recommendations:${userId}`,
};

// Higher-order function to add caching to any async function
export function withCache<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  cache: MemoryCache = apiCache,
  ttl?: number
): T {
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator(...args);
    
    // Try to get from cache first
    const cached = cache.get(key);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, execute function and cache result
    try {
      const result = await fn(...args);
      cache.set(key, result, ttl);
      return result;
    } catch (error) {
      // Don't cache errors
      throw error;
    }
  }) as T;
}

// Specific caching functions for common operations
export const cachedFunctions = {
  // Cache user data
  getUser: withCache(
    async (id: string) => {
      // This would be replaced with actual user service call
      throw new Error('Replace with actual user service');
    },
    (id: string) => cacheKeys.user(id),
    userCache
  ),

  // Cache career paths
  getCareerPaths: withCache(
    async () => {
      // This would be replaced with actual career paths service call
      throw new Error('Replace with actual career paths service');
    },
    () => cacheKeys.careerPaths(),
    staticCache
  ),

  // Cache learning resources with filters
  getLearningResources: withCache(
    async (filters: Record<string, any>) => {
      // This would be replaced with actual learning resources service call
      throw new Error('Replace with actual learning resources service');
    },
    (filters: Record<string, any>) => cacheKeys.learningResources(JSON.stringify(filters)),
    apiCache
  ),
};

// Cache invalidation utilities
export const cacheInvalidation = {
  // Invalidate user-related caches
  invalidateUser: (userId: string) => {
    userCache.delete(cacheKeys.user(userId));
    userCache.delete(cacheKeys.userProfile(userId));
    apiCache.delete(cacheKeys.assessment(userId));
    apiCache.delete(cacheKeys.userProgress(userId));
    apiCache.delete(cacheKeys.freedomFund(userId));
    apiCache.delete(cacheKeys.recommendations(userId));
  },

  // Invalidate assessment cache
  invalidateAssessment: (userId: string) => {
    apiCache.delete(cacheKeys.assessment(userId));
    apiCache.delete(cacheKeys.recommendations(userId)); // Recommendations depend on assessment
  },

  // Invalidate learning progress
  invalidateProgress: (userId: string) => {
    apiCache.delete(cacheKeys.userProgress(userId));
    apiCache.delete(cacheKeys.recommendations(userId)); // Recommendations depend on progress
  },

  // Invalidate static data (when admin updates)
  invalidateStatic: () => {
    staticCache.delete(cacheKeys.careerPaths());
    // Clear all learning resources cache entries
    staticCache.clear(); // Simple approach, could be more granular
  },
};

// Browser-side caching using localStorage (with expiration)
export class BrowserCache {
  private prefix: string;

  constructor(prefix: string = 'faafo_cache_') {
    this.prefix = prefix;
  }

  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void {
    if (typeof window === 'undefined') return;

    const entry = {
      data,
      expires: Date.now() + ttlMs
    };

    try {
      localStorage.setItem(this.prefix + key, JSON.stringify(entry));
    } catch (error) {
      console.warn('Failed to set browser cache:', error);
    }
  }

  get<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;

    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;

      const entry = JSON.parse(item);
      
      if (Date.now() > entry.expires) {
        localStorage.removeItem(this.prefix + key);
        return null;
      }

      return entry.data as T;
    } catch (error) {
      console.warn('Failed to get browser cache:', error);
      return null;
    }
  }

  delete(key: string): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.prefix + key);
  }

  clear(): void {
    if (typeof window === 'undefined') return;
    
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    });
  }

  // Clean up expired entries
  cleanup(): void {
    if (typeof window === 'undefined') return;

    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        this.get(key.replace(this.prefix, '')); // This will remove expired entries
      }
    });
  }
}

export const browserCache = new BrowserCache();

// React hook for browser caching
export function useBrowserCache<T>(key: string, ttlMs: number = 5 * 60 * 1000) {
  const setCache = (data: T) => {
    browserCache.set(key, data, ttlMs);
  };

  const getCache = (): T | null => {
    return browserCache.get<T>(key);
  };

  const deleteCache = () => {
    browserCache.delete(key);
  };

  return { setCache, getCache, deleteCache };
}

// Periodic cleanup
if (typeof window !== 'undefined') {
  // Clean up expired cache entries every 10 minutes
  setInterval(() => {
    apiCache.cleanup();
    userCache.cleanup();
    staticCache.cleanup();
    browserCache.cleanup();
  }, 10 * 60 * 1000);
}

// Export a general cache instance for backward compatibility
export const cache = apiCache;

// Export cacheService for API routes
export const cacheService = apiCache;

// Export MemoryCache as named export
export { MemoryCache };

export default {
  apiCache,
  userCache,
  staticCache,
  browserCache,
  withCache,
  cacheKeys,
  cacheInvalidation,
  BrowserCache,
  MemoryCache
};
