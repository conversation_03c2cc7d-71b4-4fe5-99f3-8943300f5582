/**
 * Unified Caching Service
 * 
 * Provides consistent caching strategy across the application
 * with intelligent cache invalidation and performance optimization.
 */

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  tags: string[];
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  tags?: string[]; // Tags for cache invalidation
  maxSize?: number; // Maximum cache size
  serialize?: boolean; // Whether to serialize data
}

export interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
  memoryUsage: number;
  oldestEntry: number;
  newestEntry: number;
}

export class UnifiedCachingService {
  private static cache = new Map<string, CacheEntry<any>>();
  private static stats = {
    hits: 0,
    misses: 0,
    evictions: 0
  };
  
  private static readonly DEFAULT_TTL = 300000; // 5 minutes
  private static readonly MAX_CACHE_SIZE = 1000;
  private static readonly CLEANUP_INTERVAL = 60000; // 1 minute

  static {
    // Start automatic cleanup
    if (typeof window !== 'undefined') {
      setInterval(() => this.cleanup(), this.CLEANUP_INTERVAL);
    }
  }

  /**
   * Get item from cache
   */
  static get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.data;
  }

  /**
   * Set item in cache
   */
  static set<T>(key: string, data: T, options: CacheOptions = {}): void {
    const {
      ttl = this.DEFAULT_TTL,
      tags = [],
      serialize = false
    } = options;

    // Serialize data if requested
    const cacheData = serialize ? JSON.parse(JSON.stringify(data)) : data;

    const entry: CacheEntry<T> = {
      data: cacheData,
      timestamp: Date.now(),
      ttl,
      accessCount: 0,
      lastAccessed: Date.now(),
      tags
    };

    // Check cache size and evict if necessary
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(key, entry);
  }

  /**
   * Delete item from cache
   */
  static delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear cache by tags
   */
  static invalidateByTags(tags: string[]): number {
    let invalidated = 0;
    const keysToDelete: string[] = [];

    this.cache.forEach((entry, key) => {
      if (entry.tags.some(tag => tags.includes(tag))) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      invalidated++;
    });

    return invalidated;
  }

  /**
   * Clear all cache
   */
  static clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, evictions: 0 };
  }

  /**
   * Get cache statistics
   */
  static getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const timestamps = entries.map(e => e.timestamp);
    
    return {
      size: this.cache.size,
      hits: this.stats.hits,
      misses: this.stats.misses,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      memoryUsage: this.estimateMemoryUsage(),
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };
  }

  /**
   * Memoization wrapper for functions
   */
  static memoize<T extends (...args: any[]) => any>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string,
    options: CacheOptions = {}
  ): T {
    const generateKey = keyGenerator || ((...args) => `memoized:${fn.name}:${JSON.stringify(args)}`);

    return ((...args: Parameters<T>) => {
      const key = generateKey(...args);
      const cached = this.get(key);
      
      if (cached !== null) {
        return cached;
      }

      const result = fn(...args);
      this.set(key, result, options);
      return result;
    }) as T;
  }

  /**
   * Cache wrapper for async functions with automatic invalidation
   */
  static async cacheAsync<T>(
    key: string,
    asyncFn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Check cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    try {
      const result = await asyncFn();
      this.set(key, result, options);
      return result;
    } catch (error) {
      // Don't cache errors
      throw error;
    }
  }

  /**
   * Batch cache operations
   */
  static setBatch<T>(entries: Array<{ key: string; data: T; options?: CacheOptions }>): void {
    entries.forEach(({ key, data, options }) => {
      this.set(key, data, options);
    });
  }

  static getBatch<T>(keys: string[]): Map<string, T | null> {
    const results = new Map<string, T | null>();
    keys.forEach(key => {
      results.set(key, this.get<T>(key));
    });
    return results;
  }

  /**
   * Cache warming - preload frequently accessed data
   */
  static async warmCache(warmupFunctions: Array<{ key: string; fn: () => Promise<any>; options?: CacheOptions }>): Promise<void> {
    const promises = warmupFunctions.map(async ({ key, fn, options }) => {
      try {
        const data = await fn();
        this.set(key, data, options);
      } catch (error) {
        console.error(`Cache warming failed for key ${key}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * Private helper methods
   */
  private static evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null;
    let lruTime = Date.now();

    this.cache.forEach((entry, key) => {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    });

    if (lruKey) {
      this.cache.delete(lruKey);
      this.stats.evictions++;
    }
  }

  private static cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));

    if (keysToDelete.length > 0) {
      console.log(`Cache cleanup: removed ${keysToDelete.length} expired entries`);
    }
  }

  private static estimateMemoryUsage(): number {
    let size = 0;
    this.cache.forEach((entry, key) => {
      size += key.length * 2; // Approximate string size
      size += JSON.stringify(entry).length * 2; // Approximate entry size
    });
    return size;
  }

  /**
   * Interview Practice specific cache helpers
   */
  static cacheInterviewSession(sessionId: string, data: any, ttl: number = 600000): void {
    this.set(`interview:session:${sessionId}`, data, {
      ttl,
      tags: ['interview', 'session', sessionId]
    });
  }

  static getCachedInterviewSession(sessionId: string): any | null {
    return this.get(`interview:session:${sessionId}`);
  }

  static cacheInterviewQuestions(sessionId: string, questions: any[], ttl: number = 1800000): void {
    this.set(`interview:questions:${sessionId}`, questions, {
      ttl,
      tags: ['interview', 'questions', sessionId]
    });
  }

  static getCachedInterviewQuestions(sessionId: string): any[] | null {
    return this.get(`interview:questions:${sessionId}`);
  }

  static cacheInterviewProgress(userId: string, progress: any, ttl: number = 300000): void {
    this.set(`interview:progress:${userId}`, progress, {
      ttl,
      tags: ['interview', 'progress', userId]
    });
  }

  static getCachedInterviewProgress(userId: string): any | null {
    return this.get(`interview:progress:${userId}`);
  }

  static invalidateInterviewCache(sessionId: string): void {
    this.invalidateByTags(['interview', sessionId]);
  }

  static invalidateUserInterviewCache(userId: string): void {
    this.invalidateByTags(['interview', userId]);
  }
}

export default UnifiedCachingService;
