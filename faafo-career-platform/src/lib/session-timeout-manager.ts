/**
 * Session Timeout Manager
 * Manages session timeouts and provides notifications to users
 */

import { getSessionActivityTracker } from './session-activity-tracker';

export interface SessionTimeoutConfig {
  sessionDuration: number; // Total session duration in milliseconds
  warningTime: number; // Time before expiry to show warning (in milliseconds)
  checkInterval: number; // How often to check session status (in milliseconds)
  renewalGracePeriod: number; // Grace period for renewal (in milliseconds)
  autoRenewThreshold: number; // Auto-renew if user is active within this time (in milliseconds)
}

export interface SessionTimeoutState {
  isActive: boolean;
  timeRemaining: number;
  showWarning: boolean;
  isExpired: boolean;
  lastActivity: number;
  canRenew: boolean;
}

export interface SessionTimeoutCallbacks {
  onWarning?: (timeRemaining: number) => void;
  onExpiry?: () => void;
  onRenewal?: () => void;
  onActivityDetected?: () => void;
  onStateChange?: (state: SessionTimeoutState) => void;
}

/**
 * Default session timeout configuration
 */
export const DEFAULT_SESSION_CONFIG: SessionTimeoutConfig = {
  sessionDuration: 30 * 60 * 1000, // 30 minutes
  warningTime: 5 * 60 * 1000, // 5 minutes before expiry
  checkInterval: 30 * 1000, // Check every 30 seconds
  renewalGracePeriod: 2 * 60 * 1000, // 2 minutes grace period
  autoRenewThreshold: 5 * 60 * 1000 // Auto-renew if active within 5 minutes
};

/**
 * Session Timeout Manager Class
 */
export class SessionTimeoutManager {
  private config: SessionTimeoutConfig;
  private callbacks: SessionTimeoutCallbacks;
  private state: SessionTimeoutState;
  private checkTimer: NodeJS.Timeout | null = null;
  private activityTimer: NodeJS.Timeout | null = null;
  private sessionStartTime: number;
  private listeners: Set<(state: SessionTimeoutState) => void> = new Set();

  constructor(config: Partial<SessionTimeoutConfig> = {}, callbacks: SessionTimeoutCallbacks = {}) {
    this.config = { ...DEFAULT_SESSION_CONFIG, ...config };
    this.callbacks = callbacks;
    this.sessionStartTime = Date.now();

    this.state = {
      isActive: true,
      timeRemaining: this.config.sessionDuration,
      showWarning: false,
      isExpired: false,
      lastActivity: Date.now(),
      canRenew: true
    };

    this.setupActivityListeners();
    this.setupActivityTracker();
    this.startTimer();
  }

  /**
   * Get current session state
   */
  getState(): SessionTimeoutState {
    return { ...this.state };
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: SessionTimeoutState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Update session state and notify listeners
   */
  private updateState(updates: Partial<SessionTimeoutState>) {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
    this.callbacks.onStateChange?.(this.state);
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Error in session timeout listener:', error);
      }
    });
  }

  /**
   * Start the session timeout timer
   */
  private startTimer() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
    }

    this.checkTimer = setInterval(() => {
      this.checkSessionStatus();
    }, this.config.checkInterval);

    // Initial check
    this.checkSessionStatus();
  }

  /**
   * Check current session status
   */
  private checkSessionStatus() {
    const now = Date.now();
    const elapsed = now - this.sessionStartTime;
    const timeRemaining = Math.max(0, this.config.sessionDuration - elapsed);
    const shouldShowWarning = timeRemaining <= this.config.warningTime && timeRemaining > 0;
    const isExpired = timeRemaining <= 0;

    // Check if user has been active recently for auto-renewal
    const timeSinceActivity = now - this.state.lastActivity;
    const shouldAutoRenew = timeSinceActivity <= this.config.autoRenewThreshold && shouldShowWarning;

    if (shouldAutoRenew && this.state.canRenew) {
      this.renewSession();
      return;
    }

    this.updateState({
      timeRemaining,
      showWarning: shouldShowWarning,
      isExpired
    });

    // Trigger callbacks
    if (shouldShowWarning && !this.state.showWarning) {
      this.callbacks.onWarning?.(timeRemaining);
    }

    if (isExpired && !this.state.isExpired) {
      this.expireSession();
    }
  }

  /**
   * Setup activity tracker integration
   */
  private setupActivityTracker() {
    if (typeof window === 'undefined') return;

    const activityTracker = getSessionActivityTracker({
      idleThreshold: this.config.autoRenewThreshold
    }, {
      onActivity: () => {
        this.recordActivity();
      },
      onIdle: (idleDuration) => {
        // User has gone idle, but don't expire session immediately
        // Let the session timeout handle expiry
      }
    });

    // Store reference for cleanup
    (this as any).activityTracker = activityTracker;
  }

  /**
   * Record user activity
   */
  recordActivity() {
    const now = Date.now();
    this.updateState({
      lastActivity: now
    });
    this.callbacks.onActivityDetected?.();
  }

  /**
   * Renew the session
   */
  renewSession(): boolean {
    if (!this.state.canRenew) {
      return false;
    }

    this.sessionStartTime = Date.now();
    this.updateState({
      timeRemaining: this.config.sessionDuration,
      showWarning: false,
      isExpired: false,
      lastActivity: Date.now()
    });

    this.callbacks.onRenewal?.();
    return true;
  }

  /**
   * Expire the session
   */
  private expireSession() {
    this.updateState({
      isActive: false,
      isExpired: true,
      canRenew: false
    });

    this.stopTimer();
    this.callbacks.onExpiry?.();
  }

  /**
   * Extend session by specified duration
   */
  extendSession(additionalTime: number) {
    if (!this.state.isActive || this.state.isExpired) {
      return false;
    }

    this.sessionStartTime = Date.now() - (this.config.sessionDuration - this.state.timeRemaining - additionalTime);
    this.checkSessionStatus();
    return true;
  }

  /**
   * Stop the session timeout manager
   */
  stop() {
    this.stopTimer();
    this.removeActivityListeners();
  }

  /**
   * Stop the timer
   */
  private stopTimer() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
      this.activityTimer = null;
    }
  }

  /**
   * Setup activity listeners
   */
  private setupActivityListeners() {
    if (typeof window === 'undefined') return;

    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const throttledActivityHandler = this.throttle(() => {
      this.recordActivity();
    }, 1000); // Throttle to once per second

    activityEvents.forEach(event => {
      document.addEventListener(event, throttledActivityHandler, true);
    });

    // Store reference for cleanup
    (this as any).activityHandler = throttledActivityHandler;
    (this as any).activityEvents = activityEvents;
  }

  /**
   * Remove activity listeners
   */
  private removeActivityListeners() {
    if (typeof window === 'undefined') return;

    const activityEvents = (this as any).activityEvents;
    const activityHandler = (this as any).activityHandler;

    if (activityEvents && activityHandler) {
      activityEvents.forEach((event: string) => {
        document.removeEventListener(event, activityHandler, true);
      });
    }
  }

  /**
   * Throttle function to limit how often a function can be called
   */
  private throttle(func: Function, limit: number) {
    let inThrottle: boolean;
    return function(this: any, ...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Format time remaining for display
   */
  static formatTimeRemaining(milliseconds: number): string {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get time until warning
   */
  getTimeUntilWarning(): number {
    return Math.max(0, this.state.timeRemaining - this.config.warningTime);
  }

  /**
   * Check if session is about to expire
   */
  isAboutToExpire(): boolean {
    return this.state.showWarning && !this.state.isExpired;
  }

  /**
   * Get session progress as percentage
   */
  getSessionProgress(): number {
    const elapsed = this.config.sessionDuration - this.state.timeRemaining;
    return Math.min(100, (elapsed / this.config.sessionDuration) * 100);
  }
}

/**
 * Global session timeout manager instance
 */
let globalSessionManager: SessionTimeoutManager | null = null;

/**
 * Get or create global session timeout manager
 */
export function getSessionTimeoutManager(
  config?: Partial<SessionTimeoutConfig>,
  callbacks?: SessionTimeoutCallbacks
): SessionTimeoutManager {
  if (!globalSessionManager) {
    globalSessionManager = new SessionTimeoutManager(config, callbacks);
  }
  return globalSessionManager;
}

/**
 * Reset global session timeout manager
 */
export function resetSessionTimeoutManager() {
  if (globalSessionManager) {
    globalSessionManager.stop();
    globalSessionManager = null;
  }
}
