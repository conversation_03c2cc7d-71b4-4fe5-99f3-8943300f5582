/**
 * Skill Data Migrator
 * Handles migration of existing user skill data to new Skill Gap Analyzer schema
 * Provides backward compatibility and data validation
 */

import { prisma } from '@/lib/prisma';
import { SkillLevel, SkillAssessmentType, ExperienceLevel, PrismaClient } from '@prisma/client';

interface MigrationResult {
  migratedCount: number;
  skippedCount: number;
  errors: string[];
  createdSkills?: number;
}

interface LearningInsights {
  totalLearningTime: number;
  averageLearningVelocity: number;
  skillImprovementRate: number;
  averageCompletionTime: number;
  learningConsistency: number;
  recommendedConfidenceAdjustment: number;
}

interface ComprehensiveMigrationResult {
  userId: string;
  totalMigrated: number;
  userSkillProgress: MigrationResult;
  legacyAssessments: MigrationResult;
  interviewProgress: MigrationResult;
  learningInsights: LearningInsights;
  migrationDate: Date;
  success: boolean;
  errors?: string[];
}

interface MigrationStatus {
  userId: string;
  isMigrated: boolean;
  migrationDate: Date | null;
  dataSourcesMigrated: string[];
  totalRecordsMigrated: number;
  lastChecked: Date;
}

interface ValidationResult {
  userId: string;
  isValid: boolean;
  validationErrors: string[];
  dataConsistencyScore: number;
  recommendedActions: string[];
  validatedAt: Date;
}

interface DataInconsistency {
  type: 'RATING_MISMATCH' | 'MISSING_DATA' | 'DUPLICATE_ENTRY' | 'INVALID_VALUE';
  skillId: string;
  oldValue: any;
  newValue: any;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description?: string;
}

export class SkillDataMigrator {
  private prismaClient: any;

  private readonly SKILL_LEVEL_TO_RATING = {
    BEGINNER: 3,
    INTERMEDIATE: 6,
    ADVANCED: 8,
    EXPERT: 10,
  };

  private readonly CONFIDENCE_MULTIPLIER = {
    BEGINNER: 0.7,
    INTERMEDIATE: 0.8,
    ADVANCED: 0.9,
    EXPERT: 1.0,
  };

  constructor(prismaClient?: any) {
    this.prismaClient = prismaClient || prisma;
  }

  /**
   * Migrate UserSkillProgress to SkillAssessment format
   */
  async migrateUserSkillProgress(userId: string): Promise<MigrationResult> {
    try {
      // Get existing UserSkillProgress records
      const userSkillProgress = await this.prismaClient.userSkillProgress.findMany({
        where: { userId },
        include: { skill: true },
      });

      if (userSkillProgress.length === 0) {
        return { migratedCount: 0, skippedCount: 0, errors: [] };
      }

      // Check for existing SkillAssessments to avoid duplicates
      const existingAssessments = await this.prismaClient.skillAssessment.findMany({
        where: {
          userId,
          assessmentType: 'SELF_ASSESSMENT',
        },
        select: { skillId: true },
      });

      const existingSkillIds = new Set(existingAssessments.map(a => a.skillId));

      // Convert UserSkillProgress to SkillAssessment format
      const assessmentsToCreate = userSkillProgress
        .filter(progress => !existingSkillIds.has(progress.skillId))
        .map(progress => {
          const selfRating = progress.selfAssessment || 
            this.SKILL_LEVEL_TO_RATING[progress.currentLevel as keyof typeof this.SKILL_LEVEL_TO_RATING];
          
          const confidenceLevel = Math.round(
            selfRating * this.CONFIDENCE_MULTIPLIER[progress.currentLevel as keyof typeof this.CONFIDENCE_MULTIPLIER]
          );

          const notes = progress.selfAssessment 
            ? `Migrated from UserSkillProgress. Practice hours: ${progress.practiceHours}, Progress points: ${progress.progressPoints}`
            : `Migrated from UserSkillProgress. Derived from skill level: ${progress.currentLevel}. Practice hours: ${progress.practiceHours}`;

          return {
            userId,
            skillId: progress.skillId,
            selfRating: Math.min(Math.max(selfRating, 1), 10),
            confidenceLevel: Math.min(Math.max(confidenceLevel, 1), 10),
            assessmentType: 'SELF_ASSESSMENT' as SkillAssessmentType,
            assessmentDate: progress.lastPracticed || progress.updatedAt,
            notes,
            isActive: true,
          };
        });

      if (assessmentsToCreate.length === 0) {
        return { 
          migratedCount: 0, 
          skippedCount: userSkillProgress.length, 
          errors: [] 
        };
      }

      // Create new SkillAssessments
      const result = await this.prismaClient.skillAssessment.createMany({
        data: assessmentsToCreate,
        skipDuplicates: true,
      });

      return {
        migratedCount: result.count,
        skippedCount: userSkillProgress.length - result.count,
        errors: [],
      };

    } catch (error) {
      return {
        migratedCount: 0,
        skippedCount: 0,
        errors: [(error as Error).message],
      };
    }
  }

  /**
   * Migrate legacy Assessment responses to SkillAssessment
   */
  async migrateLegacyAssessments(userId: string): Promise<MigrationResult> {
    try {
      // Get completed assessments with responses
      const assessments = await this.prismaClient.assessment.findMany({
        where: {
          userId,
          status: 'COMPLETED',
        },
        include: {
          responses: true,
        },
      });

      const assessmentsToCreate = [];
      let createdSkills = 0;

      for (const assessment of assessments) {
        for (const response of assessment.responses) {
          // Extract skill name from questionKey (e.g., 'skill_javascript' -> 'javascript')
          const skillMatch = response.questionKey.match(/skill_(.+)/);
          if (!skillMatch) continue;

          const skillName = skillMatch[1].replace(/_/g, ' ');
          const capitalizedSkillName = skillName.charAt(0).toUpperCase() + skillName.slice(1);

          // Find or create skill
          let skill = await this.prismaClient.skill.findUnique({
            where: { name: capitalizedSkillName },
          });

          if (!skill) {
            skill = await this.prismaClient.skill.create({
              data: {
                name: capitalizedSkillName,
                category: 'Unknown',
                description: 'Skill created during migration',
              },
            });
            createdSkills++;
          }

          // Extract rating and confidence from response
          const answer = response.answer as any;
          const rating = answer.rating || answer.value || 5;
          const confidence = answer.confidence || rating;

          assessmentsToCreate.push({
            userId,
            skillId: skill.id,
            selfRating: Math.min(Math.max(rating, 1), 10),
            confidenceLevel: Math.min(Math.max(confidence, 1), 10),
            assessmentType: 'SELF_ASSESSMENT' as SkillAssessmentType,
            assessmentDate: assessment.completedAt || assessment.updatedAt,
            notes: `Migrated from legacy assessment (ID: ${assessment.id})`,
            isActive: true,
          });
        }
      }

      if (assessmentsToCreate.length === 0) {
        return { migratedCount: 0, skippedCount: 0, errors: [], createdSkills };
      }

      const result = await this.prismaClient.skillAssessment.createMany({
        data: assessmentsToCreate,
        skipDuplicates: true,
      });

      return {
        migratedCount: result.count,
        skippedCount: assessmentsToCreate.length - result.count,
        errors: [],
        createdSkills,
      };

    } catch (error) {
      return {
        migratedCount: 0,
        skippedCount: 0,
        errors: [(error as Error).message],
        createdSkills: 0,
      };
    }
  }

  /**
   * Extract learning insights from LearningAnalytics
   */
  async extractLearningInsights(userId: string): Promise<LearningInsights> {
    try {
      const analytics = await this.prismaClient.learningAnalytics.findMany({
        where: { userId },
        orderBy: { date: 'desc' },
        take: 30, // Last 30 days
      });

      if (analytics.length === 0) {
        return {
          totalLearningTime: 0,
          averageLearningVelocity: 0.5,
          skillImprovementRate: 0,
          averageCompletionTime: 0,
          learningConsistency: 0.5,
          recommendedConfidenceAdjustment: 1.0,
        };
      }

      const totalLearningTime = analytics.reduce((sum, a) => sum + a.timeSpent, 0);
      const averageLearningVelocity = analytics.reduce((sum, a) => sum + (a.learningVelocity || 0), 0) / analytics.length;
      const skillImprovementRate = analytics.reduce((sum, a) => sum + a.skillsImproved, 0) / analytics.length;
      const averageCompletionTime = analytics.reduce((sum, a) => sum + (a.avgCompletionTime || 0), 0) / analytics.length;
      
      // Calculate learning consistency (how regularly the user learns)
      const learningDays = analytics.filter(a => a.timeSpent > 0).length;
      const learningConsistency = learningDays / analytics.length;

      // Calculate confidence adjustment based on learning patterns
      const recommendedConfidenceAdjustment = Math.min(
        1.0 + (averageLearningVelocity * 0.3) + (learningConsistency * 0.2),
        1.5
      );

      return {
        totalLearningTime,
        averageLearningVelocity,
        skillImprovementRate,
        averageCompletionTime,
        learningConsistency,
        recommendedConfidenceAdjustment,
      };

    } catch (error) {
      console.error('Error extracting learning insights:', error);
      return {
        totalLearningTime: 0,
        averageLearningVelocity: 0.5,
        skillImprovementRate: 0,
        averageCompletionTime: 0,
        learningConsistency: 0.5,
        recommendedConfidenceAdjustment: 1.0,
      };
    }
  }

  /**
   * Apply learning insights to adjust skill assessment confidence
   */
  applyLearningInsights(assessment: any, insights: LearningInsights): any {
    const adjustedConfidence = Math.round(
      assessment.confidenceLevel * insights.recommendedConfidenceAdjustment
    );

    return {
      ...assessment,
      confidenceLevel: Math.min(Math.max(adjustedConfidence, 1), 10),
      notes: `${assessment.notes || ''} (confidence adjusted based on learning analytics)`.trim(),
    };
  }

  /**
   * Migrate interview progress to skill assessments
   */
  async migrateInterviewProgress(userId: string): Promise<MigrationResult> {
    try {
      const interviewProgress = await this.prismaClient.interviewProgress.findMany({
        where: { userId },
      });

      if (interviewProgress.length === 0) {
        return { migratedCount: 0, skippedCount: 0, errors: [] };
      }

      const assessmentsToCreate = [];

      for (const progress of interviewProgress) {
        const strengthAreas = (progress.strengthAreas as string[]) || [];
        const improvementAreas = (progress.improvementAreas as string[]) || [];
        
        // Create assessments for strength areas (higher ratings)
        for (const skillName of strengthAreas) {
          const skill = await this.findOrCreateSkill(skillName, 'Interview Skills');
          assessmentsToCreate.push({
            userId,
            skillId: skill.id,
            selfRating: 8,
            confidenceLevel: 8,
            assessmentType: 'PERFORMANCE_BASED' as SkillAssessmentType,
            assessmentDate: progress.lastPracticed || progress.updatedAt,
            notes: `Migrated from interview practice. Strength area. Sessions: ${progress.totalSessions}, Avg Score: ${progress.averageScore}`,
            isActive: true,
          });
        }

        // Create assessments for improvement areas (lower ratings)
        for (const skillName of improvementAreas) {
          const skill = await this.findOrCreateSkill(skillName, 'Interview Skills');
          assessmentsToCreate.push({
            userId,
            skillId: skill.id,
            selfRating: 5,
            confidenceLevel: 5,
            assessmentType: 'PERFORMANCE_BASED' as SkillAssessmentType,
            assessmentDate: progress.lastPracticed || progress.updatedAt,
            notes: `Migrated from interview practice. Improvement area. Sessions: ${progress.totalSessions}`,
            isActive: true,
          });
        }
      }

      if (assessmentsToCreate.length === 0) {
        return { migratedCount: 0, skippedCount: 0, errors: [] };
      }

      const result = await this.prismaClient.skillAssessment.createMany({
        data: assessmentsToCreate,
        skipDuplicates: true,
      });

      return {
        migratedCount: result.count,
        skippedCount: assessmentsToCreate.length - result.count,
        errors: [],
      };

    } catch (error) {
      return {
        migratedCount: 0,
        skippedCount: 0,
        errors: [(error as Error).message],
      };
    }
  }

  /**
   * Perform comprehensive migration for a user
   */
  async migrateUserData(userId: string): Promise<ComprehensiveMigrationResult> {
    try {
      return await this.prismaClient.$transaction(async (tx) => {
        // Extract learning insights first
        const learningInsights = await this.extractLearningInsights(userId);

        // Migrate all data sources
        const userSkillProgress = await this.migrateUserSkillProgress(userId);
        const legacyAssessments = await this.migrateLegacyAssessments(userId);
        const interviewProgress = await this.migrateInterviewProgress(userId);

        const totalMigrated = 
          userSkillProgress.migratedCount + 
          legacyAssessments.migratedCount + 
          interviewProgress.migratedCount;

        return {
          userId,
          totalMigrated,
          userSkillProgress,
          legacyAssessments,
          interviewProgress,
          learningInsights,
          migrationDate: new Date(),
          success: true,
        };
      });

    } catch (error) {
      return {
        userId,
        totalMigrated: 0,
        userSkillProgress: { migratedCount: 0, skippedCount: 0, errors: [] },
        legacyAssessments: { migratedCount: 0, skippedCount: 0, errors: [] },
        interviewProgress: { migratedCount: 0, skippedCount: 0, errors: [] },
        learningInsights: await this.extractLearningInsights(userId),
        migrationDate: new Date(),
        success: false,
        errors: [(error as Error).message],
      };
    }
  }

  /**
   * Get backward compatibility layer
   */
  getBackwardCompatibilityLayer() {
    return {
      async getUserSkillProgress(userId: string) {
        return await this.prismaClient.userSkillProgress.findMany({
          where: { userId },
          include: { skill: true },
        });
      },
    };
  }

  /**
   * Get migration status for a user
   */
  async getMigrationStatus(userId: string): Promise<MigrationStatus> {
    // This would typically be stored in a migration tracking table
    // For now, we'll check if the user has any SkillAssessments
    const assessmentCount = await this.prismaClient.skillAssessment.count({
      where: { userId },
    });

    return {
      userId,
      isMigrated: assessmentCount > 0,
      migrationDate: assessmentCount > 0 ? new Date() : null,
      dataSourcesMigrated: assessmentCount > 0 ? ['UserSkillProgress', 'LegacyAssessments'] : [],
      totalRecordsMigrated: assessmentCount,
      lastChecked: new Date(),
    };
  }

  /**
   * Validate migrated data integrity
   */
  async validateMigratedData(userId: string): Promise<ValidationResult> {
    const validationErrors: string[] = [];
    let dataConsistencyScore = 100;

    // Check for basic data integrity
    const assessments = await this.prismaClient.skillAssessment.findMany({
      where: { userId },
    });

    for (const assessment of assessments) {
      if (assessment.selfRating < 1 || assessment.selfRating > 10) {
        validationErrors.push(`Invalid self rating: ${assessment.selfRating} for skill ${assessment.skillId}`);
        dataConsistencyScore -= 10;
      }
      
      if (assessment.confidenceLevel < 1 || assessment.confidenceLevel > 10) {
        validationErrors.push(`Invalid confidence level: ${assessment.confidenceLevel} for skill ${assessment.skillId}`);
        dataConsistencyScore -= 10;
      }
    }

    const isValid = validationErrors.length === 0;
    const recommendedActions = isValid ? [] : ['Review and correct invalid ratings', 'Re-run migration if necessary'];

    return {
      userId,
      isValid,
      validationErrors,
      dataConsistencyScore: Math.max(dataConsistencyScore, 0),
      recommendedActions,
      validatedAt: new Date(),
    };
  }

  /**
   * Detect data inconsistencies between old and new data
   */
  async detectDataInconsistencies(userId: string): Promise<DataInconsistency[]> {
    const inconsistencies: DataInconsistency[] = [];

    // Compare UserSkillProgress with SkillAssessments
    const userSkillProgress = await this.prismaClient.userSkillProgress.findMany({
      where: { userId },
    });

    const skillAssessments = await this.prismaClient.skillAssessment.findMany({
      where: { userId, assessmentType: 'SELF_ASSESSMENT' },
    });

    for (const progress of userSkillProgress) {
      const assessment = skillAssessments.find(a => a.skillId === progress.skillId);
      if (assessment && progress.selfAssessment) {
        const ratingDiff = Math.abs(progress.selfAssessment - assessment.selfRating);
        if (ratingDiff > 2) {
          inconsistencies.push({
            type: 'RATING_MISMATCH',
            skillId: progress.skillId,
            oldValue: progress.selfAssessment,
            newValue: assessment.selfRating,
            severity: ratingDiff > 4 ? 'HIGH' : 'MEDIUM',
            description: `Rating difference of ${ratingDiff} points between old and new assessment`,
          });
        }
      }
    }

    return inconsistencies;
  }

  // Private helper methods

  private async findOrCreateSkill(skillName: string, category: string = 'General') {
    const capitalizedName = skillName.charAt(0).toUpperCase() + skillName.slice(1);
    
    return await this.prismaClient.skill.upsert({
      where: { name: capitalizedName },
      update: {},
      create: {
        name: capitalizedName,
        category,
        description: `Skill created during migration`,
      },
    });
  }
}
