import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  endpoint?: string;
  userAgent?: string;
  ip?: string;
  timestamp: string;
  requestId?: string;
}

export interface SecureErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
  timestamp: string;
  requestId?: string;
}

export class SecureErrorHandler {
  private static readonly GENERIC_ERROR_MESSAGE = 'An unexpected error occurred. Please try again later.';
  private static readonly VALIDATION_ERROR_MESSAGE = 'Invalid input data provided.';
  private static readonly AUTH_ERROR_MESSAGE = 'Authentication required.';
  private static readonly FORBIDDEN_ERROR_MESSAGE = 'Access denied.';
  private static readonly NOT_FOUND_ERROR_MESSAGE = 'Resource not found.';
  private static readonly RATE_LIMIT_ERROR_MESSAGE = 'Too many requests. Please try again later.';

  /**
   * Main error handling function that sanitizes errors for client response
   */
  static handleError(
    error: unknown,
    context: Partial<ErrorContext> = {},
    isDevelopment: boolean = process.env.NODE_ENV === 'development'
  ): SecureErrorResponse {
    const requestId = this.generateRequestId();
    const timestamp = new Date().toISOString();
    
    // Log the full error for debugging (server-side only)
    this.logError(error, { ...context, timestamp, requestId });

    // Determine error type and create sanitized response
    if (error instanceof ZodError) {
      return this.handleValidationError(error, requestId, timestamp, isDevelopment);
    }

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      return this.handlePrismaError(error, requestId, timestamp, isDevelopment);
    }

    if (error instanceof Prisma.PrismaClientValidationError) {
      return this.handlePrismaValidationError(error, requestId, timestamp, isDevelopment);
    }

    if (this.isAuthError(error)) {
      return this.createErrorResponse(
        this.AUTH_ERROR_MESSAGE,
        'AUTH_ERROR',
        requestId,
        timestamp,
        isDevelopment ? this.getErrorDetails(error) : undefined
      );
    }

    if (this.isForbiddenError(error)) {
      return this.createErrorResponse(
        this.FORBIDDEN_ERROR_MESSAGE,
        'FORBIDDEN',
        requestId,
        timestamp
      );
    }

    if (this.isNotFoundError(error)) {
      return this.createErrorResponse(
        this.NOT_FOUND_ERROR_MESSAGE,
        'NOT_FOUND',
        requestId,
        timestamp
      );
    }

    if (this.isRateLimitError(error)) {
      return this.createErrorResponse(
        this.RATE_LIMIT_ERROR_MESSAGE,
        'RATE_LIMIT',
        requestId,
        timestamp
      );
    }

    // Generic error for unknown types
    return this.createErrorResponse(
      this.GENERIC_ERROR_MESSAGE,
      'INTERNAL_ERROR',
      requestId,
      timestamp,
      isDevelopment ? this.getErrorDetails(error) : undefined
    );
  }

  /**
   * Handle Zod validation errors
   */
  private static handleValidationError(
    error: ZodError,
    requestId: string,
    timestamp: string,
    isDevelopment: boolean
  ): SecureErrorResponse {
    const sanitizedErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: this.sanitizeValidationMessage(err.message)
    }));

    return this.createErrorResponse(
      this.VALIDATION_ERROR_MESSAGE,
      'VALIDATION_ERROR',
      requestId,
      timestamp,
      isDevelopment ? sanitizedErrors : undefined
    );
  }

  /**
   * Handle Prisma database errors
   */
  private static handlePrismaError(
    error: Prisma.PrismaClientKnownRequestError,
    requestId: string,
    timestamp: string,
    isDevelopment: boolean
  ): SecureErrorResponse {
    switch (error.code) {
      case 'P2002':
        return this.createErrorResponse(
          'A record with this information already exists.',
          'DUPLICATE_ERROR',
          requestId,
          timestamp
        );
      case 'P2025':
        return this.createErrorResponse(
          this.NOT_FOUND_ERROR_MESSAGE,
          'NOT_FOUND',
          requestId,
          timestamp
        );
      case 'P2003':
        return this.createErrorResponse(
          'Invalid reference to related data.',
          'FOREIGN_KEY_ERROR',
          requestId,
          timestamp
        );
      case 'P2014':
        return this.createErrorResponse(
          'Invalid data relationship.',
          'RELATION_ERROR',
          requestId,
          timestamp
        );
      default:
        return this.createErrorResponse(
          'Database operation failed.',
          'DATABASE_ERROR',
          requestId,
          timestamp,
          isDevelopment ? { code: error.code, meta: error.meta } : undefined
        );
    }
  }

  /**
   * Handle Prisma validation errors
   */
  private static handlePrismaValidationError(
    error: Prisma.PrismaClientValidationError,
    requestId: string,
    timestamp: string,
    isDevelopment: boolean
  ): SecureErrorResponse {
    return this.createErrorResponse(
      'Invalid data format.',
      'VALIDATION_ERROR',
      requestId,
      timestamp,
      isDevelopment ? { message: error.message } : undefined
    );
  }

  /**
   * Create standardized error response
   */
  private static createErrorResponse(
    message: string,
    code: string,
    requestId: string,
    timestamp: string,
    details?: any
  ): SecureErrorResponse {
    const response: SecureErrorResponse = {
      success: false,
      error: message,
      code,
      timestamp,
      requestId
    };

    if (details) {
      response.details = details;
    }

    return response;
  }

  /**
   * Sanitize validation messages to prevent information disclosure
   */
  private static sanitizeValidationMessage(message: string): string {
    // Remove potentially sensitive information from validation messages
    return message
      .replace(/\b\d{4,}\b/g, '[REDACTED]') // Remove long numbers (could be IDs)
      .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]') // Remove emails
      .replace(/\b(?:https?:\/\/)?(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\/[^\s]*)?\b/g, '[URL]'); // Remove URLs
  }

  /**
   * Check if error is authentication related
   */
  private static isAuthError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return message.includes('unauthorized') || 
             message.includes('authentication') || 
             message.includes('token') ||
             message.includes('session');
    }
    return false;
  }

  /**
   * Check if error is forbidden/access related
   */
  private static isForbiddenError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return message.includes('forbidden') || 
             message.includes('access denied') || 
             message.includes('permission');
    }
    return false;
  }

  /**
   * Check if error is not found related
   */
  private static isNotFoundError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return message.includes('not found') || 
             message.includes('does not exist');
    }
    return false;
  }

  /**
   * Check if error is rate limiting related
   */
  private static isRateLimitError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return message.includes('rate limit') || 
             message.includes('too many requests');
    }
    return false;
  }

  /**
   * Extract safe error details for development
   */
  private static getErrorDetails(error: unknown): any {
    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5) // Limit stack trace
      };
    }
    return { error: String(error) };
  }

  /**
   * Generate unique request ID for tracking
   */
  private static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log error for monitoring (implement your logging strategy here)
   */
  private static logError(error: unknown, context: ErrorContext): void {
    const logEntry = {
      level: 'error',
      message: error instanceof Error ? error.message : String(error),
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      context,
      timestamp: context.timestamp
    };

    // In production, send to your logging service (e.g., Sentry, LogRocket, etc.)
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { contexts: { custom: context } });
      console.error('SECURE_ERROR_LOG:', JSON.stringify(logEntry));
    } else {
      console.error('Development Error:', logEntry);
    }
  }
}

/**
 * Express/Next.js middleware wrapper for secure error handling
 */
export function withSecureErrorHandling(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse> => {
    try {
      return await handler(request, ...args);
    } catch (error) {
      const context: Partial<ErrorContext> = {
        endpoint: request.nextUrl.pathname,
        userAgent: request.headers.get('user-agent') || undefined,
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      };

      const errorResponse = SecureErrorHandler.handleError(error, context);
      
      // Determine appropriate HTTP status code
      let statusCode = 500;
      switch (errorResponse.code) {
        case 'AUTH_ERROR':
          statusCode = 401;
          break;
        case 'FORBIDDEN':
          statusCode = 403;
          break;
        case 'NOT_FOUND':
          statusCode = 404;
          break;
        case 'VALIDATION_ERROR':
          statusCode = 400;
          break;
        case 'RATE_LIMIT':
          statusCode = 429;
          break;
        case 'DUPLICATE_ERROR':
          statusCode = 409;
          break;
      }

      return NextResponse.json(errorResponse, { status: statusCode });
    }
  };
}

export default SecureErrorHandler;
