/**
 * Database Query Optimization System
 * 
 * Provides intelligent query optimization, connection pooling,
 * and database performance monitoring for production environments.
 */

import { PrismaClient } from '@prisma/client';
import { UnifiedCachingService } from './unified-caching-service';

export interface QueryMetrics {
  queryId: string;
  query: string;
  executionTime: number;
  timestamp: number;
  success: boolean;
  rowsAffected?: number;
  cached: boolean;
  optimizationApplied?: string;
}

export interface DatabaseOptimization {
  id: string;
  type: 'INDEX_SUGGESTION' | 'QUERY_REWRITE' | 'CACHE_STRATEGY' | 'CONNECTION_POOL';
  description: string;
  impact: 'LOW' | 'MEDIUM' | 'HIGH';
  estimatedImprovement: number; // percentage
  query?: string;
  optimizedQuery?: string;
  applied: boolean;
  appliedAt?: number;
}

export interface DatabaseHealth {
  connectionPoolStatus: {
    active: number;
    idle: number;
    total: number;
    utilization: number;
  };
  queryPerformance: {
    averageExecutionTime: number;
    slowQueries: number;
    totalQueries: number;
    cacheHitRate: number;
  };
  optimizations: {
    total: number;
    applied: number;
    pending: number;
    estimatedImprovementTotal: number;
  };
  healthScore: number;
  recommendations: string[];
}

export class DatabaseOptimizer {
  private queryMetrics: QueryMetrics[] = [];
  private optimizations: DatabaseOptimization[] = [];
  private slowQueryThreshold: number = 1000; // 1 second
  private cacheableQueryPatterns: RegExp[] = [];

  constructor() {
    this.initializeCacheablePatterns();
  }

  /**
   * Initialize patterns for queries that should be cached
   */
  private initializeCacheablePatterns(): void {
    this.cacheableQueryPatterns = [
      // SELECT queries without WHERE clauses (static data)
      /^SELECT\s+.*\s+FROM\s+\w+\s*(?:ORDER\s+BY|LIMIT|$)/i,
      
      // Lookup queries with simple WHERE clauses
      /^SELECT\s+.*\s+FROM\s+\w+\s+WHERE\s+\w+\s*=\s*\$\d+/i,
      
      // Count queries
      /^SELECT\s+COUNT\(/i,
      
      // Career paths and resources (relatively static)
      /SELECT.*FROM.*career_paths/i,
      /SELECT.*FROM.*learning_resources/i,
      /SELECT.*FROM.*categories/i,
    ];
  }

  /**
   * Record query execution metrics
   */
  recordQuery(
    query: string,
    executionTime: number,
    success: boolean,
    rowsAffected?: number,
    cached: boolean = false,
    optimizationApplied?: string
  ): void {
    const queryId = this.generateQueryId(query);
    
    const metrics: QueryMetrics = {
      queryId,
      query: this.sanitizeQuery(query),
      executionTime,
      timestamp: Date.now(),
      success,
      rowsAffected,
      cached,
      optimizationApplied
    };

    this.queryMetrics.push(metrics);

    // Keep only last 1000 query metrics
    if (this.queryMetrics.length > 1000) {
      this.queryMetrics = this.queryMetrics.slice(-1000);
    }

    // Analyze for optimization opportunities
    if (executionTime > this.slowQueryThreshold && success) {
      this.analyzeSlowQuery(metrics);
    }

    // Check if query should be cached
    if (this.shouldCacheQuery(query) && !cached) {
      this.suggestCacheStrategy(metrics);
    }
  }

  /**
   * Generate unique query ID for tracking
   */
  private generateQueryId(query: string): string {
    // Normalize query by removing parameters and whitespace
    const normalized = query
      .replace(/\$\d+/g, '?')
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase();
    
    // Generate hash-like ID
    let hash = 0;
    for (let i = 0; i < normalized.length; i++) {
      const char = normalized.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Sanitize query for logging (remove sensitive data)
   */
  private sanitizeQuery(query: string): string {
    return query
      .replace(/password\s*=\s*'[^']*'/gi, "password = '[REDACTED]'")
      .replace(/email\s*=\s*'[^']*'/gi, "email = '[REDACTED]'")
      .substring(0, 500); // Limit length
  }

  /**
   * Analyze slow query for optimization opportunities
   */
  private analyzeSlowQuery(metrics: QueryMetrics): void {
    const optimizations: DatabaseOptimization[] = [];

    // Suggest index for WHERE clauses
    const whereMatch = metrics.query.match(/WHERE\s+(\w+)\s*=/i);
    if (whereMatch) {
      const column = whereMatch[1];
      optimizations.push({
        id: `index-${metrics.queryId}-${Date.now()}`,
        type: 'INDEX_SUGGESTION',
        description: `Consider adding index on column '${column}' to improve query performance`,
        impact: 'HIGH',
        estimatedImprovement: 60,
        query: metrics.query,
        applied: false
      });
    }

    // Suggest query rewrite for complex JOINs
    if (metrics.query.includes('JOIN') && metrics.executionTime > 2000) {
      optimizations.push({
        id: `rewrite-${metrics.queryId}-${Date.now()}`,
        type: 'QUERY_REWRITE',
        description: 'Complex JOIN query detected - consider query optimization or denormalization',
        impact: 'MEDIUM',
        estimatedImprovement: 40,
        query: metrics.query,
        applied: false
      });
    }

    // Suggest LIMIT for large result sets
    if (!metrics.query.includes('LIMIT') && metrics.rowsAffected && metrics.rowsAffected > 1000) {
      const optimizedQuery = metrics.query + ' LIMIT 100';
      optimizations.push({
        id: `limit-${metrics.queryId}-${Date.now()}`,
        type: 'QUERY_REWRITE',
        description: 'Large result set detected - consider adding LIMIT clause',
        impact: 'MEDIUM',
        estimatedImprovement: 30,
        query: metrics.query,
        optimizedQuery,
        applied: false
      });
    }

    this.optimizations.push(...optimizations);
  }

  /**
   * Check if query should be cached
   */
  private shouldCacheQuery(query: string): boolean {
    // Don't cache write operations
    if (/^(INSERT|UPDATE|DELETE|CREATE|DROP|ALTER)/i.test(query)) {
      return false;
    }

    // Check against cacheable patterns
    return this.cacheableQueryPatterns.some(pattern => pattern.test(query));
  }

  /**
   * Suggest cache strategy for query
   */
  private suggestCacheStrategy(metrics: QueryMetrics): void {
    let ttl = 300000; // 5 minutes default
    let impact: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM';

    // Adjust TTL based on query type
    if (metrics.query.includes('career_paths') || metrics.query.includes('learning_resources')) {
      ttl = 1800000; // 30 minutes for relatively static data
      impact = 'HIGH';
    } else if (metrics.query.includes('COUNT(')) {
      ttl = 600000; // 10 minutes for count queries
      impact = 'MEDIUM';
    } else if (metrics.query.includes('user') || metrics.query.includes('session')) {
      ttl = 60000; // 1 minute for user-specific data
      impact = 'LOW';
    }

    const optimization: DatabaseOptimization = {
      id: `cache-${metrics.queryId}-${Date.now()}`,
      type: 'CACHE_STRATEGY',
      description: `Cache query results with TTL of ${ttl / 1000} seconds`,
      impact,
      estimatedImprovement: impact === 'HIGH' ? 70 : impact === 'MEDIUM' ? 50 : 30,
      query: metrics.query,
      applied: false
    };

    this.optimizations.push(optimization);
  }

  /**
   * Create optimized query wrapper with caching
   */
  createOptimizedQuery<T>(
    queryFn: () => Promise<T>,
    cacheKey: string,
    ttl: number = 300000
  ): () => Promise<T> {
    return async (): Promise<T> => {
      const startTime = Date.now();
      
      try {
        // Check cache first
        const cached = UnifiedCachingService.get<T>(cacheKey);
        if (cached !== null) {
          this.recordQuery(
            `CACHED: ${cacheKey}`,
            Date.now() - startTime,
            true,
            undefined,
            true
          );
          return cached;
        }

        // Execute query
        const result = await queryFn();
        const executionTime = Date.now() - startTime;

        // Cache result
        UnifiedCachingService.set(cacheKey, result, { ttl });

        this.recordQuery(
          `EXECUTED: ${cacheKey}`,
          executionTime,
          true,
          Array.isArray(result) ? result.length : 1,
          false,
          'caching-applied'
        );

        return result;
      } catch (error) {
        const executionTime = Date.now() - startTime;
        this.recordQuery(
          `ERROR: ${cacheKey}`,
          executionTime,
          false
        );
        throw error;
      }
    };
  }

  /**
   * Get database health status
   */
  getDatabaseHealth(): DatabaseHealth {
    const recentMetrics = this.queryMetrics.filter(m => Date.now() - m.timestamp < 300000); // Last 5 minutes
    
    // Calculate query performance metrics
    const totalQueries = recentMetrics.length;
    const slowQueries = recentMetrics.filter(m => m.executionTime > this.slowQueryThreshold).length;
    const cachedQueries = recentMetrics.filter(m => m.cached).length;
    const averageExecutionTime = totalQueries > 0 
      ? recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / totalQueries
      : 0;
    const cacheHitRate = totalQueries > 0 ? (cachedQueries / totalQueries) * 100 : 0;

    // Calculate optimization metrics
    const totalOptimizations = this.optimizations.length;
    const appliedOptimizations = this.optimizations.filter(o => o.applied).length;
    const pendingOptimizations = totalOptimizations - appliedOptimizations;
    const estimatedImprovementTotal = this.optimizations
      .filter(o => !o.applied)
      .reduce((sum, o) => sum + o.estimatedImprovement, 0);

    // Calculate health score
    let healthScore = 100;
    if (averageExecutionTime > this.slowQueryThreshold) healthScore -= 30;
    if (slowQueries > totalQueries * 0.1) healthScore -= 20; // More than 10% slow queries
    if (cacheHitRate < 50) healthScore -= 20;
    if (pendingOptimizations > 5) healthScore -= 15;

    healthScore = Math.max(0, healthScore);

    // Generate recommendations
    const recommendations: string[] = [];
    if (averageExecutionTime > this.slowQueryThreshold) {
      recommendations.push('Average query execution time is high - review slow queries');
    }
    if (cacheHitRate < 50) {
      recommendations.push('Cache hit rate is low - implement more aggressive caching');
    }
    if (pendingOptimizations > 0) {
      recommendations.push(`${pendingOptimizations} optimization suggestions pending review`);
    }
    if (slowQueries > 0) {
      recommendations.push(`${slowQueries} slow queries detected in last 5 minutes`);
    }

    return {
      connectionPoolStatus: {
        active: 5, // Simulated - would be actual pool metrics
        idle: 3,
        total: 8,
        utilization: 62.5
      },
      queryPerformance: {
        averageExecutionTime,
        slowQueries,
        totalQueries,
        cacheHitRate
      },
      optimizations: {
        total: totalOptimizations,
        applied: appliedOptimizations,
        pending: pendingOptimizations,
        estimatedImprovementTotal
      },
      healthScore,
      recommendations
    };
  }

  /**
   * Get optimization suggestions
   */
  getOptimizationSuggestions(): DatabaseOptimization[] {
    return this.optimizations
      .filter(o => !o.applied)
      .sort((a, b) => {
        // Sort by impact and estimated improvement
        const impactWeight = { HIGH: 3, MEDIUM: 2, LOW: 1 };
        const aScore = impactWeight[a.impact] * a.estimatedImprovement;
        const bScore = impactWeight[b.impact] * b.estimatedImprovement;
        return bScore - aScore;
      })
      .slice(0, 10); // Top 10 suggestions
  }

  /**
   * Apply optimization
   */
  applyOptimization(optimizationId: string): boolean {
    const optimization = this.optimizations.find(o => o.id === optimizationId);
    if (!optimization || optimization.applied) {
      return false;
    }

    optimization.applied = true;
    optimization.appliedAt = Date.now();

    console.log(`✅ Applied optimization: ${optimization.description}`);
    return true;
  }

  /**
   * Generate database optimization report
   */
  generateOptimizationReport(): string {
    const health = this.getDatabaseHealth();
    const suggestions = this.getOptimizationSuggestions();

    let report = `
🗄️ DATABASE OPTIMIZATION REPORT
===============================

Health Score: ${health.healthScore}/100 ${health.healthScore >= 80 ? '✅' : health.healthScore >= 60 ? '⚠️' : '🚨'}

Query Performance (Last 5 minutes):
- Total Queries: ${health.queryPerformance.totalQueries}
- Average Execution Time: ${health.queryPerformance.averageExecutionTime.toFixed(1)}ms
- Slow Queries: ${health.queryPerformance.slowQueries}
- Cache Hit Rate: ${health.queryPerformance.cacheHitRate.toFixed(1)}%

Connection Pool:
- Active Connections: ${health.connectionPoolStatus.active}
- Idle Connections: ${health.connectionPoolStatus.idle}
- Total Connections: ${health.connectionPoolStatus.total}
- Utilization: ${health.connectionPoolStatus.utilization.toFixed(1)}%

Optimizations:
- Total Suggestions: ${health.optimizations.total}
- Applied: ${health.optimizations.applied}
- Pending: ${health.optimizations.pending}
- Estimated Total Improvement: ${health.optimizations.estimatedImprovementTotal.toFixed(1)}%

`;

    if (health.recommendations.length > 0) {
      report += `Recommendations:
`;
      health.recommendations.forEach(rec => {
        report += `- ${rec}\n`;
      });
      report += '\n';
    }

    if (suggestions.length > 0) {
      report += `Top Optimization Suggestions:
`;
      suggestions.slice(0, 5).forEach((opt, index) => {
        report += `${index + 1}. ${opt.description} (${opt.impact} impact, ${opt.estimatedImprovement}% improvement)\n`;
      });
    }

    return report;
  }
}

// Export singleton instance
export const databaseOptimizer = new DatabaseOptimizer();
