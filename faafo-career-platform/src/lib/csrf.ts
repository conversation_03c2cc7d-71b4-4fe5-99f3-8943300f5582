import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth';

// Global persistent token storage for CSRF tokens
declare global {
  var __csrfTokens: Map<string, { token: string; expiresAt: number }> | undefined;
}

// Use global variable to persist across requests in development
const csrfTokens = globalThis.__csrfTokens ?? new Map<string, { token: string; expiresAt: number }>();
if (process.env.NODE_ENV === 'development') {
  globalThis.__csrfTokens = csrfTokens;
}

export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export async function getCSRFToken(request: NextRequest): Promise<string> {
  // Get user session to create a unique identifier
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  console.log('🔑 CSRF Token Generation:', {
    hasSession: !!session,
    userId: userId?.substring(0, 10) + '...' || 'none',
    userEmail: session?.user?.email || 'none'
  });

  if (!userId) {
    // For non-authenticated users, use IP address
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'anonymous';
    const identifier = `guest_${ip}`;

    const existing = csrfTokens.get(identifier);
    if (existing && existing.expiresAt > Date.now()) {
      console.log('🔄 Returning existing guest token');
      return existing.token;
    }

    const token = generateCSRFToken();
    csrfTokens.set(identifier, {
      token,
      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
    });

    console.log('🆕 Generated new guest token:', { identifier, tokenCount: csrfTokens.size });
    return token;
  }

  // For authenticated users, use user ID
  const existing = csrfTokens.get(userId);
  if (existing && existing.expiresAt > Date.now()) {
    console.log('🔄 Returning existing user token');
    return existing.token;
  }

  const token = generateCSRFToken();
  csrfTokens.set(userId, {
    token,
    expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
  });

  console.log('🆕 Generated new user token:', { userId: userId.substring(0, 10) + '...', tokenCount: csrfTokens.size });
  return token;
}

export async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {
  console.log('🔍 CSRF Validation Debug:', {
    token: token?.substring(0, 10) + '...',
    method: request.method,
    url: request.url
  });

  // Get user session to create a unique identifier
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  console.log('👤 Session Info:', {
    hasSession: !!session,
    userId: userId?.substring(0, 10) + '...' || 'none',
    userEmail: session?.user?.email || 'none'
  });

  if (!userId) {
    // For non-authenticated users, use IP address
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'anonymous';
    const identifier = `guest_${ip}`;

    console.log('🌐 Guest validation:', { identifier, tokenCount: csrfTokens.size });

    const stored = csrfTokens.get(identifier);
    if (stored && stored.expiresAt > Date.now() && stored.token === token) {
      console.log('✅ Guest CSRF token valid');
      return true;
    }
    console.log('❌ Guest CSRF token invalid or expired');
    return false;
  }

  // For authenticated users, use user ID
  console.log('🔐 User validation:', { userId: userId.substring(0, 10) + '...', tokenCount: csrfTokens.size });

  const stored = csrfTokens.get(userId);
  if (stored && stored.expiresAt > Date.now() && stored.token === token) {
    console.log('✅ User CSRF token valid');
    return true;
  }

  console.log('❌ User CSRF token invalid or expired', {
    hasStored: !!stored,
    isExpired: stored ? stored.expiresAt <= Date.now() : 'no-token',
    tokenMatch: stored ? stored.token === token : 'no-token'
  });

  return false;
}

export async function withCSRFProtection(
  request: NextRequest,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  // Skip CSRF for GET requests
  if (request.method === 'GET') {
    return handler();
  }
  
  // Get CSRF token from header or body
  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('csrf-token');
  
  if (!csrfToken) {
    return NextResponse.json(
      { error: 'CSRF token missing' },
      { status: 403 }
    );
  }
  
  const isValid = await validateCSRFToken(request, csrfToken);
  if (!isValid) {
    return NextResponse.json(
      { error: 'Invalid CSRF token' },
      { status: 403 }
    );
  }
  
  return handler();
}

// API endpoint to get CSRF token
export async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {
  const token = await getCSRFToken(request);

  return NextResponse.json(
    {
      success: true,
      csrfToken: token,
      timestamp: Date.now()
    },
    {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache'
      }
    }
  );
}

// Cleanup expired tokens periodically
setInterval(() => {
  const now = Date.now();
  Array.from(csrfTokens.entries()).forEach(([key, value]) => {
    if (value.expiresAt <= now) {
      csrfTokens.delete(key);
    }
  });
}, 15 * 60 * 1000); // Clean up every 15 minutes
