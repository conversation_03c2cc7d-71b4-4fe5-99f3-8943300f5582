/**
 * Enhanced Question Generation Service
 * Provides sophisticated, context-aware interview question selection and generation
 */

interface QuestionContext {
  sessionType: string;
  interviewType?: string;
  careerPath?: string;
  experienceLevel?: string;
  specificRole?: string;
  companyType?: string;
  industryFocus?: string;
  focusAreas?: string[];
  difficulty?: string;
  count: number;
}

interface QuestionWeight {
  base: number;
  contextMultiplier: number;
  diversityBonus: number;
  difficultyMatch: number;
  priorityBonus: number;
}

interface EnhancedQuestion {
  questionText: string;
  questionType: string;
  category: string;
  difficulty: string;
  expectedDuration: number;
  context: string;
  hints: any;
  followUpQuestions?: string[];
  industrySpecific: boolean;
  tags: string[];
  isRequired: boolean;
  priority: number;
  weight?: number;
  contextRelevance?: number;
}

export class EnhancedQuestionGenerator {
  /**
   * Generate contextually relevant questions using advanced selection algorithm
   */
  static generateQuestions(
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): EnhancedQuestion[] {
    // Step 1: Build candidate pool with context-aware weighting
    const candidatePool = this.buildCandidatePool(questionBanks, context);
    
    // Step 2: Apply sophisticated scoring algorithm
    const scoredQuestions = this.scoreQuestions(candidatePool, context);
    
    // Step 3: Select optimal question mix
    const selectedQuestions = this.selectOptimalMix(scoredQuestions, context);
    
    // Step 4: Ensure diversity and balance
    const balancedQuestions = this.ensureDiversityAndBalance(selectedQuestions, context);
    
    // Step 5: Final ordering and preparation
    return this.finalizeQuestions(balancedQuestions, context);
  }

  /**
   * Build candidate pool based on context
   */
  private static buildCandidatePool(
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): EnhancedQuestion[] {
    const candidates: EnhancedQuestion[] = [];
    
    // Always include core behavioral questions
    if (questionBanks.BEHAVIORAL_CORE) {
      candidates.push(...questionBanks.BEHAVIORAL_CORE);
    }

    // Add questions based on session type
    this.addQuestionsBySessionType(candidates, questionBanks, context);
    
    // Add questions based on interview type
    this.addQuestionsByInterviewType(candidates, questionBanks, context);
    
    // Add questions based on career path and role
    this.addQuestionsByCareerContext(candidates, questionBanks, context);
    
    // Add questions based on focus areas
    this.addQuestionsByFocusAreas(candidates, questionBanks, context);
    
    // Add industry-specific questions
    this.addQuestionsByIndustry(candidates, questionBanks, context);
    
    // Remove duplicates while preserving the best version of each question
    return this.deduplicateQuestions(candidates);
  }

  /**
   * Add questions based on session type with enhanced logic
   */
  private static addQuestionsBySessionType(
    candidates: EnhancedQuestion[],
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): void {
    const sessionTypeMapping = {
      'TECHNICAL_PRACTICE': ['TECHNICAL', 'PROBLEM_SOLVING'],
      'BEHAVIORAL_PRACTICE': ['COMMUNICATION', 'SITUATIONAL'],
      'MOCK_INTERVIEW': ['BEHAVIORAL_CORE', 'TECHNICAL', 'COMMUNICATION'],
      'QUICK_PRACTICE': ['BEHAVIORAL_CORE']
    };

    const relevantBanks = sessionTypeMapping[context.sessionType as keyof typeof sessionTypeMapping] || ['BEHAVIORAL_CORE'];
    
    for (const bankName of relevantBanks) {
      if (questionBanks[bankName]) {
        candidates.push(...questionBanks[bankName]);
      }
    }
  }

  /**
   * Add questions based on interview type
   */
  private static addQuestionsByInterviewType(
    candidates: EnhancedQuestion[],
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): void {
    const interviewTypeMapping = {
      'PANEL': ['COMMUNICATION', 'LEADERSHIP'],
      'GROUP': ['TEAMWORK', 'COMMUNICATION'],
      'TECHNICAL_SCREEN': ['TECHNICAL', 'PROBLEM_SOLVING'],
      'PHONE': ['COMMUNICATION', 'BEHAVIORAL_CORE'],
      'VIDEO': ['COMMUNICATION', 'BEHAVIORAL_CORE'],
      'IN_PERSON': ['BEHAVIORAL_CORE', 'SITUATIONAL']
    };

    if (context.interviewType) {
      const relevantBanks = interviewTypeMapping[context.interviewType as keyof typeof interviewTypeMapping] || [];
      for (const bankName of relevantBanks) {
        if (questionBanks[bankName]) {
          candidates.push(...questionBanks[bankName]);
        }
      }
    }
  }

  /**
   * Add questions based on career path and specific role
   */
  private static addQuestionsByCareerContext(
    candidates: EnhancedQuestion[],
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): void {
    // Leadership roles
    if (this.isLeadershipRole(context.careerPath, context.specificRole)) {
      if (questionBanks.LEADERSHIP) {
        candidates.push(...questionBanks.LEADERSHIP);
      }
    }

    // Technical roles
    if (this.isTechnicalRole(context.careerPath, context.specificRole)) {
      if (questionBanks.TECHNICAL) {
        candidates.push(...questionBanks.TECHNICAL);
      }
      if (questionBanks.PROBLEM_SOLVING) {
        candidates.push(...questionBanks.PROBLEM_SOLVING);
      }
    }

    // Customer-facing roles
    if (this.isCustomerFacingRole(context.careerPath, context.specificRole)) {
      if (questionBanks.COMMUNICATION) {
        candidates.push(...questionBanks.COMMUNICATION);
      }
      if (questionBanks.CUSTOMER_SERVICE) {
        candidates.push(...questionBanks.CUSTOMER_SERVICE);
      }
    }

    // Senior level roles
    if (this.isSeniorLevel(context.experienceLevel, context.specificRole)) {
      if (questionBanks.LEADERSHIP) {
        candidates.push(...questionBanks.LEADERSHIP);
      }
      if (questionBanks.STRATEGY) {
        candidates.push(...questionBanks.STRATEGY);
      }
    }
  }

  /**
   * Add questions based on focus areas with enhanced matching
   */
  private static addQuestionsByFocusAreas(
    candidates: EnhancedQuestion[],
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): void {
    if (!context.focusAreas || context.focusAreas.length === 0) return;

    const focusAreaMapping = {
      'leadership': ['LEADERSHIP', 'MANAGEMENT'],
      'communication': ['COMMUNICATION'],
      'problem-solving': ['PROBLEM_SOLVING', 'ANALYTICAL_THINKING'],
      'technical': ['TECHNICAL'],
      'teamwork': ['TEAMWORK', 'COMMUNICATION'],
      'adaptability': ['SITUATIONAL'],
      'creativity': ['PROBLEM_SOLVING'],
      'customer-service': ['CUSTOMER_SERVICE', 'COMMUNICATION'],
      'sales': ['SALES', 'COMMUNICATION'],
      'strategy': ['STRATEGY', 'LEADERSHIP'],
      'ethics': ['ETHICS'],
      'culture': ['COMPANY_CULTURE']
    };

    for (const focusArea of context.focusAreas) {
      const normalizedArea = focusArea.toLowerCase().replace(/\s+/g, '-');
      const relevantBanks = focusAreaMapping[normalizedArea as keyof typeof focusAreaMapping] || [];
      
      for (const bankName of relevantBanks) {
        if (questionBanks[bankName]) {
          candidates.push(...questionBanks[bankName]);
        }
      }
    }
  }

  /**
   * Add industry-specific questions
   */
  private static addQuestionsByIndustry(
    candidates: EnhancedQuestion[],
    questionBanks: Record<string, EnhancedQuestion[]>,
    context: QuestionContext
  ): void {
    if (!context.industryFocus) return;

    // Add industry-specific questions if available
    const industryBankName = `INDUSTRY_${context.industryFocus.toUpperCase().replace(/\s+/g, '_')}`;
    if (questionBanks[industryBankName]) {
      candidates.push(...questionBanks[industryBankName]);
    }

    // Mark existing questions as industry-relevant
    candidates.forEach(question => {
      if (this.isIndustryRelevant(question, context.industryFocus!)) {
        question.industrySpecific = true;
      }
    });
  }

  /**
   * Score questions based on context relevance
   */
  private static scoreQuestions(
    questions: EnhancedQuestion[],
    context: QuestionContext
  ): EnhancedQuestion[] {
    return questions.map(question => {
      const weightObj = this.calculateQuestionWeight(question, context);
      const totalWeight = weightObj.base * weightObj.contextMultiplier *
                         weightObj.difficultyMatch + weightObj.priorityBonus;
      return {
        ...question,
        weight: totalWeight,
        contextRelevance: weightObj.contextMultiplier
      };
    });
  }

  /**
   * Calculate sophisticated question weight
   */
  private static calculateQuestionWeight(
    question: EnhancedQuestion,
    context: QuestionContext
  ): QuestionWeight {
    let base = 1.0;
    let contextMultiplier = 1.0;
    let diversityBonus = 0.0;
    let difficultyMatch = 1.0;
    let priorityBonus = 0.0;

    // Base weight from question priority
    base = question.priority ? (6 - question.priority) / 5 : 0.5;

    // Context relevance multiplier
    contextMultiplier = this.calculateContextRelevance(question, context);

    // Difficulty match bonus
    if (context.difficulty && question.difficulty === context.difficulty) {
      difficultyMatch = 1.2;
    } else if (context.difficulty) {
      const difficultyOrder = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];
      const contextIndex = difficultyOrder.indexOf(context.difficulty);
      const questionIndex = difficultyOrder.indexOf(question.difficulty);
      const distance = Math.abs(contextIndex - questionIndex);
      difficultyMatch = Math.max(0.7, 1.0 - (distance * 0.1));
    }

    // Priority bonus for required questions
    if (question.isRequired) {
      priorityBonus = 0.5;
    }

    // Industry-specific bonus
    if (question.industrySpecific && context.industryFocus) {
      contextMultiplier *= 1.3;
    }

    return {
      base,
      contextMultiplier,
      diversityBonus,
      difficultyMatch,
      priorityBonus
    };
  }

  /**
   * Calculate context relevance score
   */
  private static calculateContextRelevance(
    question: EnhancedQuestion,
    context: QuestionContext
  ): number {
    let relevance = 1.0;

    // Session type relevance
    if (this.isQuestionRelevantToSessionType(question, context.sessionType)) {
      relevance *= 1.4;
    }

    // Interview type relevance
    if (context.interviewType && this.isQuestionRelevantToInterviewType(question, context.interviewType)) {
      relevance *= 1.2;
    }

    // Focus area relevance
    if (context.focusAreas && this.isQuestionRelevantToFocusAreas(question, context.focusAreas)) {
      relevance *= 1.3;
    }

    // Career path relevance
    if (context.careerPath && this.isQuestionRelevantToCareerPath(question, context.careerPath)) {
      relevance *= 1.2;
    }

    return relevance;
  }

  // Helper methods for role and context detection
  private static isLeadershipRole(careerPath?: string, specificRole?: string): boolean {
    const leadershipKeywords = ['manager', 'director', 'lead', 'supervisor', 'head', 'chief', 'vp', 'president'];
    const text = `${careerPath || ''} ${specificRole || ''}`.toLowerCase();
    return leadershipKeywords.some(keyword => text.includes(keyword));
  }

  private static isTechnicalRole(careerPath?: string, specificRole?: string): boolean {
    const technicalKeywords = ['developer', 'engineer', 'programmer', 'architect', 'analyst', 'scientist', 'technician'];
    const text = `${careerPath || ''} ${specificRole || ''}`.toLowerCase();
    return technicalKeywords.some(keyword => text.includes(keyword));
  }

  private static isCustomerFacingRole(careerPath?: string, specificRole?: string): boolean {
    const customerKeywords = ['sales', 'support', 'service', 'account', 'customer', 'client', 'representative'];
    const text = `${careerPath || ''} ${specificRole || ''}`.toLowerCase();
    return customerKeywords.some(keyword => text.includes(keyword));
  }

  private static isSeniorLevel(experienceLevel?: string, specificRole?: string): boolean {
    const seniorKeywords = ['senior', 'principal', 'staff', 'lead', 'architect'];
    const text = `${experienceLevel || ''} ${specificRole || ''}`.toLowerCase();
    return text.includes('senior') || text.includes('executive') || 
           seniorKeywords.some(keyword => text.includes(keyword));
  }

  private static isIndustryRelevant(question: EnhancedQuestion, industry: string): boolean {
    const questionText = question.questionText.toLowerCase();
    const industryKeywords = this.getIndustryKeywords(industry);
    return industryKeywords.some(keyword => questionText.includes(keyword));
  }

  private static getIndustryKeywords(industry: string): string[] {
    const industryKeywordMap: Record<string, string[]> = {
      'technology': ['software', 'tech', 'digital', 'platform', 'system', 'code', 'development'],
      'healthcare': ['patient', 'medical', 'health', 'clinical', 'treatment', 'care'],
      'finance': ['financial', 'investment', 'banking', 'risk', 'portfolio', 'market'],
      'education': ['student', 'learning', 'curriculum', 'teaching', 'academic'],
      'retail': ['customer', 'sales', 'merchandise', 'inventory', 'store'],
      'manufacturing': ['production', 'quality', 'process', 'efficiency', 'safety']
    };
    
    return industryKeywordMap[industry.toLowerCase()] || [];
  }

  // Additional helper methods for relevance checking
  private static isQuestionRelevantToSessionType(question: EnhancedQuestion, sessionType: string): boolean {
    const sessionTypeKeywords: Record<string, string[]> = {
      'TECHNICAL_PRACTICE': ['technical', 'problem', 'solution', 'code', 'system'],
      'BEHAVIORAL_PRACTICE': ['behavior', 'situation', 'experience', 'team', 'challenge'],
      'MOCK_INTERVIEW': ['general', 'common', 'typical', 'standard']
    };
    
    const keywords = sessionTypeKeywords[sessionType] || [];
    const questionText = question.questionText.toLowerCase();
    return keywords.some(keyword => questionText.includes(keyword));
  }

  private static isQuestionRelevantToInterviewType(question: EnhancedQuestion, interviewType: string): boolean {
    // Implementation for interview type relevance
    return true; // Simplified for now
  }

  private static isQuestionRelevantToFocusAreas(question: EnhancedQuestion, focusAreas: string[]): boolean {
    // Implementation for focus area relevance
    return true; // Simplified for now
  }

  private static isQuestionRelevantToCareerPath(question: EnhancedQuestion, careerPath: string): boolean {
    // Implementation for career path relevance
    return true; // Simplified for now
  }

  private static deduplicateQuestions(questions: EnhancedQuestion[]): EnhancedQuestion[] {
    const seen = new Map<string, EnhancedQuestion>();
    
    for (const question of questions) {
      const key = question.questionText.toLowerCase().trim();
      if (!seen.has(key) || (seen.get(key)!.priority || 10) > (question.priority || 10)) {
        seen.set(key, question);
      }
    }
    
    return Array.from(seen.values());
  }

  private static selectOptimalMix(questions: EnhancedQuestion[], context: QuestionContext): EnhancedQuestion[] {
    // Sort by total weight (all factors combined)
    const sortedQuestions = questions.sort((a, b) => {
      const aWeight = a.weight || 0;
      const bWeight = b.weight || 0;
      return bWeight - aWeight;
    });

    // Select required questions first
    const required = sortedQuestions.filter(q => q.isRequired).slice(0, Math.min(context.count, 5));
    const remaining = context.count - required.length;
    
    // Select best optional questions
    const optional = sortedQuestions.filter(q => !q.isRequired).slice(0, remaining);
    
    return [...required, ...optional];
  }

  private static ensureDiversityAndBalance(questions: EnhancedQuestion[], context: QuestionContext): EnhancedQuestion[] {
    // Ensure we have a good mix of question types and categories
    const typeDistribution = new Map<string, number>();
    const categoryDistribution = new Map<string, number>();
    
    const balanced: EnhancedQuestion[] = [];
    const remaining = [...questions];
    
    // Add questions while maintaining diversity
    while (balanced.length < context.count && remaining.length > 0) {
      let bestQuestion = remaining[0];
      let bestScore = this.calculateDiversityScore(bestQuestion, balanced, typeDistribution, categoryDistribution);
      
      for (let i = 1; i < Math.min(remaining.length, 5); i++) {
        const score = this.calculateDiversityScore(remaining[i], balanced, typeDistribution, categoryDistribution);
        if (score > bestScore) {
          bestQuestion = remaining[i];
          bestScore = score;
        }
      }
      
      balanced.push(bestQuestion);
      typeDistribution.set(bestQuestion.questionType, (typeDistribution.get(bestQuestion.questionType) || 0) + 1);
      categoryDistribution.set(bestQuestion.category, (categoryDistribution.get(bestQuestion.category) || 0) + 1);
      
      const index = remaining.indexOf(bestQuestion);
      remaining.splice(index, 1);
    }
    
    return balanced;
  }

  private static calculateDiversityScore(
    question: EnhancedQuestion,
    selected: EnhancedQuestion[],
    typeDistribution: Map<string, number>,
    categoryDistribution: Map<string, number>
  ): number {
    let score = question.weight || 0;
    
    // Penalize over-representation of types and categories
    const typeCount = typeDistribution.get(question.questionType) || 0;
    const categoryCount = categoryDistribution.get(question.category) || 0;
    
    score *= Math.max(0.3, 1.0 - (typeCount * 0.2));
    score *= Math.max(0.3, 1.0 - (categoryCount * 0.15));
    
    return score;
  }

  private static finalizeQuestions(questions: EnhancedQuestion[], context: QuestionContext): EnhancedQuestion[] {
    // Apply final difficulty adjustment
    return questions.map(question => ({
      ...question,
      difficulty: context.difficulty || question.difficulty,
      // Remove internal scoring fields
      weight: undefined,
      contextRelevance: undefined
    }));
  }
}

export default EnhancedQuestionGenerator;
