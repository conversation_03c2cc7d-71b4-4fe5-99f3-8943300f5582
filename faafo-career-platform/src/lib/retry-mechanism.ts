/**
 * Retry Mechanism System
 * Provides intelligent retry logic for failed operations
 */

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // Base delay in milliseconds
  maxDelay: number; // Maximum delay in milliseconds
  backoffMultiplier: number; // Exponential backoff multiplier
  retryableErrors?: string[]; // Specific error codes that should trigger retry
  nonRetryableErrors?: string[]; // Error codes that should NOT trigger retry
  onRetry?: (attempt: number, error: any) => void;
  onMaxAttemptsReached?: (error: any) => void;
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: any;
  attempts: number;
  totalTime: number;
}

/**
 * Default retry configurations for different operation types
 */
export const DEFAULT_RETRY_CONFIGS: Record<string, RetryConfig> = {
  // Network operations
  network: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', '500', '502', '503', '504'],
    nonRetryableErrors: ['401', '403', '404', 'VALIDATION_ERROR']
  },

  // API calls
  api: {
    maxAttempts: 3,
    baseDelay: 500,
    maxDelay: 5000,
    backoffMultiplier: 1.5,
    retryableErrors: ['500', '502', '503', '504', 'TIMEOUT'],
    nonRetryableErrors: ['400', '401', '403', '404', '422']
  },

  // Database operations
  database: {
    maxAttempts: 2,
    baseDelay: 2000,
    maxDelay: 8000,
    backoffMultiplier: 2,
    retryableErrors: ['DATABASE_ERROR', 'CONNECTION_ERROR', 'TIMEOUT'],
    nonRetryableErrors: ['VALIDATION_ERROR', 'CONSTRAINT_VIOLATION']
  },

  // File operations
  file: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffMultiplier: 1.5,
    retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'TEMPORARY_FAILURE'],
    nonRetryableErrors: ['FILE_NOT_FOUND', 'PERMISSION_DENIED', 'INVALID_FORMAT']
  },

  // Authentication operations
  auth: {
    maxAttempts: 2,
    baseDelay: 1000,
    maxDelay: 3000,
    backoffMultiplier: 1.5,
    retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', '500', '502', '503'],
    nonRetryableErrors: ['INVALID_CREDENTIALS', 'ACCOUNT_LOCKED', '401', '403']
  },

  // User interactions
  user: {
    maxAttempts: 5,
    baseDelay: 500,
    maxDelay: 3000,
    backoffMultiplier: 1.2,
    retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', '500', '502', '503'],
    nonRetryableErrors: ['VALIDATION_ERROR', '400', '401', '403', '404']
  }
};

/**
 * Retry mechanism class
 */
export class RetryMechanism {
  private config: RetryConfig;

  constructor(config: Partial<RetryConfig> = {}) {
    this.config = {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffMultiplier: 2,
      ...config
    };
  }

  /**
   * Execute an operation with retry logic
   */
  async execute<T>(
    operation: () => Promise<T>,
    operationType?: keyof typeof DEFAULT_RETRY_CONFIGS
  ): Promise<RetryResult<T>> {
    const config = operationType 
      ? { ...DEFAULT_RETRY_CONFIGS[operationType], ...this.config }
      : this.config;

    const startTime = Date.now();
    let lastError: any;
    let attempts = 0;

    for (attempts = 1; attempts <= config.maxAttempts; attempts++) {
      try {
        const result = await operation();
        return {
          success: true,
          data: result,
          attempts,
          totalTime: Date.now() - startTime
        };
      } catch (error) {
        lastError = error;
        
        // Check if this error should trigger a retry
        if (!this.shouldRetry(error, config, attempts)) {
          break;
        }

        // Call retry callback
        config.onRetry?.(attempts, error);

        // Wait before retrying (except on last attempt)
        if (attempts < config.maxAttempts) {
          const delay = this.calculateDelay(attempts, config);
          await this.sleep(delay);
        }
      }
    }

    // Max attempts reached or non-retryable error
    config.onMaxAttemptsReached?.(lastError);

    return {
      success: false,
      error: lastError,
      attempts,
      totalTime: Date.now() - startTime
    };
  }

  /**
   * Determine if an error should trigger a retry
   */
  private shouldRetry(error: any, config: RetryConfig, attempt: number): boolean {
    // Don't retry if we've reached max attempts
    if (attempt >= config.maxAttempts) {
      return false;
    }

    const errorCode = this.extractErrorCode(error);

    // Check non-retryable errors first
    if (config.nonRetryableErrors?.includes(errorCode)) {
      return false;
    }

    // Check retryable errors
    if (config.retryableErrors?.includes(errorCode)) {
      return true;
    }

    // Default behavior: retry on network/server errors
    return this.isRetryableByDefault(errorCode);
  }

  /**
   * Extract error code from error object
   */
  private extractErrorCode(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error && typeof error === 'object') {
      return error.code || error.status?.toString() || error.name || 'UNKNOWN_ERROR';
    }

    return 'UNKNOWN_ERROR';
  }

  /**
   * Default retryable error detection
   */
  private isRetryableByDefault(errorCode: string): boolean {
    const retryablePatterns = [
      /^5\d{2}$/, // 5xx server errors
      /^NETWORK/i,
      /^TIMEOUT/i,
      /^CONNECTION/i,
      /^TEMPORARY/i
    ];

    return retryablePatterns.some(pattern => pattern.test(errorCode));
  }

  /**
   * Calculate delay for exponential backoff
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5); // Add jitter
    return Math.min(jitteredDelay, config.maxDelay);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a retryable version of a function
   */
  static createRetryableFunction<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    config?: Partial<RetryConfig>,
    operationType?: keyof typeof DEFAULT_RETRY_CONFIGS
  ): (...args: T) => Promise<R> {
    const retryMechanism = new RetryMechanism(config);

    return async (...args: T): Promise<R> => {
      const result = await retryMechanism.execute(() => fn(...args), operationType);
      
      if (result.success) {
        return result.data!;
      } else {
        throw result.error;
      }
    };
  }

  /**
   * Retry a fetch request with intelligent error handling
   */
  static async retryFetch(
    url: string,
    options?: RequestInit,
    config?: Partial<RetryConfig>
  ): Promise<Response> {
    const retryMechanism = new RetryMechanism(config);

    const result = await retryMechanism.execute(async () => {
      const response = await fetch(url, options);
      
      // Consider non-2xx responses as errors for retry logic
      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        (error as any).status = response.status;
        (error as any).response = response;
        throw error;
      }
      
      return response;
    }, 'network');

    if (result.success) {
      return result.data!;
    } else {
      throw result.error;
    }
  }

  /**
   * Create a retry wrapper for React components
   */
  static createRetryWrapper<T>(
    operation: () => Promise<T>,
    options: {
      operationType?: keyof typeof DEFAULT_RETRY_CONFIGS;
      config?: Partial<RetryConfig>;
      onRetry?: (attempt: number, error: any) => void;
      onSuccess?: (data: T, attempts: number) => void;
      onFailure?: (error: any, attempts: number) => void;
    } = {}
  ) {
    return async (): Promise<T> => {
      const retryMechanism = new RetryMechanism({
        ...options.config,
        onRetry: options.onRetry,
        onMaxAttemptsReached: options.onFailure ? (error) => options.onFailure!(error, 0) : undefined
      });

      const result = await retryMechanism.execute(operation, options.operationType);

      if (result.success) {
        options.onSuccess?.(result.data!, result.attempts);
        return result.data!;
      } else {
        options.onFailure?.(result.error, result.attempts);
        throw result.error;
      }
    };
  }
}

/**
 * Convenience functions for common retry scenarios
 */

// Retry API calls
export const retryApiCall = <T>(
  apiCall: () => Promise<T>,
  config?: Partial<RetryConfig>
): Promise<T> => {
  return RetryMechanism.createRetryableFunction(apiCall, config, 'api')();
};

// Retry network requests
export const retryNetworkRequest = <T>(
  request: () => Promise<T>,
  config?: Partial<RetryConfig>
): Promise<T> => {
  return RetryMechanism.createRetryableFunction(request, config, 'network')();
};

// Retry database operations
export const retryDatabaseOperation = <T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>
): Promise<T> => {
  return RetryMechanism.createRetryableFunction(operation, config, 'database')();
};

// Retry user operations
export const retryUserOperation = <T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>
): Promise<T> => {
  return RetryMechanism.createRetryableFunction(operation, config, 'user')();
};
