/**
 * Shared type definitions for salary calculator
 * This file ensures consistency between frontend and backend
 */

// Base enums for validation
export const EXPERIENCE_LEVELS = ['entry', 'junior', 'mid', 'senior', 'lead', 'principal', 'executive'] as const;
export const EDUCATION_LEVELS = ['high_school', 'associate', 'bachelor', 'master', 'phd', 'bootcamp', 'self_taught'] as const;
export const COMPANY_SIZES = ['startup', 'small', 'medium', 'large', 'enterprise'] as const;

export type ExperienceLevel = typeof EXPERIENCE_LEVELS[number];
export type EducationLevel = typeof EDUCATION_LEVELS[number];
export type CompanySize = typeof COMPANY_SIZES[number];

// Form data interface
export interface SalaryCalculatorForm {
  careerPath: string;
  experienceLevel: ExperienceLevel;
  location: string;
  skills: string[];
  education: EducationLevel;
  companySize: CompanySize;
  industry: string;
}

// Factor details for enhanced results
export interface FactorDetail {
  multiplier: number;
  impact: string;
  description?: string;
}

export interface SkillsDetail {
  bonus: number;
  impact: string;
  relevantSkills: string[];
  description?: string;
}

// Unified salary calculation result interface
export interface SalaryCalculationResult {
  baseRange: { min: number; max: number };
  adjustedRange: { min: number; max: number };
  median: number;
  factors: {
    location: FactorDetail;
    experience: FactorDetail;
    skills: SkillsDetail;
    education: FactorDetail;
    companySize: FactorDetail;
  };
  confidence: number;
  dataPoints: number;
  dataSource: string;
  marketInsights: {
    demand: string;
    growth: string;
    topSkills: string[];
    recommendations: string[];
  };
  comparisons: {
    percentile25: number;
    percentile75: number;
    nationalAverage: number;
  };
  metadata: {
    calculatedAt: string;
    version: string;
    currency: string;
  };
}

// API response wrapper
export interface SalaryCalculationResponse {
  success: boolean;
  data?: SalaryCalculationResult;
  error?: string;
  message?: string;
  details?: any;
}

// Career path data structure
export interface CareerPathData {
  min: number;
  max: number;
  growth: string;
  demand: 'high' | 'medium' | 'low';
  skills: string[];
  description?: string;
}

// Location data structure
export interface LocationData {
  multiplier: number;
  impact: string;
  costOfLiving?: number;
  marketSize?: string;
}

// Experience data structure
export interface ExperienceData {
  multiplier: number;
  impact: string;
  description?: string;
}

// Company size data structure
export interface CompanySizeData {
  multiplier: number;
  impact: string;
  description?: string;
}

// Education data structure
export interface EducationData {
  multiplier: number;
  impact: string;
  description?: string;
}

// Validation schemas (for runtime validation)
export const SALARY_FORM_VALIDATION = {
  careerPath: {
    required: true,
    minLength: 1,
    message: 'Career path is required'
  },
  experienceLevel: {
    required: true,
    enum: EXPERIENCE_LEVELS,
    message: 'Valid experience level is required'
  },
  location: {
    required: true,
    minLength: 1,
    message: 'Location is required'
  },
  skills: {
    required: false,
    type: 'array',
    maxItems: 20,
    message: 'Maximum 20 skills allowed'
  },
  education: {
    required: false,
    enum: EDUCATION_LEVELS,
    default: 'bachelor',
    message: 'Valid education level required'
  },
  companySize: {
    required: false,
    enum: COMPANY_SIZES,
    default: 'medium',
    message: 'Valid company size required'
  },
  industry: {
    required: false,
    type: 'string',
    maxLength: 100,
    default: 'technology',
    message: 'Industry must be less than 100 characters'
  }
} as const;

// Error types
export interface SalaryCalculatorError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

// Configuration constants
export const SALARY_CALCULATOR_CONFIG = {
  MAX_SKILLS_BONUS: 0.25, // 25% maximum skills bonus
  SKILLS_BONUS_PER_SKILL: 0.05, // 5% per relevant skill
  BASE_CONFIDENCE: 25,
  MAX_CONFIDENCE: 95,
  DEFAULT_DATA_POINTS: 1000,
  CURRENCY: 'USD',
  VERSION: '2.0.0'
} as const;

// Utility types for frontend display
export interface DisplayFactor {
  label: string;
  value: number;
  percentage: string;
  impact: string;
  isPositive: boolean;
}

export interface SalaryDisplayData {
  range: string;
  median: string;
  confidence: {
    value: number;
    level: 'high' | 'medium' | 'low';
    color: string;
  };
  factors: DisplayFactor[];
  recommendations: string[];
}
