// Type declaration to resolve TypeScript error for lru-cache transitive dependency
// This file provides minimal type definitions for lru-cache to prevent TypeScript errors
// when it's included as a transitive dependency

declare module 'lru-cache' {
  class LRUCache<K = any, V = any> {
    constructor(options?: any);
    set(key: K, value: V, maxAge?: number): boolean;
    get(key: K): V | undefined;
    peek(key: K): V | undefined;
    del(key: K): void;
    reset(): void;
    has(key: K): boolean;
    forEach<T = this>(callbackfn: (this: T, value: V, key: K, cache: this) => void, thisArg?: T): void;
    keys(): K[];
    values(): V[];
    length: number;
    itemCount: number;
    dump(): Array<{ k: K; v: V; e?: number }>;
    load(cacheEntriesArray: Array<{ k: K; v: V; e?: number }>): void;
    prune(): void;
  }

  export = LRUCache;
}
