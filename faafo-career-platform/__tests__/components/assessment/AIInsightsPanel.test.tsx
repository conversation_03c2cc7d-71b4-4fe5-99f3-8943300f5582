import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AIInsightsPanel } from '@/components/assessment/AIInsightsPanel';

// Mock the AI service
jest.mock('@/lib/ai/geminiService', () => ({
  generateInsights: jest.fn(),
}));

// Mock the tabs component
jest.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, ...props }: any) => <div data-testid="tabs" {...props}>{children}</div>,
  TabsContent: ({ children, ...props }: any) => <div data-testid="tabs-content" {...props}>{children}</div>,
  TabsList: ({ children, ...props }: any) => <div data-testid="tabs-list" {...props}>{children}</div>,
  TabsTrigger: ({ children, ...props }: any) => <button data-testid="tabs-trigger" {...props}>{children}</button>,
}));

// Mock other UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div data-testid="card" {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div data-testid="card-content" {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div data-testid="card-header" {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 data-testid="card-title" {...props}>{children}</h3>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button data-testid="button" onClick={onClick} {...props}>{children}</button>
  ),
}));

jest.mock('@/components/ui/alert', () => ({
  Alert: ({ children, ...props }: any) => <div data-testid="alert" {...props}>{children}</div>,
  AlertDescription: ({ children, ...props }: any) => <div data-testid="alert-description" {...props}>{children}</div>,
}));

const mockAssessmentData = {
  responses: {
    'career-interests': ['technology', 'problem-solving'],
    'work-style': ['collaborative', 'analytical'],
    'skills': ['programming', 'communication'],
    'values': ['innovation', 'work-life-balance'],
  },
  scores: {
    technology: 85,
    business: 60,
    creative: 45,
  },
  careerPaths: [
    { id: '1', title: 'Software Developer', match: 90 },
    { id: '2', title: 'Data Scientist', match: 85 },
  ],
};

const mockAIInsights = {
  personalityAnalysis: {
    workStyle: 'Analytical and collaborative',
    strengths: ['Problem-solving', 'Technical skills'],
    preferences: ['Remote work', 'Team collaboration'],
    confidence: 0.85,
  },
  careerFitAnalysis: {
    topMatches: [
      { career: 'Software Developer', fit: 90, reasoning: 'Strong technical skills' },
    ],
    considerations: ['Consider specialization in AI/ML'],
    confidence: 0.88,
  },
  skillGapAnalysis: {
    currentStrengths: ['Programming', 'Problem-solving'],
    skillGaps: ['Cloud computing', 'DevOps'],
    hiddenStrengths: ['Leadership potential'],
    confidence: 0.82,
  },
  learningRecommendations: {
    learningStyle: 'Visual and hands-on',
    optimalSchedule: '2-3 hours daily',
    recommendedResources: ['Online courses', 'Practice projects'],
    confidence: 0.80,
  },
  marketTrends: {
    emergingSkills: ['AI/ML', 'Cloud computing'],
    industryGrowth: 'High demand in tech sector',
    salaryTrends: 'Increasing for specialized roles',
    confidence: 0.75,
  },
};

describe('AIInsightsPanel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);
    
    expect(screen.getByText(/generating ai insights/i)).toBeInTheDocument();
    expect(screen.getByTestId('tabs')).toBeInTheDocument();
  });

  it('displays AI insights after loading', async () => {
    const { generateInsights } = require('@/lib/ai/geminiService');
    generateInsights.mockResolvedValue(mockAIInsights);

    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);

    await waitFor(() => {
      expect(screen.getByText(/personality analysis/i)).toBeInTheDocument();
    });

    expect(screen.getByText('Analytical and collaborative')).toBeInTheDocument();
    expect(screen.getByText('Problem-solving')).toBeInTheDocument();
  });

  it('handles AI service errors gracefully', async () => {
    const { generateInsights } = require('@/lib/ai/geminiService');
    generateInsights.mockRejectedValue(new Error('AI service unavailable'));

    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);

    await waitFor(() => {
      expect(screen.getByText(/unable to generate ai insights/i)).toBeInTheDocument();
    });

    expect(screen.getByTestId('alert')).toBeInTheDocument();
  });

  it('allows retrying after error', async () => {
    const { generateInsights } = require('@/lib/ai/geminiService');
    generateInsights
      .mockRejectedValueOnce(new Error('AI service unavailable'))
      .mockResolvedValueOnce(mockAIInsights);

    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);

    await waitFor(() => {
      expect(screen.getByText(/unable to generate ai insights/i)).toBeInTheDocument();
    });

    const retryButton = screen.getByText(/try again/i);
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText(/personality analysis/i)).toBeInTheDocument();
    });
  });

  it('displays confidence scores for each insight', async () => {
    const { generateInsights } = require('@/lib/ai/geminiService');
    generateInsights.mockResolvedValue(mockAIInsights);

    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);

    await waitFor(() => {
      expect(screen.getByText('85%')).toBeInTheDocument(); // Personality confidence
      expect(screen.getByText('88%')).toBeInTheDocument(); // Career fit confidence
    });
  });

  it('switches between different insight tabs', async () => {
    const { generateInsights } = require('@/lib/ai/geminiService');
    generateInsights.mockResolvedValue(mockAIInsights);

    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);

    await waitFor(() => {
      expect(screen.getByText(/personality analysis/i)).toBeInTheDocument();
    });

    // Check that all tab triggers are present
    const tabTriggers = screen.getAllByTestId('tabs-trigger');
    expect(tabTriggers).toHaveLength(5); // 5 insight categories
  });

  it('handles missing assessment data gracefully', () => {
    render(<AIInsightsPanel assessmentData={null} />);
    
    expect(screen.getByText(/assessment data required/i)).toBeInTheDocument();
  });

  it('displays skill gap analysis correctly', async () => {
    const { generateInsights } = require('@/lib/ai/geminiService');
    generateInsights.mockResolvedValue(mockAIInsights);

    render(<AIInsightsPanel assessmentData={mockAssessmentData} />);

    await waitFor(() => {
      expect(screen.getByText('Programming')).toBeInTheDocument();
      expect(screen.getByText('Cloud computing')).toBeInTheDocument();
      expect(screen.getByText('Leadership potential')).toBeInTheDocument();
    });
  });
});
