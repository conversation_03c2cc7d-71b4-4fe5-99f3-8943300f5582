// Mock the feature flag provider before importing
const mockFeatureFlagProvider = {
  getFlag: jest.fn(),
  setFlag: jest.fn(),
  getAllFlags: jest.fn(),
  evaluateFlag: jest.fn(),
  trackFlagUsage: jest.fn(),
  getABTestVariant: jest.fn(),
  getUserSegment: jest.fn(),
  updateUserContext: jest.fn(),
};

jest.mock('@/lib/feature-flags/feature-flag-provider', () => ({
  FeatureFlagProvider: jest.fn().mockImplementation(() => mockFeatureFlagProvider),
}));

import { SkillGapFeatureFlags } from '@/lib/feature-flags/skill-gap-feature-flags';

describe('Skill Gap Analyzer Feature Flags - TDD', () => {
  let skillGapFeatureFlags: SkillGapFeatureFlags;

  beforeEach(() => {
    jest.clearAllMocks();
    skillGapFeatureFlags = new SkillGapFeatureFlags();
  });

  describe('Core Feature Flags', () => {
    it('should check if skill gap analyzer is enabled for user', async () => {
      // Test: Basic feature flag check for skill gap analyzer
      const userId = 'user-123';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isSkillGapAnalyzerEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'skill_gap_analyzer_enabled',
        userId,
        false // default value
      );
      expect(isEnabled).toBe(true);
    });

    it('should check if comprehensive analysis is enabled', async () => {
      // Test: Feature flag for comprehensive analysis feature
      const userId = 'user-456';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(false);

      const isEnabled = await skillGapFeatureFlags.isComprehensiveAnalysisEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'comprehensive_analysis_enabled',
        userId,
        false
      );
      expect(isEnabled).toBe(false);
    });

    it('should check if AI-powered recommendations are enabled', async () => {
      // Test: Feature flag for AI recommendations
      const userId = 'user-789';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isAIRecommendationsEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'ai_recommendations_enabled',
        userId,
        true // default enabled
      );
      expect(isEnabled).toBe(true);
    });

    it('should check if market data integration is enabled', async () => {
      // Test: Feature flag for market data features
      const userId = 'user-market';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(false);

      const isEnabled = await skillGapFeatureFlags.isMarketDataEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'market_data_integration',
        userId,
        false
      );
      expect(isEnabled).toBe(false);
    });
  });

  describe('A/B Testing Features', () => {
    it('should get A/B test variant for skill assessment UI', async () => {
      // Test: A/B testing for different UI variants
      const userId = 'user-ab-test';
      const testName = 'skill_assessment_ui_v2';
      mockFeatureFlagProvider.getABTestVariant.mockResolvedValue('variant_b');

      const variant = await skillGapFeatureFlags.getSkillAssessmentUIVariant(userId);

      expect(mockFeatureFlagProvider.getABTestVariant).toHaveBeenCalledWith(
        testName,
        userId,
        ['control', 'variant_a', 'variant_b'],
        'control' // default
      );
      expect(variant).toBe('variant_b');
    });

    it('should get A/B test variant for analysis algorithm', async () => {
      // Test: A/B testing for different analysis algorithms
      const userId = 'user-algo-test';
      const testName = 'analysis_algorithm_v3';
      mockFeatureFlagProvider.getABTestVariant.mockResolvedValue('enhanced_ai');

      const variant = await skillGapFeatureFlags.getAnalysisAlgorithmVariant(userId);

      expect(mockFeatureFlagProvider.getABTestVariant).toHaveBeenCalledWith(
        testName,
        userId,
        ['standard', 'enhanced_ai', 'hybrid'],
        'standard'
      );
      expect(variant).toBe('enhanced_ai');
    });

    it('should get A/B test variant for recommendation engine', async () => {
      // Test: A/B testing for recommendation algorithms
      const userId = 'user-rec-test';
      mockFeatureFlagProvider.getABTestVariant.mockResolvedValue('ml_enhanced');

      const variant = await skillGapFeatureFlags.getRecommendationEngineVariant(userId);

      expect(mockFeatureFlagProvider.getABTestVariant).toHaveBeenCalledWith(
        'recommendation_engine_v2',
        userId,
        ['basic', 'ml_enhanced', 'collaborative'],
        'basic'
      );
      expect(variant).toBe('ml_enhanced');
    });
  });

  describe('Gradual Rollout Features', () => {
    it('should check rollout percentage for new features', async () => {
      // Test: Gradual rollout based on user percentage
      const userId = 'user-rollout';
      mockFeatureFlagProvider.evaluateFlag.mockImplementation((flagName, userId, defaultValue) => {
        // Simulate 25% rollout
        const userHash = userId.charCodeAt(0) % 100;
        return Promise.resolve(userHash < 25);
      });

      const isEnabled = await skillGapFeatureFlags.isNewSkillMatchingEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'new_skill_matching_algorithm',
        userId,
        false
      );
    });

    it('should handle beta features for specific user segments', async () => {
      // Test: Beta features for premium users
      const userId = 'premium-user-123';
      mockFeatureFlagProvider.getUserSegment.mockResolvedValue('premium');
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isBetaFeaturesEnabled(userId);

      expect(mockFeatureFlagProvider.getUserSegment).toHaveBeenCalledWith(userId);
      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'beta_features_enabled',
        userId,
        false
      );
      expect(isEnabled).toBe(true);
    });

    it('should handle enterprise features for organization users', async () => {
      // Test: Enterprise features for organization accounts
      const userId = 'org-user-456';
      const orgId = 'org-123';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isEnterpriseAnalyticsEnabled(userId, orgId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'enterprise_analytics',
        userId,
        false,
        { organizationId: orgId }
      );
      expect(isEnabled).toBe(true);
    });
  });

  describe('Performance and Optimization Flags', () => {
    it('should check if caching optimization is enabled', async () => {
      // Test: Performance optimization flags
      const userId = 'user-perf';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isCachingOptimizationEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'caching_optimization_v2',
        userId,
        false
      );
      expect(isEnabled).toBe(true);
    });

    it('should check if parallel processing is enabled', async () => {
      // Test: Parallel processing optimization
      const userId = 'user-parallel';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(false);

      const isEnabled = await skillGapFeatureFlags.isParallelProcessingEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'parallel_processing_enabled',
        userId,
        false
      );
      expect(isEnabled).toBe(false);
    });

    it('should check if advanced AI models are enabled', async () => {
      // Test: Advanced AI model usage
      const userId = 'user-ai-advanced';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isAdvancedAIModelsEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'advanced_ai_models',
        userId,
        false
      );
      expect(isEnabled).toBe(true);
    });
  });

  describe('User Experience Flags', () => {
    it('should check if enhanced UI is enabled', async () => {
      // Test: Enhanced UI features
      const userId = 'user-ui';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isEnhancedUIEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'enhanced_ui_v3',
        userId,
        false
      );
      expect(isEnabled).toBe(true);
    });

    it('should check if interactive tutorials are enabled', async () => {
      // Test: Interactive tutorial features
      const userId = 'user-tutorial';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(false);

      const isEnabled = await skillGapFeatureFlags.isInteractiveTutorialsEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'interactive_tutorials',
        userId,
        true // default enabled for better UX
      );
      expect(isEnabled).toBe(false);
    });

    it('should check if real-time collaboration is enabled', async () => {
      // Test: Real-time collaboration features
      const userId = 'user-collab';
      mockFeatureFlagProvider.evaluateFlag.mockResolvedValue(true);

      const isEnabled = await skillGapFeatureFlags.isRealTimeCollaborationEnabled(userId);

      expect(mockFeatureFlagProvider.evaluateFlag).toHaveBeenCalledWith(
        'realtime_collaboration',
        userId,
        false
      );
      expect(isEnabled).toBe(true);
    });
  });

  describe('Feature Flag Configuration', () => {
    it('should get all skill gap analyzer flags for user', async () => {
      // Test: Get all relevant flags for a user
      const userId = 'user-all-flags';
      const mockFlags = {
        skill_gap_analyzer_enabled: true,
        comprehensive_analysis_enabled: false,
        ai_recommendations_enabled: true,
        market_data_integration: false,
        enhanced_ui_v3: true,
      };

      mockFeatureFlagProvider.getAllFlags.mockResolvedValue(mockFlags);

      const flags = await skillGapFeatureFlags.getAllSkillGapFlags(userId);

      expect(mockFeatureFlagProvider.getAllFlags).toHaveBeenCalledWith(
        userId,
        [
          'skill_gap_analyzer_enabled',
          'comprehensive_analysis_enabled',
          'ai_recommendations_enabled',
          'market_data_integration',
          'enhanced_ui_v3',
          'interactive_tutorials',
          'realtime_collaboration',
          'beta_features_enabled',
          'enterprise_analytics',
          'caching_optimization_v2',
          'parallel_processing_enabled',
          'advanced_ai_models',
        ]
      );
      expect(flags).toEqual(mockFlags);
    });

    it('should update user context for better flag targeting', async () => {
      // Test: Update user context for personalized flags
      const userId = 'user-context';
      const context = {
        userTier: 'premium',
        skillLevel: 'intermediate',
        careerPath: 'Full Stack Developer',
        organizationId: 'org-456',
        experimentsOptIn: true,
      };

      await skillGapFeatureFlags.updateUserContext(userId, context);

      expect(mockFeatureFlagProvider.updateUserContext).toHaveBeenCalledWith(userId, context);
    });

    it('should track feature flag usage for analytics', async () => {
      // Test: Track flag usage for analytics and optimization
      const userId = 'user-tracking';
      const flagName = 'comprehensive_analysis_enabled';
      const value = true;
      const context = {
        source: 'skill_gap_analyzer_page',
        timestamp: expect.any(Number),
      };

      await skillGapFeatureFlags.trackFlagUsage(userId, flagName, value, context);

      expect(mockFeatureFlagProvider.trackFlagUsage).toHaveBeenCalledWith(
        userId,
        flagName,
        value,
        context
      );
    });
  });

  describe('Emergency Controls', () => {
    it('should handle emergency feature disable', async () => {
      // Test: Emergency disable of features
      const flagName = 'ai_recommendations_enabled';
      mockFeatureFlagProvider.setFlag.mockResolvedValue(true);

      await skillGapFeatureFlags.emergencyDisableFeature(flagName);

      expect(mockFeatureFlagProvider.setFlag).toHaveBeenCalledWith(
        flagName,
        false,
        { emergency: true, disabledAt: expect.any(String) }
      );
    });

    it('should handle emergency rollback to safe defaults', async () => {
      // Test: Emergency rollback to safe configuration
      const safeDefaults = {
        skill_gap_analyzer_enabled: true,
        comprehensive_analysis_enabled: false,
        ai_recommendations_enabled: false,
        market_data_integration: false,
        advanced_ai_models: false,
      };

      mockFeatureFlagProvider.setFlag.mockResolvedValue(true);

      await skillGapFeatureFlags.emergencyRollbackToSafeDefaults();

      // Verify each safe default was set
      Object.entries(safeDefaults).forEach(([flagName, value]) => {
        expect(mockFeatureFlagProvider.setFlag).toHaveBeenCalledWith(
          flagName,
          value,
          { emergency: true, rollback: true, timestamp: expect.any(String) }
        );
      });
    });

    it('should check if feature is in emergency mode', async () => {
      // Test: Check if feature is in emergency disabled state
      const flagName = 'comprehensive_analysis_enabled';
      mockFeatureFlagProvider.getFlag.mockResolvedValue({
        value: false,
        metadata: { emergency: true, disabledAt: '2024-01-15T10:00:00Z' }
      });

      const isEmergencyMode = await skillGapFeatureFlags.isFeatureInEmergencyMode(flagName);

      expect(mockFeatureFlagProvider.getFlag).toHaveBeenCalledWith(flagName);
      expect(isEmergencyMode).toBe(true);
    });
  });

  describe('Feature Flag Validation', () => {
    it('should validate feature flag configuration', async () => {
      // Test: Validate that all required flags are properly configured
      const requiredFlags = [
        'skill_gap_analyzer_enabled',
        'comprehensive_analysis_enabled',
        'ai_recommendations_enabled',
      ];

      mockFeatureFlagProvider.getAllFlags.mockResolvedValue({
        skill_gap_analyzer_enabled: true,
        comprehensive_analysis_enabled: false,
        ai_recommendations_enabled: true,
      });

      const validation = await skillGapFeatureFlags.validateConfiguration(requiredFlags);

      expect(validation).toEqual({
        isValid: true,
        missingFlags: [],
        invalidFlags: [],
        recommendations: [],
      });
    });

    it('should detect missing or invalid flag configurations', async () => {
      // Test: Detect configuration issues
      const requiredFlags = [
        'skill_gap_analyzer_enabled',
        'missing_flag',
        'invalid_flag',
      ];

      mockFeatureFlagProvider.getAllFlags.mockResolvedValue({
        skill_gap_analyzer_enabled: true,
        invalid_flag: 'invalid_value', // Should be boolean
      });

      const validation = await skillGapFeatureFlags.validateConfiguration(requiredFlags);

      expect(validation).toEqual({
        isValid: false,
        missingFlags: ['missing_flag'],
        invalidFlags: ['invalid_flag'],
        recommendations: [
          'Add missing flag: missing_flag',
          'Fix invalid flag value: invalid_flag',
        ],
      });
    });
  });

  describe('Performance Impact Monitoring', () => {
    it('should monitor feature flag evaluation performance', async () => {
      // Test: Monitor performance impact of flag evaluations
      const userId = 'user-perf-monitor';
      const startTime = Date.now();
      
      mockFeatureFlagProvider.evaluateFlag.mockImplementation(() => {
        // Simulate slow flag evaluation
        return new Promise(resolve => setTimeout(() => resolve(true), 100));
      });

      const result = await skillGapFeatureFlags.isSkillGapAnalyzerEnabled(userId);
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBe(true);
      expect(duration).toBeGreaterThan(90); // Should take at least 90ms
      
      // Verify performance tracking was called
      expect(mockFeatureFlagProvider.trackFlagUsage).toHaveBeenCalledWith(
        userId,
        'skill_gap_analyzer_enabled',
        true,
        expect.objectContaining({
          evaluationTime: expect.any(Number),
        })
      );
    });

    it('should handle flag evaluation timeouts gracefully', async () => {
      // Test: Handle slow or failed flag evaluations
      const userId = 'user-timeout';
      
      mockFeatureFlagProvider.evaluateFlag.mockImplementation(() => {
        return new Promise((resolve) => {
          // Never resolve to simulate timeout
          setTimeout(() => resolve(false), 10000); // 10 seconds
        });
      });

      // Should return default value quickly
      const startTime = Date.now();
      const result = await skillGapFeatureFlags.isSkillGapAnalyzerEnabledWithTimeout(userId, 1000);
      const endTime = Date.now();

      expect(result).toBe(false); // Default value
      expect(endTime - startTime).toBeLessThan(1500); // Should timeout within 1.5 seconds
    });
  });
});
