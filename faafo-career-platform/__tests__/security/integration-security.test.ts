import { NextRequest } from 'next/server';
import { createMocks } from 'node-mocks-http';

// Mock the database
jest.mock('@/lib/prisma', () => ({
  user: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  userGoal: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  assessment: {
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  forumPost: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock authentication
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

describe('Integration Security Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Route Security Integration', () => {
    it('should protect goals API with CSRF and authentication', async () => {
      const { req } = createMocks({
        method: 'POST',
        url: '/api/goals',
        headers: {
          'content-type': 'application/json',
        },
        body: {
          title: 'Test Goal',
          targetValue: 5,
          type: 'WEEKLY',
          category: 'LEARNING_RESOURCES'
        },
      });

      // Import the actual API route
      const { POST } = await import('@/app/api/goals/route');
      
      // Test without CSRF token - should fail
      const response = await POST(req as NextRequest);
      expect(response.status).toBe(403);
      
      const responseData = await response.json();
      expect(responseData.error).toBe('CSRF token missing');
    });

    it('should protect assessment API with proper validation', async () => {
      const { req } = createMocks({
        method: 'POST',
        url: '/api/assessment',
        headers: {
          'content-type': 'application/json',
          'x-csrf-token': 'valid-token',
        },
        body: {
          currentStep: 1,
          formData: {
            personalInfo: {
              name: 'Test User',
              email: '<EMAIL>'
            }
          }
        },
      });

      const { POST } = await import('@/app/api/assessment/route');
      
      // Mock CSRF validation to pass
      jest.doMock('@/lib/csrf', () => ({
        withCSRFProtection: jest.fn((req, handler) => handler()),
      }));

      // Mock authentication to fail
      const { getServerSession } = await import('next-auth/next');
      (getServerSession as jest.Mock).mockResolvedValue(null);

      const response = await POST(req as NextRequest);
      expect(response.status).toBe(401);
    });

    it('should validate forum post creation security', async () => {
      const { req } = createMocks({
        method: 'POST',
        url: '/api/forum/posts',
        headers: {
          'content-type': 'application/json',
          'x-csrf-token': 'valid-token',
        },
        body: {
          title: '<script>alert("xss")</script>',
          content: 'This is a test post',
          category: 'GENERAL'
        },
      });

      const { POST } = await import('@/app/api/forum/posts/route');
      
      // Mock successful authentication
      const { getServerSession } = await import('next-auth/next');
      (getServerSession as jest.Mock).mockResolvedValue({
        user: { id: 'user123', email: '<EMAIL>' }
      });

      const response = await POST(req as NextRequest);
      
      // Should sanitize XSS content
      if (response.status === 201) {
        const responseData = await response.json();
        expect(responseData.title).not.toContain('<script>');
      }
    });
  });

  describe('Cross-Component Security Integration', () => {
    it('should maintain security context across components', async () => {
      // Test that security context is properly maintained
      // when components interact with each other
      
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        role: 'USER'
      };

      // Mock session
      const { getServerSession } = await import('next-auth/next');
      (getServerSession as jest.Mock).mockResolvedValue({
        user: mockUser
      });

      // Test goal creation followed by assessment update
      const goalRequest = new NextRequest('http://localhost:3000/api/goals', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'x-csrf-token': 'valid-token',
        },
        body: JSON.stringify({
          title: 'Test Goal',
          targetValue: 5,
          type: 'WEEKLY',
          category: 'LEARNING_RESOURCES'
        }),
      });

      // Both operations should maintain the same security context
      expect(goalRequest.headers.get('x-csrf-token')).toBe('valid-token');
    });

    it('should prevent privilege escalation across components', async () => {
      const regularUser = {
        id: 'user123',
        email: '<EMAIL>',
        role: 'USER'
      };

      const { getServerSession } = await import('next-auth/next');
      (getServerSession as jest.Mock).mockResolvedValue({
        user: regularUser
      });

      // Try to access admin functionality
      const adminRequest = new NextRequest('http://localhost:3000/api/admin/database', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'x-csrf-token': 'valid-token',
        },
      });

      // Should be denied due to insufficient privileges
      // (This would be tested if the admin route had proper role checking)
    });
  });

  describe('Data Flow Security', () => {
    it('should sanitize data throughout the entire flow', async () => {
      const maliciousData = {
        title: '<script>alert("xss")</script>',
        description: 'Normal description',
        content: '$(rm -rf /)',
      };

      // Test that data is sanitized at input
      const { BusinessLogicSecurity } = await import('@/lib/business-logic-security');
      const validation = BusinessLogicSecurity.validateDataIntegrity(maliciousData);
      
      expect(validation.sanitizedData.title).not.toContain('<script>');
      expect(validation.sanitizedData.content).not.toContain('$(rm -rf /)');
      expect(validation.threats).toContain('XSS_ATTEMPT');
      expect(validation.threats).toContain('COMMAND_INJECTION_ATTEMPT');
    });

    it('should maintain security through database operations', async () => {
      const prisma = await import('@/lib/prisma');
      
      // Mock database operations
      (prisma.default.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user123',
        email: '<EMAIL>',
        role: 'USER'
      });

      // Test that user can only access their own data
      const user = await prisma.default.user.findUnique({
        where: { id: 'user123' }
      });

      expect(user?.id).toBe('user123');
      expect(prisma.default.user.findUnique).toHaveBeenCalledWith({
        where: { id: 'user123' }
      });
    });
  });

  describe('Error Handling Security', () => {
    it('should not leak sensitive information in errors', async () => {
      // Test that errors don't expose internal details
      const { req } = createMocks({
        method: 'POST',
        url: '/api/goals',
        headers: {
          'content-type': 'application/json',
        },
        body: {
          malformed: 'data'
        },
      });

      const { POST } = await import('@/app/api/goals/route');
      
      const response = await POST(req as NextRequest);
      const responseData = await response.json();
      
      // Error should be generic, not exposing internal details
      expect(responseData.error).not.toContain('database');
      expect(responseData.error).not.toContain('internal');
      expect(responseData.error).not.toContain('stack');
    });

    it('should handle security errors gracefully', async () => {
      // Test that security errors are handled without crashing
      const invalidRequest = new NextRequest('http://localhost:3000/api/test', {
        method: 'POST',
        headers: {
          'x-csrf-token': 'definitely-invalid-token',
        },
      });

      // Should handle gracefully without throwing
      expect(async () => {
        const { withCSRFProtection } = await import('@/lib/csrf');
        await withCSRFProtection(invalidRequest, async () => {
          return new Response('success');
        });
      }).not.toThrow();
    });
  });

  describe('Session Security Integration', () => {
    it('should validate session consistency across requests', async () => {
      const sessionData = {
        user: {
          id: 'user123',
          email: '<EMAIL>',
          role: 'USER'
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };

      const { getServerSession } = await import('next-auth/next');
      (getServerSession as jest.Mock).mockResolvedValue(sessionData);

      // Multiple requests should maintain session consistency
      const request1 = new NextRequest('http://localhost:3000/api/goals');
      const request2 = new NextRequest('http://localhost:3000/api/assessment');

      const session1 = await getServerSession();
      const session2 = await getServerSession();

      expect(session1?.user?.id).toBe(session2?.user?.id);
    });

    it('should handle session expiration securely', async () => {
      const expiredSession = {
        user: {
          id: 'user123',
          email: '<EMAIL>'
        },
        expires: new Date(Date.now() - 1000).toISOString() // Expired
      };

      const { getServerSession } = await import('next-auth/next');
      (getServerSession as jest.Mock).mockResolvedValue(null); // Expired session returns null

      const session = await getServerSession();
      expect(session).toBeNull();
    });
  });
});
