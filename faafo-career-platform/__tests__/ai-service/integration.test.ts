/**
 * Integration tests for AI Service
 * Tests complete workflows and component interactions
 */

import { NextRequest } from 'next/server';
import { POST as resumeAnalysisHandler } from '@/app/api/ai/resume-analysis/route';
import { POST as careerRecommendationsHandler } from '@/app/api/ai/career-recommendations/route';
import { geminiService } from '@/lib/services/geminiService';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';

// Mock external dependencies
jest.mock('@/lib/services/geminiService');
jest.mock('@/lib/ai-service-monitor');
jest.mock('@/lib/auth', () => ({
  getServerSession: jest.fn().mockResolvedValue({
    user: { id: 'test-user-123', email: '<EMAIL>' }
  })
}));

describe('AI Service Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful AI service responses
    (geminiService.analyzeResume as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        skills: ['JavaScript', 'React', 'Node.js'],
        experience: 'Senior',
        strengths: ['Full-stack development', 'Team leadership'],
        recommendations: ['Consider cloud certifications']
      }
    });

    (geminiService.generateCareerRecommendations as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        recommendations: [
          {
            title: 'Senior Software Engineer',
            match: 85,
            reasoning: 'Strong technical skills',
            nextSteps: ['Gain cloud experience']
          }
        ]
      }
    });

    (geminiService.generateInterviewQuestions as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        questions: [
          {
            questionText: 'Tell me about yourself',
            questionType: 'BEHAVIORAL',
            difficulty: 'BEGINNER',
            expectedDuration: 120
          }
        ]
      }
    });

    (aiServiceMonitor.recordRequest as jest.Mock).mockImplementation(() => {});
  });

  describe('Resume Analysis Workflow', () => {
    it('should complete full resume analysis workflow', async () => {
      const requestBody = {
        resumeText: `
          John Doe
          Software Engineer
          Email: <EMAIL>
          
          EXPERIENCE:
          Senior Developer at TechCorp (2020-2024)
          - Developed web applications using React and Node.js
          - Led team of 5 developers
          
          SKILLS:
          JavaScript, Python, SQL, AWS, Docker
        `,
        userId: 'test-user-123'
      };

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await resumeAnalysisHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('skills');
      expect(data.data).toHaveProperty('experience');
      expect(geminiService.analyzeResume).toHaveBeenCalledWith(
        requestBody.resumeText,
        requestBody.userId
      );
      expect(aiServiceMonitor.recordRequest).toHaveBeenCalled();
    });

    it('should handle resume analysis with validation errors', async () => {
      const maliciousRequestBody = {
        resumeText: '<script>alert("xss")</script>Malicious resume',
        userId: 'test-user-123'
      };

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify(maliciousRequestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await resumeAnalysisHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid');
    });

    it('should handle AI service failures gracefully', async () => {
      (geminiService.analyzeResume as jest.Mock).mockResolvedValue({
        success: false,
        error: 'AI service temporarily unavailable'
      });

      const requestBody = {
        resumeText: 'Valid resume content',
        userId: 'test-user-123'
      };

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await resumeAnalysisHandler(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toContain('AI service');
    });
  });

  describe('Career Recommendations Workflow', () => {
    it('should complete full career recommendations workflow', async () => {
      const requestBody = {
        assessmentData: { experience: 'senior', interests: ['technology', 'leadership'] },
        currentSkills: ['JavaScript', 'React', 'Node.js', 'Python'],
        preferences: { workStyle: 'collaborative', industry: 'technology' },
        userId: 'test-user-123'
      };

      const request = new NextRequest('http://localhost:3000/api/ai/career-recommendations', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await careerRecommendationsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('recommendations');
      expect(Array.isArray(data.data.recommendations)).toBe(true);
      expect(geminiService.generateCareerRecommendations).toHaveBeenCalledWith(
        requestBody.assessmentData,
        requestBody.currentSkills,
        requestBody.preferences,
        requestBody.userId
      );
    });

    it('should validate career recommendation inputs', async () => {
      const invalidRequestBody = {
        assessmentData: null,
        currentSkills: [],
        preferences: {},
        userId: 'test-user-123'
      };

      const request = new NextRequest('http://localhost:3000/api/ai/career-recommendations', {
        method: 'POST',
        body: JSON.stringify(invalidRequestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await careerRecommendationsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid');
    });
  });

  describe('Interview Questions Workflow', () => {
    it('should generate interview questions successfully', async () => {
      const requestBody = {
        count: 3,
        difficulty: 'INTERMEDIATE'
      };

      const request = new NextRequest('http://localhost:3000/api/interview-practice/session123/questions', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      // Mock the session lookup
      const mockSession = {
        id: 'session123',
        sessionType: 'TECHNICAL_PRACTICE',
        careerPath: 'Software Engineer',
        experienceLevel: 'SENIOR',
        difficulty: 'INTERMEDIATE',
        focusAreas: ['technical', 'problem-solving']
      };

      // We would need to mock the database call here
      // For now, we'll test the core logic

      expect(true).toBe(true); // Placeholder for actual test
    });
  });

  describe('End-to-End User Journey', () => {
    it('should complete full user journey: resume → recommendations → interview prep', async () => {
      const userId = 'test-user-journey';
      
      // Step 1: Resume Analysis
      const resumeRequest = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify({
          resumeText: 'John Doe\nSoftware Engineer\nSkills: JavaScript, React',
          userId
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      const resumeResponse = await resumeAnalysisHandler(resumeRequest);
      const resumeData = await resumeResponse.json();

      expect(resumeResponse.status).toBe(200);
      expect(resumeData.success).toBe(true);

      // Step 2: Career Recommendations
      const careerRequest = new NextRequest('http://localhost:3000/api/ai/career-recommendations', {
        method: 'POST',
        body: JSON.stringify({
          assessmentData: { experience: 'senior' },
          currentSkills: resumeData.data.skills,
          preferences: { workStyle: 'collaborative' },
          userId
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      const careerResponse = await careerRecommendationsHandler(careerRequest);
      const careerData = await careerResponse.json();

      expect(careerResponse.status).toBe(200);
      expect(careerData.success).toBe(true);

      // Verify the workflow completed successfully
      expect(aiServiceMonitor.recordRequest).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle network timeouts gracefully', async () => {
      (geminiService.analyzeResume as jest.Mock).mockImplementation(
        () => new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify({
          resumeText: 'Valid resume content',
          userId: 'test-user-123'
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await resumeAnalysisHandler(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toContain('timeout');
    });

    it('should handle rate limiting', async () => {
      (geminiService.analyzeResume as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Rate limit exceeded'
      });

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify({
          resumeText: 'Valid resume content',
          userId: 'test-user-rate-limited'
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await resumeAnalysisHandler(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.success).toBe(false);
      expect(data.error).toContain('rate limit');
    });

    it('should provide fallback responses when AI fails', async () => {
      (geminiService.analyzeResume as jest.Mock).mockResolvedValue({
        success: false,
        error: 'AI service unavailable',
        fallbackData: {
          skills: ['Unable to analyze'],
          experience: 'Unknown',
          message: 'AI service temporarily unavailable'
        }
      });

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify({
          resumeText: 'Valid resume content',
          userId: 'test-user-123'
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      const response = await resumeAnalysisHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('message');
      expect(data.data.message).toContain('temporarily unavailable');
    });
  });

  describe('Performance and Caching', () => {
    it('should utilize caching for repeated requests', async () => {
      const requestBody = {
        resumeText: 'Cached resume content',
        userId: 'test-user-cache'
      };

      // First request
      const request1 = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      await resumeAnalysisHandler(request1);

      // Second identical request
      const request2 = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      });

      await resumeAnalysisHandler(request2);

      // Should have used cache for second request
      expect(geminiService.analyzeResume).toHaveBeenCalledTimes(1);
    });

    it('should handle high concurrent load', async () => {
      const requests = Array(10).fill(null).map((_, index) => {
        return new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
          method: 'POST',
          body: JSON.stringify({
            resumeText: `Resume content ${index}`,
            userId: `test-user-${index}`
          }),
          headers: { 'Content-Type': 'application/json' }
        });
      });

      const responses = await Promise.all(
        requests.map(request => resumeAnalysisHandler(request))
      );

      // All requests should complete successfully
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      expect(geminiService.analyzeResume).toHaveBeenCalledTimes(10);
    });
  });

  describe('Monitoring and Analytics', () => {
    it('should track request metrics', async () => {
      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify({
          resumeText: 'Valid resume content',
          userId: 'test-user-metrics'
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      await resumeAnalysisHandler(request);

      expect(aiServiceMonitor.recordRequest).toHaveBeenCalledWith(
        'resume-analysis',
        'test-user-metrics',
        true,
        expect.any(Number)
      );
    });

    it('should track error metrics', async () => {
      (geminiService.analyzeResume as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Test error'
      });

      const request = new NextRequest('http://localhost:3000/api/ai/resume-analysis', {
        method: 'POST',
        body: JSON.stringify({
          resumeText: 'Valid resume content',
          userId: 'test-user-error-metrics'
        }),
        headers: { 'Content-Type': 'application/json' }
      });

      await resumeAnalysisHandler(request);

      expect(aiServiceMonitor.recordRequest).toHaveBeenCalledWith(
        'resume-analysis',
        'test-user-error-metrics',
        false,
        expect.any(Number)
      );
    });
  });
});
