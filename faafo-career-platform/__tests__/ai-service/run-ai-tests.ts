/**
 * AI Service Test Runner
 * Runs comprehensive tests for all AI service components
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

interface TestResult {
  testFile: string;
  passed: boolean;
  duration: number;
  coverage?: number;
  errors?: string[];
}

interface TestSuite {
  name: string;
  description: string;
  testFiles: string[];
  priority: 'high' | 'medium' | 'low';
}

class AIServiceTestRunner {
  private testSuites: TestSuite[] = [
    {
      name: 'Core AI Service',
      description: 'Tests core AI functionality and integration',
      testFiles: [
        'gemini-service.test.ts',
        'integration.test.ts'
      ],
      priority: 'high'
    },
    {
      name: 'Security & Validation',
      description: 'Tests security validation and threat detection',
      testFiles: [
        'security-validation.test.ts'
      ],
      priority: 'high'
    },
    {
      name: 'Caching & Performance',
      description: 'Tests caching systems and performance optimization',
      testFiles: [
        'redis-cache.test.ts'
      ],
      priority: 'medium'
    },
    {
      name: 'Monitoring & Analytics',
      description: 'Tests monitoring, metrics, and analytics',
      testFiles: [
        'monitoring.test.ts'
      ],
      priority: 'medium'
    }
  ];

  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting AI Service Comprehensive Test Suite\n');
    console.log('=' .repeat(60));
    
    const startTime = Date.now();
    
    // Run tests by priority
    for (const priority of ['high', 'medium', 'low']) {
      const suitesForPriority = this.testSuites.filter(s => s.priority === priority);
      
      if (suitesForPriority.length > 0) {
        console.log(`\n📋 Running ${priority.toUpperCase()} Priority Tests`);
        console.log('-'.repeat(40));
        
        for (const suite of suitesForPriority) {
          await this.runTestSuite(suite);
        }
      }
    }
    
    const totalTime = Date.now() - startTime;
    
    // Generate comprehensive report
    this.generateReport(totalTime);
  }

  private async runTestSuite(suite: TestSuite): Promise<void> {
    console.log(`\n🧪 ${suite.name}: ${suite.description}`);
    
    for (const testFile of suite.testFiles) {
      await this.runSingleTest(testFile);
    }
  }

  private async runSingleTest(testFile: string): Promise<void> {
    const testPath = path.join(__dirname, testFile);
    
    if (!fs.existsSync(testPath)) {
      console.log(`⚠️  Test file not found: ${testFile}`);
      this.results.push({
        testFile,
        passed: false,
        duration: 0,
        errors: ['Test file not found']
      });
      return;
    }

    console.log(`   Running ${testFile}...`);
    
    const startTime = Date.now();
    
    try {
      // Run Jest for specific test file
      const command = `npx jest ${testPath} --coverage --json --outputFile=test-results-${Date.now()}.json`;
      
      execSync(command, { 
        stdio: 'pipe',
        cwd: path.join(__dirname, '../../'),
        timeout: 60000 // 1 minute timeout
      });
      
      const duration = Date.now() - startTime;
      
      console.log(`   ✅ ${testFile} - PASSED (${duration}ms)`);
      
      this.results.push({
        testFile,
        passed: true,
        duration
      });
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.log(`   ❌ ${testFile} - FAILED (${duration}ms)`);
      console.log(`      Error: ${errorMessage}`);
      
      this.results.push({
        testFile,
        passed: false,
        duration,
        errors: [errorMessage]
      });
    }
  }

  private generateReport(totalTime: number): void {
    console.log('\n' + '='.repeat(60));
    console.log('📊 AI SERVICE TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
    
    console.log(`\n📈 Overall Statistics:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Success Rate: ${successRate.toFixed(1)}%`);
    console.log(`   Total Time: ${totalTime}ms`);
    
    // Test results by suite
    console.log(`\n📋 Results by Test Suite:`);
    for (const suite of this.testSuites) {
      const suiteResults = this.results.filter(r => 
        suite.testFiles.includes(r.testFile)
      );
      const suitePassed = suiteResults.filter(r => r.passed).length;
      const suiteTotal = suiteResults.length;
      const suiteRate = suiteTotal > 0 ? (suitePassed / suiteTotal) * 100 : 0;
      
      const status = suiteRate === 100 ? '✅' : suiteRate >= 80 ? '⚠️' : '❌';
      console.log(`   ${status} ${suite.name}: ${suitePassed}/${suiteTotal} (${suiteRate.toFixed(1)}%)`);
    }
    
    // Individual test results
    console.log(`\n📝 Individual Test Results:`);
    for (const result of this.results) {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} - ${result.testFile} (${result.duration}ms)`);
      
      if (!result.passed && result.errors) {
        result.errors.forEach(error => {
          console.log(`      └─ ${error}`);
        });
      }
    }
    
    // Performance analysis
    console.log(`\n⚡ Performance Analysis:`);
    const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / totalTests;
    const slowestTest = this.results.reduce((prev, current) => 
      prev.duration > current.duration ? prev : current
    );
    const fastestTest = this.results.reduce((prev, current) => 
      prev.duration < current.duration ? prev : current
    );
    
    console.log(`   Average Duration: ${avgDuration.toFixed(0)}ms`);
    console.log(`   Slowest Test: ${slowestTest.testFile} (${slowestTest.duration}ms)`);
    console.log(`   Fastest Test: ${fastestTest.testFile} (${fastestTest.duration}ms)`);
    
    // Recommendations
    console.log(`\n💡 Recommendations:`);
    if (successRate < 100) {
      console.log(`   🔧 Fix failing tests to improve reliability`);
    }
    if (avgDuration > 5000) {
      console.log(`   ⚡ Optimize test performance - average duration is high`);
    }
    if (failedTests > 0) {
      console.log(`   🚨 Address test failures before deployment`);
    }
    if (successRate >= 95) {
      console.log(`   🎉 Excellent test coverage and reliability!`);
    }
    
    // Final status
    console.log('\n' + '='.repeat(60));
    if (successRate === 100) {
      console.log('🎉 ALL TESTS PASSED - AI SERVICE IS READY FOR PRODUCTION!');
    } else if (successRate >= 90) {
      console.log('⚠️  MOSTLY PASSING - Minor issues need attention');
    } else if (successRate >= 70) {
      console.log('🔧 NEEDS WORK - Several tests failing');
    } else {
      console.log('🚨 CRITICAL ISSUES - Major problems detected');
    }
    console.log('='.repeat(60));
    
    // Save detailed report
    this.saveDetailedReport(totalTime);
  }

  private saveDetailedReport(totalTime: number): void {
    const report = {
      timestamp: new Date().toISOString(),
      totalTime,
      summary: {
        totalTests: this.results.length,
        passedTests: this.results.filter(r => r.passed).length,
        failedTests: this.results.filter(r => !r.passed).length,
        successRate: this.results.length > 0 ? 
          (this.results.filter(r => r.passed).length / this.results.length) * 100 : 0
      },
      testSuites: this.testSuites.map(suite => ({
        ...suite,
        results: this.results.filter(r => suite.testFiles.includes(r.testFile))
      })),
      detailedResults: this.results,
      performance: {
        averageDuration: this.results.reduce((sum, r) => sum + r.duration, 0) / this.results.length,
        slowestTest: this.results.reduce((prev, current) => 
          prev.duration > current.duration ? prev : current
        ),
        fastestTest: this.results.reduce((prev, current) => 
          prev.duration < current.duration ? prev : current
        )
      }
    };

    const reportPath = path.join(__dirname, `ai-service-test-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }

  async runSpecificSuite(suiteName: string): Promise<void> {
    const suite = this.testSuites.find(s => s.name === suiteName);
    
    if (!suite) {
      console.log(`❌ Test suite '${suiteName}' not found`);
      console.log('Available suites:');
      this.testSuites.forEach(s => console.log(`   - ${s.name}`));
      return;
    }

    console.log(`🧪 Running specific test suite: ${suite.name}`);
    await this.runTestSuite(suite);
    this.generateReport(0);
  }

  listTestSuites(): void {
    console.log('📋 Available AI Service Test Suites:\n');
    
    this.testSuites.forEach((suite, index) => {
      console.log(`${index + 1}. ${suite.name} (${suite.priority} priority)`);
      console.log(`   Description: ${suite.description}`);
      console.log(`   Test Files: ${suite.testFiles.join(', ')}`);
      console.log('');
    });
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const runner = new AIServiceTestRunner();

  if (args.length === 0) {
    await runner.runAllTests();
  } else if (args[0] === 'list') {
    runner.listTestSuites();
  } else if (args[0] === 'suite' && args[1]) {
    await runner.runSpecificSuite(args[1]);
  } else {
    console.log('Usage:');
    console.log('  npm run test:ai-service           # Run all AI service tests');
    console.log('  npm run test:ai-service list      # List available test suites');
    console.log('  npm run test:ai-service suite <name>  # Run specific test suite');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { AIServiceTestRunner };
