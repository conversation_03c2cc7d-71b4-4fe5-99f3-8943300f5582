/**
 * Basic AI Service Functionality Tests
 * Tests the existing AI service implementation
 */

// Set up environment before importing
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-testing-purposes-only';

// Mock Google Generative AI
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn().mockResolvedValue({
        response: {
          text: () => JSON.stringify({
            skills: ['JavaScript', 'React', 'Node.js'],
            experience: 'Senior',
            strengths: ['Full-stack development'],
            recommendations: ['Consider cloud certifications']
          })
        }
      })
    })
  }))
}));

import { geminiService } from '@/lib/services/geminiService';

describe('Basic AI Service Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should have initialized the service', () => {
      expect(geminiService).toBeDefined();
      expect(typeof geminiService.analyzeResume).toBe('function');
      expect(typeof geminiService.generateCareerRecommendations).toBe('function');
    });
  });

  describe('Resume Analysis', () => {

    it('should analyze resume successfully', async () => {
      const resumeText = `
        John Doe
        Software Engineer
        Skills: JavaScript, React, Node.js
        Experience: 5 years at TechCorp
      `;

      const result = await geminiService.analyzeResume(resumeText, 'test-user-123');

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('skills');
      expect(result.data).toHaveProperty('experience');
    });

    it('should handle empty resume', async () => {
      const result = await geminiService.analyzeResume('', 'test-user-123');
      expect(result.success).toBe(false);
    });

    it('should handle null resume', async () => {
      const result = await geminiService.analyzeResume(null as any, 'test-user-123');
      expect(result.success).toBe(false);
    });
  });

  describe('Career Recommendations', () => {

    it('should generate career recommendations', async () => {
      const assessmentData = { experience: 'senior', interests: ['technology'] };
      const skills = ['JavaScript', 'React'];
      const preferences = { workStyle: 'collaborative' };

      const result = await geminiService.generateCareerRecommendations(
        assessmentData,
        skills,
        preferences,
        'test-user-123'
      );

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle invalid input', async () => {
      const result = await geminiService.generateCareerRecommendations(
        null,
        [],
        {},
        'test-user-123'
      );

      expect(result.success).toBe(false);
    });
  });

  describe('Interview Questions', () => {

    it('should generate interview questions', async () => {
      const params = {
        sessionType: 'TECHNICAL_PRACTICE',
        careerPath: 'Software Engineer',
        experienceLevel: 'SENIOR',
        difficulty: 'INTERMEDIATE',
        count: 3
      };

      const result = await geminiService.generateInterviewQuestions(params);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle invalid parameters', async () => {
      const result = await geminiService.generateInterviewQuestions({});
      expect(result.success).toBe(false);
    });
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const health = await geminiService.healthCheck();

      expect(health).toBeDefined();
      expect(health.ai).toBeDefined();
    });
  });

  describe('Error Handling', () => {

    it('should handle AI API errors gracefully', async () => {
      // Mock API error
      const mockModel = {
        generateContent: jest.fn().mockRejectedValue(new Error('API Error'))
      };
      
      (geminiService as any).model = mockModel;

      const result = await geminiService.analyzeResume('Test resume', 'test-user');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle malformed responses', async () => {
      // Mock malformed response
      const mockModel = {
        generateContent: jest.fn().mockResolvedValue({
          response: {
            text: () => 'Invalid JSON'
          }
        })
      };
      
      (geminiService as any).model = mockModel;

      const result = await geminiService.analyzeResume('Test resume', 'test-user');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Input Validation', () => {

    it('should validate resume input length', async () => {
      const longResume = 'A'.repeat(100000); // Very long resume
      const result = await geminiService.analyzeResume(longResume, 'test-user');
      
      // Should either handle it or reject it gracefully
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });

    it('should validate user ID', async () => {
      const result = await geminiService.analyzeResume('Valid resume', '');
      
      expect(result.success).toBe(false);
    });

    it('should handle special characters in input', async () => {
      const resumeWithSpecialChars = 'John Doe\n@#$%^&*()_+\nSoftware Engineer';
      const result = await geminiService.analyzeResume(resumeWithSpecialChars, 'test-user');
      
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });
  });

  describe('Performance', () => {

    it('should complete requests within reasonable time', async () => {
      const startTime = Date.now();
      
      await geminiService.analyzeResume('Test resume', 'test-user');
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
    });

    it('should handle concurrent requests', async () => {
      const promises = Array(5).fill(null).map((_, index) => 
        geminiService.analyzeResume(`Resume ${index}`, `user-${index}`)
      );

      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(typeof result.success).toBe('boolean');
      });
    });
  });

  describe('Caching', () => {

    it('should handle cache operations', async () => {
      // Test that the service doesn't crash when cache is involved
      const result1 = await geminiService.analyzeResume('Cached resume', 'cache-user');
      const result2 = await geminiService.analyzeResume('Cached resume', 'cache-user');
      
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
    });
  });
});
