/**
 * Comprehensive tests for GeminiService
 * Tests core AI functionality, error handling, and integration
 */

import { geminiService } from '@/lib/services/geminiService';
import { AIServiceLogger } from '@/lib/ai-service-logger';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';

// Mock external dependencies
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn()
    })
  }))
}));

jest.mock('@/lib/ai-service-logger');
jest.mock('@/lib/ai-service-monitor');

describe('GeminiService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset service state
    (geminiService as any).initialized = false;
  });

  describe('Initialization', () => {
    it('should initialize successfully with valid API key', async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      
      const result = await geminiService.initialize();
      
      expect(result).toBe(true);
      expect(AIServiceLogger.info).toHaveBeenCalledWith(
        'Gemini AI Service initialized successfully',
        expect.any(Object)
      );
    });

    it('should fail initialization with missing API key', async () => {
      delete process.env.GOOGLE_GEMINI_API_KEY;
      
      const result = await geminiService.initialize();
      
      expect(result).toBe(false);
      expect(AIServiceLogger.error).toHaveBeenCalledWith(
        'Failed to initialize Gemini AI Service',
        expect.any(Object)
      );
    });

    it('should handle initialization errors gracefully', async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'invalid-key';
      
      // Mock initialization failure
      const mockError = new Error('Invalid API key');
      jest.spyOn(geminiService as any, 'validateApiKey').mockRejectedValue(mockError);
      
      const result = await geminiService.initialize();
      
      expect(result).toBe(false);
      expect(AIServiceLogger.error).toHaveBeenCalled();
    });
  });

  describe('Resume Analysis', () => {
    beforeEach(async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();
    });

    it('should analyze resume successfully', async () => {
      const mockResponse = {
        response: {
          text: () => JSON.stringify({
            skills: ['JavaScript', 'React', 'Node.js'],
            experience: 'Senior',
            strengths: ['Full-stack development', 'Team leadership'],
            recommendations: ['Consider cloud certifications']
          })
        }
      };

      const mockModel = {
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      };
      
      (geminiService as any).model = mockModel;

      const resumeText = 'John Doe\nSoftware Engineer\nExperience: 5 years\nSkills: JavaScript, React';
      const result = await geminiService.analyzeResume(resumeText, 'test-user-123');

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('skills');
      expect(result.data).toHaveProperty('experience');
      expect(mockModel.generateContent).toHaveBeenCalled();
    });

    it('should handle invalid resume content', async () => {
      const result = await geminiService.analyzeResume('', 'test-user-123');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid resume content');
    });

    it('should handle AI API errors', async () => {
      const mockModel = {
        generateContent: jest.fn().mockRejectedValue(new Error('API Error'))
      };
      
      (geminiService as any).model = mockModel;

      const result = await geminiService.analyzeResume('Valid resume text', 'test-user-123');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('AI service error');
    });

    it('should handle malformed AI responses', async () => {
      const mockResponse = {
        response: {
          text: () => 'Invalid JSON response'
        }
      };

      const mockModel = {
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      };
      
      (geminiService as any).model = mockModel;

      const result = await geminiService.analyzeResume('Valid resume text', 'test-user-123');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to parse AI response');
    });
  });

  describe('Career Recommendations', () => {
    beforeEach(async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();
    });

    it('should generate career recommendations successfully', async () => {
      const mockResponse = {
        response: {
          text: () => JSON.stringify({
            recommendations: [
              {
                title: 'Senior Software Engineer',
                match: 85,
                reasoning: 'Strong technical skills',
                nextSteps: ['Gain cloud experience']
              }
            ]
          })
        }
      };

      const mockModel = {
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      };
      
      (geminiService as any).model = mockModel;

      const assessmentData = { experience: 'senior', interests: ['technology'] };
      const skills = ['JavaScript', 'React'];
      const preferences = { workStyle: 'collaborative' };

      const result = await geminiService.generateCareerRecommendations(
        assessmentData,
        skills,
        preferences,
        'test-user-123'
      );

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('recommendations');
      expect(Array.isArray(result.data.recommendations)).toBe(true);
    });

    it('should validate input parameters', async () => {
      const result = await geminiService.generateCareerRecommendations(
        null,
        [],
        {},
        'test-user-123'
      );
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid input parameters');
    });
  });

  describe('Interview Questions Generation', () => {
    beforeEach(async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();
    });

    it('should generate interview questions successfully', async () => {
      const mockResponse = {
        response: {
          text: () => JSON.stringify({
            questions: [
              {
                questionText: 'Tell me about yourself',
                questionType: 'BEHAVIORAL',
                difficulty: 'BEGINNER',
                expectedDuration: 120
              }
            ]
          })
        }
      };

      const mockModel = {
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      };
      
      (geminiService as any).model = mockModel;

      const params = {
        sessionType: 'TECHNICAL_PRACTICE',
        careerPath: 'Software Engineer',
        experienceLevel: 'SENIOR',
        difficulty: 'INTERMEDIATE',
        count: 3
      };

      const result = await geminiService.generateInterviewQuestions(params);

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('questions');
      expect(Array.isArray(result.data.questions)).toBe(true);
    });

    it('should validate question generation parameters', async () => {
      const result = await geminiService.generateInterviewQuestions({});
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid parameters');
    });
  });

  describe('Health Check', () => {
    it('should return healthy status when service is initialized', async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();

      const health = await geminiService.healthCheck();

      expect(health.ai).toBe(true);
      expect(health.cache).toBeDefined();
    });

    it('should return unhealthy status when service is not initialized', async () => {
      const health = await geminiService.healthCheck();

      expect(health.ai).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    beforeEach(async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();
    });

    it('should track rate limits per user', async () => {
      const userId = 'test-user-rate-limit';
      
      // Mock successful AI response
      const mockResponse = {
        response: {
          text: () => JSON.stringify({ test: 'response' })
        }
      };

      const mockModel = {
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      };
      
      (geminiService as any).model = mockModel;

      // Make multiple requests
      for (let i = 0; i < 3; i++) {
        await geminiService.analyzeResume('Test resume', userId);
      }

      expect(AIServiceLogger.debug).toHaveBeenCalledWith(
        'Rate limit initialized for user',
        expect.objectContaining({ userId })
      );
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();
    });

    it('should handle network timeouts', async () => {
      const mockModel = {
        generateContent: jest.fn().mockRejectedValue(new Error('Request timeout'))
      };
      
      (geminiService as any).model = mockModel;

      const result = await geminiService.analyzeResume('Test resume', 'test-user');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('should handle quota exceeded errors', async () => {
      const quotaError = new Error('Quota exceeded');
      quotaError.name = 'QuotaExceededError';
      
      const mockModel = {
        generateContent: jest.fn().mockRejectedValue(quotaError)
      };
      
      (geminiService as any).model = mockModel;

      const result = await geminiService.analyzeResume('Test resume', 'test-user');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('quota');
    });
  });

  describe('Monitoring Integration', () => {
    beforeEach(async () => {
      process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key';
      await geminiService.initialize();
    });

    it('should track successful operations', async () => {
      const mockResponse = {
        response: {
          text: () => JSON.stringify({ test: 'response' })
        }
      };

      const mockModel = {
        generateContent: jest.fn().mockResolvedValue(mockResponse)
      };
      
      (geminiService as any).model = mockModel;

      await geminiService.analyzeResume('Test resume', 'test-user');

      expect(aiServiceMonitor.recordRequest).toHaveBeenCalledWith(
        'resume-analysis',
        'test-user',
        true,
        expect.any(Number)
      );
    });

    it('should track failed operations', async () => {
      const mockModel = {
        generateContent: jest.fn().mockRejectedValue(new Error('Test error'))
      };
      
      (geminiService as any).model = mockModel;

      await geminiService.analyzeResume('Test resume', 'test-user');

      expect(aiServiceMonitor.recordRequest).toHaveBeenCalledWith(
        'resume-analysis',
        'test-user',
        false,
        expect.any(Number)
      );
    });
  });
});
