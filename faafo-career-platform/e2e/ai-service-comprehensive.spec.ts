/**
 * Comprehensive AI Service End-to-End Tests
 * Tests all AI service improvements and optimizations
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword'
};

const SAMPLE_RESUME = `
<PERSON>
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE:
Senior Software Engineer at TechCorp (2020-2023)
- Developed scalable web applications using React and Node.js
- Led a team of 5 developers on multiple projects
- Implemented CI/CD pipelines and improved deployment efficiency by 40%
- Worked with AWS, Docker, and Kubernetes for cloud infrastructure

Software Engineer at StartupXYZ (2018-2020)
- Built full-stack applications using JavaScript, Python, and SQL
- Collaborated with cross-functional teams to deliver features
- Optimized database queries resulting in 30% performance improvement

EDUCATION:
Bachelor of Science in Computer Science
University of Technology (2014-2018)

SKILLS:
JavaScript, TypeScript, React, Node.js, Python, SQL, AWS, Docker, Git
`.trim();

test.describe('AI Service Comprehensive Testing', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Navigate to login page
    await page.goto('/login');
    
    // Login with test credentials
    await page.fill('input[name="email"]', TEST_USER.email);
    await page.fill('input[name="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    
    // Wait for successful login
    await page.waitForURL('/dashboard');
    await expect(page.locator('text=Dashboard')).toBeVisible();
  });

  test.describe('Resume Analysis Testing', () => {
    test('should analyze resume successfully with all improvements', async () => {
      // Navigate to resume analysis
      await page.goto('/resume-analysis');
      await expect(page.locator('h1')).toContainText('Resume Analysis');

      // Upload or paste resume
      const resumeTextarea = page.locator('textarea[placeholder*="resume" i]');
      await resumeTextarea.fill(SAMPLE_RESUME);

      // Submit for analysis
      const analyzeButton = page.locator('button:has-text("Analyze Resume")');
      await analyzeButton.click();

      // Wait for analysis to complete (with improved performance)
      await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 15000 });

      // Verify comprehensive analysis results
      const results = page.locator('[data-testid="analysis-results"]');
      await expect(results).toBeVisible();

      // Check for key analysis components
      await expect(page.locator('text=Strengths')).toBeVisible();
      await expect(page.locator('text=Areas for Improvement')).toBeVisible();
      await expect(page.locator('text=Skills Identified')).toBeVisible();
      await expect(page.locator('text=Experience Level')).toBeVisible();
      await expect(page.locator('text=Industry Fit')).toBeVisible();

      // Verify skills are properly identified
      await expect(page.locator('text=JavaScript')).toBeVisible();
      await expect(page.locator('text=React')).toBeVisible();
      await expect(page.locator('text=Node.js')).toBeVisible();

      // Check for overall score
      const scoreElement = page.locator('[data-testid="overall-score"]');
      await expect(scoreElement).toBeVisible();
      
      // Verify score is within valid range (1-100)
      const scoreText = await scoreElement.textContent();
      const score = parseInt(scoreText?.match(/\d+/)?.[0] || '0');
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    test('should handle empty resume input gracefully', async () => {
      await page.goto('/resume-analysis');
      
      // Try to submit empty resume
      const analyzeButton = page.locator('button:has-text("Analyze Resume")');
      await analyzeButton.click();

      // Should show validation error
      await expect(page.locator('text=Please provide resume content')).toBeVisible();
    });

    test('should handle very long resume input', async () => {
      await page.goto('/resume-analysis');
      
      // Create very long resume (test input validation)
      const longResume = SAMPLE_RESUME.repeat(50); // ~25KB
      const resumeTextarea = page.locator('textarea[placeholder*="resume" i]');
      await resumeTextarea.fill(longResume);

      const analyzeButton = page.locator('button:has-text("Analyze Resume")');
      await analyzeButton.click();

      // Should either process successfully or show appropriate error
      await page.waitForSelector('[data-testid="analysis-results"], .error-message', { timeout: 20000 });
    });
  });

  test.describe('Career Recommendations Testing', () => {
    test('should generate personalized career recommendations', async () => {
      await page.goto('/career-recommendations');
      
      // Fill in skills
      const skillsInput = page.locator('input[placeholder*="skills" i]');
      await skillsInput.fill('JavaScript, React, Node.js, Python, AWS');

      // Select experience level
      await page.selectOption('select[name="experienceLevel"]', 'SENIOR');

      // Set preferences
      const preferencesTextarea = page.locator('textarea[placeholder*="preferences" i]');
      await preferencesTextarea.fill('Remote work, flexible hours, growth opportunities');

      // Generate recommendations
      const generateButton = page.locator('button:has-text("Get Recommendations")');
      await generateButton.click();

      // Wait for recommendations
      await page.waitForSelector('[data-testid="career-recommendations"]', { timeout: 15000 });

      // Verify recommendations structure
      const recommendations = page.locator('[data-testid="career-recommendation"]');
      await expect(recommendations).toHaveCount({ min: 1, max: 5 });

      // Check recommendation details
      const firstRecommendation = recommendations.first();
      await expect(firstRecommendation.locator('.career-path')).toBeVisible();
      await expect(firstRecommendation.locator('.match-score')).toBeVisible();
      await expect(firstRecommendation.locator('.reasoning')).toBeVisible();
      await expect(firstRecommendation.locator('.required-skills')).toBeVisible();
    });

    test('should handle invalid skills input', async () => {
      await page.goto('/career-recommendations');
      
      // Try with empty skills
      const generateButton = page.locator('button:has-text("Get Recommendations")');
      await generateButton.click();

      // Should show validation error
      await expect(page.locator('text=Please provide at least one skill')).toBeVisible();
    });
  });

  test.describe('Interview Practice Testing', () => {
    test('should generate interview questions with enhanced logic', async () => {
      await page.goto('/interview-practice');
      
      // Configure interview session
      await page.selectOption('select[name="sessionType"]', 'TECHNICAL_PRACTICE');
      await page.selectOption('select[name="careerPath"]', 'Software Engineer');
      await page.selectOption('select[name="experienceLevel"]', 'SENIOR');
      await page.selectOption('select[name="difficulty"]', 'INTERMEDIATE');
      
      // Set question count
      await page.fill('input[name="questionCount"]', '5');

      // Start interview practice
      const startButton = page.locator('button:has-text("Start Practice")');
      await startButton.click();

      // Wait for questions to load
      await page.waitForSelector('[data-testid="interview-question"]', { timeout: 15000 });

      // Verify question structure
      const question = page.locator('[data-testid="interview-question"]');
      await expect(question).toBeVisible();
      
      // Check question components
      await expect(page.locator('[data-testid="question-text"]')).toBeVisible();
      await expect(page.locator('[data-testid="question-type"]')).toBeVisible();
      await expect(page.locator('[data-testid="question-category"]')).toBeVisible();
      await expect(page.locator('[data-testid="question-difficulty"]')).toBeVisible();

      // Verify question is appropriate for senior level
      const questionText = await page.locator('[data-testid="question-text"]').textContent();
      expect(questionText).toBeTruthy();
      expect(questionText!.length).toBeGreaterThan(10);
    });

    test('should handle interview response analysis', async () => {
      await page.goto('/interview-practice');
      
      // Start a quick practice session
      await page.selectOption('select[name="sessionType"]', 'BEHAVIORAL_PRACTICE');
      await page.fill('input[name="questionCount"]', '1');
      await page.click('button:has-text("Start Practice")');

      // Wait for question
      await page.waitForSelector('[data-testid="interview-question"]', { timeout: 15000 });

      // Provide a response
      const responseTextarea = page.locator('textarea[placeholder*="response" i]');
      await responseTextarea.fill(`
        In my previous role as a Senior Software Engineer, I faced a challenging situation where our main application was experiencing performance issues during peak hours. 
        
        The situation was critical because it was affecting user experience and potentially losing customers. I took the initiative to analyze the problem systematically.
        
        I implemented several optimizations including database query optimization, caching strategies, and code refactoring. I also coordinated with the DevOps team to scale our infrastructure.
        
        As a result, we reduced response times by 60% and eliminated the performance bottlenecks. This experience taught me the importance of proactive monitoring and scalable architecture design.
      `);

      // Submit response for analysis
      const analyzeButton = page.locator('button:has-text("Analyze Response")');
      await analyzeButton.click();

      // Wait for analysis
      await page.waitForSelector('[data-testid="response-analysis"]', { timeout: 20000 });

      // Verify analysis components
      await expect(page.locator('[data-testid="overall-score"]')).toBeVisible();
      await expect(page.locator('[data-testid="feedback-positive"]')).toBeVisible();
      await expect(page.locator('[data-testid="feedback-constructive"]')).toBeVisible();
      await expect(page.locator('[data-testid="strengths"]')).toBeVisible();
      await expect(page.locator('[data-testid="improvements"]')).toBeVisible();

      // Check STAR method score for behavioral questions
      await expect(page.locator('[data-testid="star-method-score"]')).toBeVisible();
    });

    test('should use fallback questions when AI service is unavailable', async () => {
      // This test simulates AI service failure to test fallback mechanisms
      await page.goto('/interview-practice');
      
      // Configure session that might trigger fallback
      await page.selectOption('select[name="sessionType"]', 'GENERAL_PRACTICE');
      await page.fill('input[name="questionCount"]', '3');
      
      // Start practice
      await page.click('button:has-text("Start Practice")');

      // Should still get questions (either from AI or fallback)
      await page.waitForSelector('[data-testid="interview-question"]', { timeout: 20000 });
      
      const question = page.locator('[data-testid="question-text"]');
      await expect(question).toBeVisible();
      
      // Verify question content exists
      const questionText = await question.textContent();
      expect(questionText).toBeTruthy();
      expect(questionText!.length).toBeGreaterThan(5);
    });
  });

  test.describe('Performance and Caching Testing', () => {
    test('should demonstrate improved response times', async () => {
      await page.goto('/resume-analysis');
      
      // First request (cache miss)
      const startTime1 = Date.now();
      await page.fill('textarea[placeholder*="resume" i]', SAMPLE_RESUME);
      await page.click('button:has-text("Analyze Resume")');
      await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 15000 });
      const responseTime1 = Date.now() - startTime1;

      // Second request with same data (should be faster due to caching)
      await page.reload();
      const startTime2 = Date.now();
      await page.fill('textarea[placeholder*="resume" i]', SAMPLE_RESUME);
      await page.click('button:has-text("Analyze Resume")');
      await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 10000 });
      const responseTime2 = Date.now() - startTime2;

      // Second request should be significantly faster (cached)
      console.log(`First request: ${responseTime1}ms, Second request: ${responseTime2}ms`);
      
      // Allow some variance but expect improvement
      expect(responseTime2).toBeLessThan(responseTime1 * 0.8); // At least 20% faster
    });

    test('should handle concurrent requests efficiently', async () => {
      const promises = [];
      
      // Create multiple concurrent requests
      for (let i = 0; i < 3; i++) {
        const promise = (async () => {
          const newPage = await page.context().newPage();
          await newPage.goto('/career-recommendations');
          
          await newPage.fill('input[placeholder*="skills" i]', `JavaScript, React, Skill${i}`);
          await newPage.selectOption('select[name="experienceLevel"]', 'MID');
          await newPage.click('button:has-text("Get Recommendations")');
          
          await newPage.waitForSelector('[data-testid="career-recommendations"]', { timeout: 20000 });
          await newPage.close();
        })();
        
        promises.push(promise);
      }

      // All requests should complete successfully
      await Promise.all(promises);
    });
  });

  test.describe('Security and Validation Testing', () => {
    test('should prevent XSS attacks in resume input', async () => {
      await page.goto('/resume-analysis');
      
      // Try to inject malicious script
      const maliciousInput = `
        <script>alert('XSS')</script>
        John Doe
        Software Engineer
        <img src="x" onerror="alert('XSS')">
      `;
      
      await page.fill('textarea[placeholder*="resume" i]', maliciousInput);
      await page.click('button:has-text("Analyze Resume")');

      // Should either sanitize input or show validation error
      await page.waitForSelector('[data-testid="analysis-results"], .error-message', { timeout: 15000 });
      
      // Verify no script execution
      const alerts = [];
      page.on('dialog', dialog => {
        alerts.push(dialog.message());
        dialog.dismiss();
      });
      
      expect(alerts).toHaveLength(0);
    });

    test('should enforce rate limiting', async () => {
      await page.goto('/resume-analysis');
      
      // Make multiple rapid requests
      const requests = [];
      for (let i = 0; i < 5; i++) {
        const request = (async () => {
          await page.fill('textarea[placeholder*="resume" i]', `Resume ${i}: ${SAMPLE_RESUME}`);
          await page.click('button:has-text("Analyze Resume")');
          
          try {
            await page.waitForSelector('[data-testid="analysis-results"], .error-message', { timeout: 10000 });
            return 'success';
          } catch {
            return 'timeout';
          }
        })();
        
        requests.push(request);
      }

      const results = await Promise.all(requests);
      
      // Some requests should succeed, but rate limiting should prevent all from succeeding
      const successCount = results.filter(r => r === 'success').length;
      expect(successCount).toBeGreaterThan(0); // At least some should work
      expect(successCount).toBeLessThan(5); // But not all due to rate limiting
    });
  });

  test.describe('Error Handling and Resilience Testing', () => {
    test('should handle network interruptions gracefully', async () => {
      await page.goto('/interview-practice');
      
      // Start interview practice
      await page.selectOption('select[name="sessionType"]', 'TECHNICAL_PRACTICE');
      await page.fill('input[name="questionCount"]', '1');
      
      // Simulate network issues by going offline briefly
      await page.context().setOffline(true);
      await page.click('button:has-text("Start Practice")');
      
      // Wait a moment then restore connection
      await page.waitForTimeout(2000);
      await page.context().setOffline(false);

      // Should eventually succeed or show appropriate error
      await page.waitForSelector('[data-testid="interview-question"], .error-message', { timeout: 25000 });
    });

    test('should provide helpful error messages', async () => {
      await page.goto('/resume-analysis');
      
      // Try with invalid input
      await page.fill('textarea[placeholder*="resume" i]', 'x'); // Too short
      await page.click('button:has-text("Analyze Resume")');

      // Should show helpful validation message
      const errorMessage = page.locator('.error-message, .validation-error');
      await expect(errorMessage).toBeVisible();
      
      const errorText = await errorMessage.textContent();
      expect(errorText).toContain('resume'); // Should mention resume
      expect(errorText!.length).toBeGreaterThan(10); // Should be descriptive
    });
  });

  test.describe('Monitoring and Health Checks', () => {
    test('should have accessible health check endpoint', async () => {
      // Test the health check API
      const response = await page.request.get('/api/health');
      expect(response.status()).toBe(200);
      
      const healthData = await response.json();
      expect(healthData).toHaveProperty('ai');
      expect(healthData).toHaveProperty('cache');
    });

    test('should track performance metrics', async () => {
      // Make a few requests to generate metrics
      await page.goto('/resume-analysis');
      await page.fill('textarea[placeholder*="resume" i]', SAMPLE_RESUME);
      await page.click('button:has-text("Analyze Resume")');
      await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 15000 });

      // Check if performance monitoring is working (admin endpoint)
      try {
        const metricsResponse = await page.request.get('/api/admin/ai-performance-dashboard?view=overview');
        if (metricsResponse.status() === 200) {
          const metrics = await metricsResponse.json();
          expect(metrics).toHaveProperty('overview');
          expect(metrics.overview).toHaveProperty('totalRequests');
        }
      } catch (error) {
        // Admin endpoint might require authentication, which is expected
        console.log('Admin metrics endpoint requires authentication (expected)');
      }
    });
  });
});

test.describe('Integration Testing', () => {
  test('should complete full user journey successfully', async ({ page }) => {
    // Login
    await page.goto('/login');
    await page.fill('input[name="email"]', TEST_USER.email);
    await page.fill('input[name="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');

    // 1. Analyze Resume
    await page.goto('/resume-analysis');
    await page.fill('textarea[placeholder*="resume" i]', SAMPLE_RESUME);
    await page.click('button:has-text("Analyze Resume")');
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 15000 });

    // 2. Get Career Recommendations
    await page.goto('/career-recommendations');
    await page.fill('input[placeholder*="skills" i]', 'JavaScript, React, Node.js');
    await page.selectOption('select[name="experienceLevel"]', 'SENIOR');
    await page.click('button:has-text("Get Recommendations")');
    await page.waitForSelector('[data-testid="career-recommendations"]', { timeout: 15000 });

    // 3. Practice Interview
    await page.goto('/interview-practice');
    await page.selectOption('select[name="sessionType"]', 'BEHAVIORAL_PRACTICE');
    await page.fill('input[name="questionCount"]', '1');
    await page.click('button:has-text("Start Practice")');
    await page.waitForSelector('[data-testid="interview-question"]', { timeout: 15000 });

    // All steps should complete successfully
    await expect(page.locator('[data-testid="interview-question"]')).toBeVisible();
  });
});
