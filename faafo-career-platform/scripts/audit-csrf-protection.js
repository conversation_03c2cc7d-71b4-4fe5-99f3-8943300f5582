#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// API routes that handle state-changing operations
const apiRoutes = [
  'src/app/api/contact/route.ts',
  'src/app/api/tools/salary-calculator/route.ts',
  'src/app/api/learning-progress/route.ts',
  'src/app/api/achievements/route.ts',
  'src/app/api/goals/route.ts',
  'src/app/api/auth/resend-verification/route.ts',
  'src/app/api/auth/verify-email/route.ts',
  'src/app/api/auth/forgot-password/route.ts',
  'src/app/api/auth/reset-password/route.ts',
  'src/app/api/signup/route.ts',
  'src/app/api/admin/database/route.ts',
  'src/app/api/progress-tracker/route.ts',
  'src/app/api/assessment/route.ts',
  'src/app/api/assessment/[id]/ai-insights/route.ts',
  'src/app/api/assessment/[id]/enhanced-results/route.ts',
  'src/app/api/forum/posts/route.ts',
  'src/app/api/forum/posts/[postId]/replies/route.ts',
  'src/app/api/forum/reactions/route.ts',
  'src/app/api/forum/bookmarks/route.ts',
  'src/app/api/profile/route.ts',
  'src/app/api/profile/photo/route.ts',
  'src/app/api/ai/career-recommendations/route.ts',
  'src/app/api/ai/health/route.ts',
  'src/app/api/ai/resume-analysis/route.ts',
  'src/app/api/ai/interview-prep/route.ts',
  'src/app/api/ai/skills-analysis/route.ts',
  'src/app/api/learning-resources/route.ts',
  'src/app/api/learning-resources/[id]/route.ts',
  'src/app/api/learning-resources/categories/route.ts',
  'src/app/api/career-paths/bookmarks/route.ts',
  'src/app/api/career-paths/route.ts',
  'src/app/api/freedom-fund/route.ts',
  'src/app/api/interview-practice/progress/route.ts',
  'src/app/api/interview-practice/route.ts',
  'src/app/api/interview-practice/[sessionId]/responses/route.ts',
  'src/app/api/interview-practice/[sessionId]/responses/[responseId]/route.ts',
  'src/app/api/interview-practice/[sessionId]/route.ts',
  'src/app/api/interview-practice/[sessionId]/questions/route.ts',
  'src/app/api/learning-paths/route.ts',
  'src/app/api/learning-paths/[id]/enroll/route.ts',
  'src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts',
  'src/app/api/learning-paths/[id]/route.ts',
  'src/app/api/analytics/dashboard/route.ts',
  'src/app/api/resource-ratings/route.ts'
];

// Routes that should be exempt from CSRF (typically auth-related)
const csrfExemptRoutes = [
  'src/app/api/auth/resend-verification/route.ts',
  'src/app/api/auth/verify-email/route.ts',
  'src/app/api/auth/forgot-password/route.ts',
  'src/app/api/auth/reset-password/route.ts',
  'src/app/api/signup/route.ts',
  'src/app/api/csrf-token/route.ts'
];

function logResult(route, status, message, severity = 'info') {
  const colors = {
    pass: '\x1b[32m✅',
    fail: '\x1b[31m❌',
    warning: '\x1b[33m⚠️',
    info: '\x1b[36mℹ️',
    reset: '\x1b[0m'
  };
  
  const color = colors[severity] || colors.info;
  console.log(`${color} ${route}: ${message}${colors.reset}`);
}

function auditCSRFProtection() {
  console.log('🔒 CSRF Protection Audit\n');
  
  let totalRoutes = 0;
  let protectedRoutes = 0;
  let exemptRoutes = 0;
  let missingProtection = [];
  
  apiRoutes.forEach(route => {
    const fullPath = path.join(process.cwd(), route);
    
    if (!fs.existsSync(fullPath)) {
      logResult(route, 'fail', 'File not found', 'fail');
      return;
    }
    
    totalRoutes++;
    
    // Check if route is exempt from CSRF
    if (csrfExemptRoutes.includes(route)) {
      exemptRoutes++;
      logResult(route, 'pass', 'Exempt from CSRF protection', 'info');
      return;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Check for state-changing methods
    const hasStateChangingMethods = /export\s+(async\s+)?function\s+(POST|PUT|DELETE|PATCH)|export\s+const\s+(POST|PUT|DELETE|PATCH)/.test(content);
    
    if (!hasStateChangingMethods) {
      logResult(route, 'pass', 'No state-changing methods found', 'info');
      return;
    }
    
    // Check for CSRF protection patterns
    const hasCSRFProtection = 
      content.includes('withCSRFProtection') ||
      content.includes('SecurityMiddleware.protect') ||
      content.includes('SecurityMiddleware.secureAPI') ||
      content.includes('requireCSRF: true') ||
      content.includes('x-csrf-token') ||
      content.includes('csrf-token');
    
    if (hasCSRFProtection) {
      protectedRoutes++;
      logResult(route, 'pass', 'CSRF protection found', 'pass');
    } else {
      missingProtection.push(route);
      logResult(route, 'fail', 'Missing CSRF protection', 'fail');
    }
  });
  
  console.log('\n📊 CSRF Protection Summary:');
  console.log(`Total routes audited: ${totalRoutes}`);
  console.log(`Routes with CSRF protection: ${protectedRoutes}`);
  console.log(`Routes exempt from CSRF: ${exemptRoutes}`);
  console.log(`Routes missing CSRF protection: ${missingProtection.length}`);
  
  if (missingProtection.length > 0) {
    console.log('\n❌ Routes requiring CSRF protection:');
    missingProtection.forEach(route => {
      console.log(`   - ${route}`);
    });
    
    console.log('\n🔧 Recommended fixes:');
    console.log('1. Wrap handlers with withCSRFProtection()');
    console.log('2. Use SecurityMiddleware.secureAPI()');
    console.log('3. Add requireCSRF: true to SecurityMiddleware.protect()');
  } else {
    console.log('\n✅ All routes have appropriate CSRF protection!');
  }
  
  const protectionRate = ((protectedRoutes + exemptRoutes) / totalRoutes * 100).toFixed(1);
  console.log(`\n🛡️ Overall protection rate: ${protectionRate}%`);
  
  return {
    totalRoutes,
    protectedRoutes,
    exemptRoutes,
    missingProtection,
    protectionRate: parseFloat(protectionRate)
  };
}

// Run the audit
if (require.main === module) {
  const results = auditCSRFProtection();
  process.exit(results.missingProtection.length > 0 ? 1 : 0);
}

module.exports = { auditCSRFProtection };
