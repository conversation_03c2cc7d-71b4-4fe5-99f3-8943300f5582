#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { NextRequest } from 'next/server';

const prisma = new PrismaClient();

// Mock NextAuth functions for testing
const mockNextAuthFunctions = {
  async testCredentialsAuth(email: string, password: string) {
    console.log(`🔐 Testing credentials authentication for: ${email}`);
    
    try {
      // Simulate the authorize function from auth.tsx
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        console.log('❌ User not found');
        return null;
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        console.log('❌ Account is locked');
        throw new Error('Account is temporarily locked due to too many failed login attempts.');
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        console.log('❌ Invalid password');
        return null;
      }

      // Check if email is verified (bypass in development)
      if (!user.emailVerified && process.env.NODE_ENV === 'production') {
        console.log('❌ Email not verified');
        throw new Error('Please verify your email address before signing in.');
      }

      console.log('✅ Authentication successful');
      return { id: user.id, email: user.email, name: user.name };
      
    } catch (error) {
      console.log(`❌ Authentication error: ${error}`);
      throw error;
    }
  },

  async testSessionCreation(userId: string) {
    console.log(`🎫 Testing session creation for user: ${userId}`);
    
    try {
      const sessionToken = `test-session-${Date.now()}-${Math.random()}`;
      const expires = new Date(Date.now() + 8 * 60 * 60 * 1000); // 8 hours
      
      const session = await prisma.session.create({
        data: {
          sessionToken,
          userId,
          expires
        }
      });
      
      console.log('✅ Session created successfully');
      console.log(`   Session ID: ${session.id}`);
      console.log(`   Session Token: ${session.sessionToken}`);
      console.log(`   Expires: ${session.expires}`);
      
      return session;
      
    } catch (error) {
      console.log(`❌ Session creation error: ${error}`);
      throw error;
    }
  },

  async testSessionRetrieval(sessionToken: string) {
    console.log(`🔍 Testing session retrieval for token: ${sessionToken.substring(0, 20)}...`);
    
    try {
      const session = await prisma.session.findUnique({
        where: { sessionToken },
        include: { user: true }
      });
      
      if (!session) {
        console.log('❌ Session not found');
        return null;
      }
      
      if (session.expires < new Date()) {
        console.log('❌ Session expired');
        return null;
      }
      
      console.log('✅ Session retrieved successfully');
      console.log(`   User: ${session.user.email}`);
      console.log(`   Expires: ${session.expires}`);
      
      return session;
      
    } catch (error) {
      console.log(`❌ Session retrieval error: ${error}`);
      throw error;
    }
  }
};

async function testAuthenticationFlow() {
  console.log('🧪 Testing Complete Authentication Flow...\n');

  try {
    // 1. Test user credentials authentication
    console.log('1️⃣ Testing credentials authentication...');
    const authResult = await mockNextAuthFunctions.testCredentialsAuth(
      '<EMAIL>',
      'testpassword'
    );
    
    if (!authResult) {
      throw new Error('Authentication failed');
    }
    
    console.log(`✅ User authenticated: ${authResult.email}\n`);

    // 2. Test session creation
    console.log('2️⃣ Testing session creation...');
    const session = await mockNextAuthFunctions.testSessionCreation(authResult.id);
    console.log('');

    // 3. Test session retrieval
    console.log('3️⃣ Testing session retrieval...');
    const retrievedSession = await mockNextAuthFunctions.testSessionRetrieval(session.sessionToken);
    
    if (!retrievedSession) {
      throw new Error('Session retrieval failed');
    }
    console.log('');

    // 4. Test invalid credentials
    console.log('4️⃣ Testing invalid credentials...');
    try {
      await mockNextAuthFunctions.testCredentialsAuth(
        '<EMAIL>',
        'wrongpassword'
      );
      console.log('❌ Should have failed with wrong password');
    } catch (error) {
      console.log('✅ Correctly rejected invalid credentials');
    }
    console.log('');

    // 5. Test non-existent user
    console.log('5️⃣ Testing non-existent user...');
    const nonExistentResult = await mockNextAuthFunctions.testCredentialsAuth(
      '<EMAIL>',
      'password'
    );
    
    if (nonExistentResult === null) {
      console.log('✅ Correctly rejected non-existent user');
    } else {
      console.log('❌ Should have rejected non-existent user');
    }
    console.log('');

    // 6. Test session expiry
    console.log('6️⃣ Testing session expiry...');
    const expiredSessionToken = `expired-session-${Date.now()}`;
    const expiredSession = await prisma.session.create({
      data: {
        sessionToken: expiredSessionToken,
        userId: authResult.id,
        expires: new Date(Date.now() - 1000) // Expired 1 second ago
      }
    });
    
    const expiredResult = await mockNextAuthFunctions.testSessionRetrieval(expiredSessionToken);
    if (expiredResult === null) {
      console.log('✅ Correctly rejected expired session');
    } else {
      console.log('❌ Should have rejected expired session');
    }
    console.log('');

    // 7. Clean up test sessions
    console.log('7️⃣ Cleaning up test sessions...');
    await prisma.session.deleteMany({
      where: {
        sessionToken: {
          in: [session.sessionToken, expiredSessionToken]
        }
      }
    });
    console.log('✅ Test sessions cleaned up\n');

    // 8. Test account lockout mechanism
    console.log('8️⃣ Testing account lockout mechanism...');
    
    // Create a test user for lockout testing
    const lockoutTestEmail = '<EMAIL>';
    const hashedPassword = await bcrypt.hash('testpassword', 12);
    
    const lockoutUser = await prisma.user.upsert({
      where: { email: lockoutTestEmail },
      update: {
        failedLoginAttempts: 0,
        lockedUntil: null
      },
      create: {
        email: lockoutTestEmail,
        password: hashedPassword,
        name: 'Lockout Test User',
        emailVerified: new Date(),
      }
    });

    // Simulate failed login attempts
    for (let i = 1; i <= 5; i++) {
      try {
        await mockNextAuthFunctions.testCredentialsAuth(lockoutTestEmail, 'wrongpassword');
      } catch (error) {
        // Expected to fail
      }
      
      // Update failed attempts manually (simulating the auth logic)
      await prisma.user.update({
        where: { id: lockoutUser.id },
        data: {
          failedLoginAttempts: i,
          ...(i >= 5 ? { lockedUntil: new Date(Date.now() + 15 * 60 * 1000) } : {})
        }
      });
    }

    // Test that account is now locked
    try {
      await mockNextAuthFunctions.testCredentialsAuth(lockoutTestEmail, 'testpassword');
      console.log('❌ Should have been locked after 5 failed attempts');
    } catch (error) {
      console.log('✅ Account correctly locked after failed attempts');
    }

    // Clean up lockout test user
    await prisma.user.delete({
      where: { id: lockoutUser.id }
    });
    console.log('✅ Lockout test user cleaned up\n');

    console.log('🎉 All authentication flow tests passed!');

  } catch (error) {
    console.error('❌ Authentication flow test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  testAuthenticationFlow();
}

export { testAuthenticationFlow };
