#!/usr/bin/env node

/**
 * Production Readiness Check Script
 * Automated verification of production readiness criteria
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ProductionReadinessChecker {
  constructor() {
    this.checks = [];
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      total: 0
    };
  }

  /**
   * Add a check to the list
   */
  addCheck(name, description, checkFn, critical = true) {
    this.checks.push({
      name,
      description,
      checkFn,
      critical,
      status: 'pending'
    });
  }

  /**
   * Run all checks
   */
  async runAllChecks() {
    console.log('🚀 Starting Production Readiness Assessment...\n');

    for (const check of this.checks) {
      await this.runCheck(check);
    }

    this.printSummary();
    return this.results;
  }

  /**
   * Run a single check
   */
  async runCheck(check) {
    try {
      console.log(`🔍 ${check.name}...`);
      const result = await check.checkFn();
      
      if (result.passed) {
        check.status = 'passed';
        this.results.passed++;
        console.log(`   ✅ ${result.message || 'Passed'}`);
      } else {
        check.status = check.critical ? 'failed' : 'warning';
        if (check.critical) {
          this.results.failed++;
          console.log(`   ❌ ${result.message || 'Failed'}`);
        } else {
          this.results.warnings++;
          console.log(`   ⚠️  ${result.message || 'Warning'}`);
        }
      }
    } catch (error) {
      check.status = 'error';
      this.results.failed++;
      console.log(`   💥 Error: ${error.message}`);
    }
    
    this.results.total++;
    console.log('');
  }

  /**
   * Print summary of results
   */
  printSummary() {
    console.log('📊 Production Readiness Assessment Summary');
    console.log('==========================================');
    console.log(`Total Checks: ${this.results.total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`⚠️  Warnings: ${this.results.warnings}`);
    console.log('');

    if (this.results.failed === 0) {
      console.log('🎉 ALL CRITICAL CHECKS PASSED - PRODUCTION READY! 🎉');
    } else {
      console.log('🚨 CRITICAL ISSUES FOUND - NOT READY FOR PRODUCTION 🚨');
      console.log('Please fix the failed checks before deploying.');
    }
  }

  /**
   * Check if file exists
   */
  fileExists(filePath) {
    return fs.existsSync(path.join(process.cwd(), filePath));
  }

  /**
   * Check if directory exists
   */
  directoryExists(dirPath) {
    return fs.existsSync(path.join(process.cwd(), dirPath)) && 
           fs.statSync(path.join(process.cwd(), dirPath)).isDirectory();
  }

  /**
   * Run command and check if it succeeds
   */
  runCommand(command) {
    try {
      execSync(command, { stdio: 'pipe' });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check package.json for required dependencies
   */
  checkDependency(packageName) {
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      return packageJson.dependencies?.[packageName] || packageJson.devDependencies?.[packageName];
    } catch (error) {
      return false;
    }
  }
}

// Initialize checker
const checker = new ProductionReadinessChecker();

// Add all production readiness checks
checker.addCheck(
  'Environment Configuration',
  'Check for required environment files',
  async () => {
    const hasEnvExample = checker.fileExists('.env.example');
    const hasEnvLocal = checker.fileExists('.env.local');
    
    if (hasEnvExample && hasEnvLocal) {
      return { passed: true, message: 'Environment files present' };
    } else {
      return { passed: false, message: 'Missing .env.example or .env.local' };
    }
  }
);

checker.addCheck(
  'Next.js Configuration',
  'Verify Next.js configuration is production-ready',
  async () => {
    const hasNextConfig = checker.fileExists('next.config.js') || checker.fileExists('next.config.mjs');
    
    if (hasNextConfig) {
      return { passed: true, message: 'Next.js configuration found' };
    } else {
      return { passed: false, message: 'Missing next.config.js' };
    }
  }
);

checker.addCheck(
  'Database Configuration',
  'Check database setup and migrations',
  async () => {
    const hasPrismaSchema = checker.fileExists('prisma/schema.prisma');
    const hasMigrations = checker.directoryExists('prisma/migrations');
    
    if (hasPrismaSchema && hasMigrations) {
      return { passed: true, message: 'Database schema and migrations present' };
    } else {
      return { passed: false, message: 'Missing Prisma schema or migrations' };
    }
  }
);

checker.addCheck(
  'Security Dependencies',
  'Verify security-related packages are installed',
  async () => {
    const hasNextAuth = checker.checkDependency('next-auth');
    const hasBcrypt = checker.checkDependency('bcryptjs') || checker.checkDependency('bcrypt');
    
    if (hasNextAuth && hasBcrypt) {
      return { passed: true, message: 'Security dependencies installed' };
    } else {
      return { passed: false, message: 'Missing security dependencies' };
    }
  }
);

checker.addCheck(
  'Input Sanitization',
  'Check for XSS protection libraries',
  async () => {
    const hasDOMPurify = checker.checkDependency('dompurify');
    const hasIsomorphicDOMPurify = checker.checkDependency('isomorphic-dompurify');
    
    if (hasDOMPurify || hasIsomorphicDOMPurify) {
      return { passed: true, message: 'Input sanitization library present' };
    } else {
      return { passed: false, message: 'Missing DOMPurify or similar sanitization library' };
    }
  }
);

checker.addCheck(
  'Error Monitoring',
  'Verify error monitoring is configured',
  async () => {
    const hasSentry = checker.checkDependency('@sentry/nextjs');
    
    if (hasSentry) {
      return { passed: true, message: 'Sentry error monitoring configured' };
    } else {
      return { passed: false, message: 'Missing error monitoring (Sentry)' };
    }
  }
);

checker.addCheck(
  'Build Process',
  'Test production build',
  async () => {
    const buildSuccess = checker.runCommand('npm run build');
    
    if (buildSuccess) {
      return { passed: true, message: 'Production build successful' };
    } else {
      return { passed: false, message: 'Production build failed' };
    }
  }
);

checker.addCheck(
  'TypeScript Compilation',
  'Check TypeScript compilation',
  async () => {
    const tscSuccess = checker.runCommand('npx tsc --noEmit');
    
    if (tscSuccess) {
      return { passed: true, message: 'TypeScript compilation successful' };
    } else {
      return { passed: false, message: 'TypeScript compilation errors found' };
    }
  }
);

checker.addCheck(
  'Linting',
  'Run ESLint checks',
  async () => {
    const lintSuccess = checker.runCommand('npm run lint');
    
    if (lintSuccess) {
      return { passed: true, message: 'Linting passed' };
    } else {
      return { passed: false, message: 'Linting errors found', critical: false };
    }
  },
  false // Non-critical
);

checker.addCheck(
  'Package Vulnerabilities',
  'Check for security vulnerabilities in dependencies',
  async () => {
    const auditSuccess = checker.runCommand('npm audit --audit-level=high');
    
    if (auditSuccess) {
      return { passed: true, message: 'No high-severity vulnerabilities found' };
    } else {
      return { passed: false, message: 'High-severity vulnerabilities found in dependencies' };
    }
  }
);

checker.addCheck(
  'Required Directories',
  'Verify all required directories exist',
  async () => {
    const requiredDirs = ['src', 'public', 'prisma'];
    const missingDirs = requiredDirs.filter(dir => !checker.directoryExists(dir));
    
    if (missingDirs.length === 0) {
      return { passed: true, message: 'All required directories present' };
    } else {
      return { passed: false, message: `Missing directories: ${missingDirs.join(', ')}` };
    }
  }
);

checker.addCheck(
  'API Routes',
  'Check for essential API routes',
  async () => {
    const apiDir = 'src/app/api';
    const hasApiDir = checker.directoryExists(apiDir);
    const hasAuthRoutes = checker.directoryExists(`${apiDir}/auth`);
    
    if (hasApiDir && hasAuthRoutes) {
      return { passed: true, message: 'Essential API routes present' };
    } else {
      return { passed: false, message: 'Missing essential API routes' };
    }
  }
);

checker.addCheck(
  'Static Assets',
  'Verify static assets are properly organized',
  async () => {
    const hasPublicDir = checker.directoryExists('public');
    const hasImages = checker.directoryExists('public/images') || 
                     fs.readdirSync('public').some(file => file.match(/\.(png|jpg|jpeg|gif|svg)$/));
    
    if (hasPublicDir) {
      return { passed: true, message: 'Static assets directory present' };
    } else {
      return { passed: false, message: 'Missing public directory for static assets' };
    }
  }
);

// Run all checks
checker.runAllChecks().then((results) => {
  process.exit(results.failed > 0 ? 1 : 0);
}).catch((error) => {
  console.error('💥 Production readiness check failed:', error);
  process.exit(1);
});
