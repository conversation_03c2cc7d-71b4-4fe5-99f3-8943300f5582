#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Critical routes that need CSRF protection immediately
const criticalRoutes = [
  'src/app/api/assessment/route.ts',
  'src/app/api/forum/posts/route.ts',
  'src/app/api/forum/posts/[postId]/replies/route.ts',
  'src/app/api/forum/reactions/route.ts',
  'src/app/api/forum/bookmarks/route.ts',
  'src/app/api/profile/route.ts',
  'src/app/api/achievements/route.ts',
  'src/app/api/learning-progress/route.ts',
  'src/app/api/progress-tracker/route.ts',
  'src/app/api/resource-ratings/route.ts'
];

function addCSRFProtection(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ File not found: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if already has CSRF protection
  if (content.includes('withCSRFProtection') || content.includes('SecurityMiddleware')) {
    console.log(`✅ ${filePath} already has CSRF protection`);
    return true;
  }

  // Check if it has state-changing methods
  const hasStateChangingMethods = /export\s+(async\s+)?function\s+(POST|PUT|DELETE|PATCH)|export\s+const\s+(POST|PUT|DELETE|PATCH)/.test(content);
  
  if (!hasStateChangingMethods) {
    console.log(`ℹ️ ${filePath} has no state-changing methods`);
    return true;
  }

  let modified = false;

  // Add imports if not present
  if (!content.includes('withCSRFProtection')) {
    const importMatch = content.match(/import.*from\s+['"][^'"]*['"];?\s*\n/g);
    if (importMatch) {
      const lastImport = importMatch[importMatch.length - 1];
      const importIndex = content.indexOf(lastImport) + lastImport.length;
      
      const newImports = [];
      if (!content.includes('withCSRFProtection')) {
        newImports.push("import { withCSRFProtection } from '@/lib/csrf';");
      }
      if (!content.includes('withRateLimit')) {
        newImports.push("import { withRateLimit } from '@/lib/rateLimit';");
      }
      
      if (newImports.length > 0) {
        content = content.slice(0, importIndex) + newImports.join('\n') + '\n' + content.slice(importIndex);
        modified = true;
      }
    }
  }

  // Wrap POST handlers
  content = content.replace(
    /export\s+(async\s+)?function\s+POST\s*\([^)]*\)\s*{/g,
    (match) => {
      const asyncKeyword = match.includes('async') ? 'async ' : '';
      return `export ${asyncKeyword}function POST(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {`;
    }
  );

  // Wrap PUT handlers
  content = content.replace(
    /export\s+(async\s+)?function\s+PUT\s*\([^)]*\)\s*{/g,
    (match) => {
      const asyncKeyword = match.includes('async') ? 'async ' : '';
      return `export ${asyncKeyword}function PUT(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 30 },
      async () => {`;
    }
  );

  // Wrap DELETE handlers
  content = content.replace(
    /export\s+(async\s+)?function\s+DELETE\s*\([^)]*\)\s*{/g,
    (match) => {
      const asyncKeyword = match.includes('async') ? 'async ' : '';
      return `export ${asyncKeyword}function DELETE(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 },
      async () => {`;
    }
  );

  // Wrap PATCH handlers
  content = content.replace(
    /export\s+(async\s+)?function\s+PATCH\s*\([^)]*\)\s*{/g,
    (match) => {
      const asyncKeyword = match.includes('async') ? 'async ' : '';
      return `export ${asyncKeyword}function PATCH(request: NextRequest) {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 30 },
      async () => {`;
    }
  );

  // Add closing braces for wrapped functions
  const methodPattern = /export\s+(async\s+)?function\s+(POST|PUT|DELETE|PATCH)/g;
  const methods = [...content.matchAll(methodPattern)];
  
  if (methods.length > 0) {
    // Find the end of each function and add closing braces
    let offset = 0;
    methods.forEach(match => {
      const methodStart = match.index + offset;
      const methodContent = content.slice(methodStart);
      
      // Find the matching closing brace
      let braceCount = 0;
      let inFunction = false;
      let functionEnd = -1;
      
      for (let i = 0; i < methodContent.length; i++) {
        if (methodContent[i] === '{') {
          if (!inFunction) inFunction = true;
          braceCount++;
        } else if (methodContent[i] === '}') {
          braceCount--;
          if (inFunction && braceCount === 0) {
            functionEnd = i;
            break;
          }
        }
      }
      
      if (functionEnd > -1) {
        const beforeClosing = content.slice(0, methodStart + functionEnd);
        const afterClosing = content.slice(methodStart + functionEnd + 1);
        
        // Add the additional closing braces
        content = beforeClosing + '\n      }\n    );\n  });\n}' + afterClosing;
        offset += '\n      }\n    );\n  });\n'.length;
        modified = true;
      }
    });
  }

  // Fix NextRequest import if needed
  if (!content.includes('NextRequest') && content.includes('NextResponse')) {
    content = content.replace(
      /import\s*{\s*NextResponse\s*}\s*from\s*['"]next\/server['"];?/,
      "import { NextRequest, NextResponse } from 'next/server';"
    );
    modified = true;
  }

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Added CSRF protection to ${filePath}`);
    return true;
  } else {
    console.log(`ℹ️ No changes needed for ${filePath}`);
    return true;
  }
}

function main() {
  console.log('🔒 Adding CSRF Protection to Critical Routes\n');
  
  let successCount = 0;
  let totalCount = criticalRoutes.length;
  
  criticalRoutes.forEach(route => {
    const fullPath = path.join(process.cwd(), route);
    if (addCSRFProtection(fullPath)) {
      successCount++;
    }
  });
  
  console.log(`\n📊 Summary:`);
  console.log(`Successfully processed: ${successCount}/${totalCount} routes`);
  console.log(`Protection rate: ${(successCount/totalCount*100).toFixed(1)}%`);
  
  if (successCount === totalCount) {
    console.log('\n✅ All critical routes now have CSRF protection!');
  } else {
    console.log('\n⚠️ Some routes may need manual review');
  }
}

if (require.main === module) {
  main();
}

module.exports = { addCSRFProtection };
