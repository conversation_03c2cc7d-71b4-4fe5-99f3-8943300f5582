#!/usr/bin/env tsx

/**
 * Test Fixed Validation Schema
 * 
 * Test the updated validation schema that handles null values properly
 */

import { z } from 'zod';

// Updated validation schema (same as in the API route)
const submitResponseSchema = z.object({
  questionId: z.string().uuid(),
  responseText: z.string().min(10, 'Response must be at least 10 characters').max(5000, 'Response too long'),
  audioUrl: z.string().url().optional().nullable().transform(val => val || undefined),
  videoUrl: z.string().url().optional().nullable().transform(val => val || undefined),
  responseTime: z.number().min(0).max(3600), // Max 1 hour
  preparationTime: z.number().min(0).max(1800).default(0), // Max 30 minutes
  userNotes: z.string().max(1000).optional().nullable().transform(val => val || undefined),
  requestFeedback: z.boolean().default(true),
});

async function testFixedValidation() {
  console.log('🔍 Testing Fixed Validation Schema');
  console.log('=================================\n');

  const testCases = [
    {
      name: 'Standard Frontend Payload',
      payload: {
        questionId: '123e4567-e89b-12d3-a456-************',
        responseText: 'I would approach system design by first understanding requirements, then designing scalable architecture with microservices.',
        responseTime: 120,
        preparationTime: 30,
        userNotes: 'Test notes',
        requestFeedback: true,
      },
      expectedToPass: true
    },
    {
      name: 'Payload with null values (THE PROBLEMATIC CASE)',
      payload: {
        questionId: '123e4567-e89b-12d3-a456-************',
        responseText: 'I would approach system design by first understanding requirements, then designing scalable architecture with microservices.',
        responseTime: 120,
        preparationTime: 30,
        userNotes: null,
        audioUrl: null,
        videoUrl: null,
        requestFeedback: true,
      },
      expectedToPass: true // Should now pass with the fix
    },
    {
      name: 'Payload with undefined values',
      payload: {
        questionId: '123e4567-e89b-12d3-a456-************',
        responseText: 'I would approach system design by first understanding requirements, then designing scalable architecture with microservices.',
        responseTime: 120,
        preparationTime: 30,
        userNotes: undefined,
        audioUrl: undefined,
        videoUrl: undefined,
        requestFeedback: true,
      },
      expectedToPass: true
    },
    {
      name: 'Payload with empty string values',
      payload: {
        questionId: '123e4567-e89b-12d3-a456-************',
        responseText: 'I would approach system design by first understanding requirements, then designing scalable architecture with microservices.',
        responseTime: 120,
        preparationTime: 30,
        userNotes: '',
        audioUrl: '',
        videoUrl: '',
        requestFeedback: true,
      },
      expectedToPass: false // Empty strings should fail for URLs
    },
    {
      name: 'Minimal payload (no optional fields)',
      payload: {
        questionId: '123e4567-e89b-12d3-a456-************',
        responseText: 'I would approach system design by first understanding requirements.',
        responseTime: 60,
        preparationTime: 0,
      },
      expectedToPass: true
    },
    {
      name: 'Invalid UUID',
      payload: {
        questionId: 'invalid-uuid',
        responseText: 'Valid response text here.',
        responseTime: 60,
        preparationTime: 0,
      },
      expectedToPass: false
    }
  ];

  let passedTests = 0;
  let failedTests = 0;

  for (const testCase of testCases) {
    console.log(`🧪 Testing: ${testCase.name}`);
    console.log(`   Expected to pass: ${testCase.expectedToPass}`);
    console.log(`   Payload:`, JSON.stringify(testCase.payload, null, 4));

    try {
      const result = submitResponseSchema.safeParse(testCase.payload);

      if (result.success) {
        console.log(`   ✅ Validation PASSED`);
        console.log(`   📋 Parsed data:`, JSON.stringify(result.data, null, 4));

        if (testCase.expectedToPass) {
          console.log(`   🎯 Result matches expectation`);
          passedTests++;
        } else {
          console.log(`   ⚠️ Expected to fail but passed`);
          failedTests++;
        }
      } else {
        console.log(`   ❌ Validation FAILED`);
        console.log(`   📋 Errors:`);
        result.error.errors.forEach(error => {
          console.log(`      - ${error.path.join('.')}: ${error.message}`);
        });

        if (!testCase.expectedToPass) {
          console.log(`   🎯 Result matches expectation`);
          passedTests++;
        } else {
          console.log(`   ⚠️ Expected to pass but failed`);
          failedTests++;
        }
      }

    } catch (error) {
      console.log(`   💥 Unexpected error:`, error);
      failedTests++;
    }

    console.log(''); // Empty line for readability
  }

  console.log('📊 Test Summary');
  console.log('===============');
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Total: ${testCases.length}`);

  if (failedTests === 0) {
    console.log('\n🎉 All validation tests passed!');
    console.log('✅ The null value issue has been fixed!');
    console.log('🚀 The interview practice functionality should now work correctly.');
  } else {
    console.log('\n⚠️ Some tests failed - there may still be issues');
  }

  // Test the specific problematic case
  console.log('\n🔍 Specific Test: Frontend Null Values');
  console.log('=====================================');

  const problematicPayload = {
    questionId: '123e4567-e89b-12d3-a456-************',
    responseText: 'I would approach system design by first understanding requirements, then designing scalable architecture with microservices.',
    responseTime: 120,
    preparationTime: 30,
    userNotes: null,
    audioUrl: null,
    videoUrl: null,
    requestFeedback: true,
  };

  console.log('Testing the exact payload that was causing 400 errors...');
  console.log('Payload:', JSON.stringify(problematicPayload, null, 2));

  const result = submitResponseSchema.safeParse(problematicPayload);

  if (result.success) {
    console.log('✅ SUCCESS! The problematic payload now passes validation');
    console.log('📋 Transformed data:', JSON.stringify(result.data, null, 2));
    console.log('');
    console.log('🎯 Key transformations:');
    console.log(`   userNotes: null → ${result.data.userNotes === undefined ? 'undefined' : result.data.userNotes}`);
    console.log(`   audioUrl: null → ${result.data.audioUrl === undefined ? 'undefined' : result.data.audioUrl}`);
    console.log(`   videoUrl: null → ${result.data.videoUrl === undefined ? 'undefined' : result.data.videoUrl}`);
  } else {
    console.log('❌ FAILED! The problematic payload still fails validation');
    console.log('📋 Errors:');
    result.error.errors.forEach(error => {
      console.log(`   - ${error.path.join('.')}: ${error.message}`);
    });
  }
}

// Run the test
if (require.main === module) {
  testFixedValidation().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { testFixedValidation };
