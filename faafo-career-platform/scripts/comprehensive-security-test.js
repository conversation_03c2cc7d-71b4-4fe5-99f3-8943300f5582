#!/usr/bin/env node

/**
 * Comprehensive Security Testing Suite
 * 
 * This script validates all security improvements implemented:
 * - Input sanitization and XSS protection
 * - HTML encoding for user-generated content
 * - Client-side form validation
 * - Authentication state consistency
 * - Error handling and recovery
 * - Session management security
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  testTimeout: 120000, // 2 minutes
  maxRetries: 3,
  outputDir: './test-results/security'
};

let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: [],
  securityMetrics: {},
  coverage: {}
};

// Ensure output directory exists
if (!fs.existsSync(TEST_CONFIG.outputDir)) {
  fs.mkdirSync(TEST_CONFIG.outputDir, { recursive: true });
}

function logTest(testName, passed, message, category = 'general') {
  const result = {
    name: testName,
    passed,
    message,
    category,
    timestamp: new Date().toISOString()
  };
  
  testResults.details.push(result);
  testResults.total++;
  
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}: ${message}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${message}`);
  }
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'pipe',
      shell: true,
      ...options
    });

    let stdout = '';
    let stderr = '';

    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Command failed with code ${code}: ${stderr}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function testInputSanitization() {
  console.log('\n🧪 Testing Input Sanitization...');
  
  try {
    // Test that DOMPurify is properly installed and configured
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    const hasDOMPurify = packageJson.dependencies?.['dompurify'] || packageJson.dependencies?.['isomorphic-dompurify'];
    
    if (hasDOMPurify) {
      logTest('DOMPurify Installation', true, 'DOMPurify libraries are installed', 'sanitization');
    } else {
      logTest('DOMPurify Installation', false, 'DOMPurify libraries not found', 'sanitization');
    }

    // Check if sanitization utilities exist
    const sanitizationFiles = [
      './src/lib/client-validation.ts',
      './src/lib/html-encoding.ts',
      './src/lib/validation.ts'
    ];

    for (const file of sanitizationFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes('sanitize') || content.includes('encodeHtml') || content.includes('DOMPurify')) {
          logTest(`Sanitization File: ${path.basename(file)}`, true, 'Contains sanitization logic', 'sanitization');
        } else {
          logTest(`Sanitization File: ${path.basename(file)}`, false, 'Missing sanitization logic', 'sanitization');
        }
      } else {
        logTest(`Sanitization File: ${path.basename(file)}`, false, 'File not found', 'sanitization');
      }
    }

  } catch (error) {
    logTest('Input Sanitization Test', false, `Error: ${error.message}`, 'sanitization');
  }
}

async function testHtmlEncoding() {
  console.log('\n🧪 Testing HTML Encoding...');
  
  try {
    // Check if HTML encoding utility exists
    const htmlEncodingFile = './src/lib/html-encoding.ts';
    
    if (fs.existsSync(htmlEncodingFile)) {
      const content = fs.readFileSync(htmlEncodingFile, 'utf8');
      
      const requiredFunctions = [
        'encodeHtml',
        'encodeHtmlAttribute',
        'safeDisplayText',
        'safeUserName',
        'SafeText'
      ];

      for (const func of requiredFunctions) {
        if (content.includes(func)) {
          logTest(`HTML Encoding Function: ${func}`, true, 'Function implemented', 'html-encoding');
        } else {
          logTest(`HTML Encoding Function: ${func}`, false, 'Function missing', 'html-encoding');
        }
      }

      // Check if components are using safe display
      const componentFiles = [
        './src/components/forum/ForumPost.tsx',
        './src/components/interview-practice/InterviewResultsAndFeedback.tsx',
        './src/components/career/CareerPathCard.tsx'
      ];

      for (const file of componentFiles) {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8');
          if (content.includes('SafeText') || content.includes('encodeHtml')) {
            logTest(`Safe Display in ${path.basename(file)}`, true, 'Uses safe display functions', 'html-encoding');
          } else {
            logTest(`Safe Display in ${path.basename(file)}`, false, 'Not using safe display', 'html-encoding');
          }
        }
      }

    } else {
      logTest('HTML Encoding Utility', false, 'html-encoding.ts not found', 'html-encoding');
    }

  } catch (error) {
    logTest('HTML Encoding Test', false, `Error: ${error.message}`, 'html-encoding');
  }
}

async function testFormValidation() {
  console.log('\n🧪 Testing Form Validation...');
  
  try {
    // Check if validation utilities exist
    const validationFiles = [
      './src/lib/client-validation.ts',
      './src/hooks/useFormValidation.ts'
    ];

    for (const file of validationFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        const requiredFeatures = [
          'ClientSideValidator',
          'ValidationRule',
          'useFormValidation',
          'maliciousPatterns',
          'SecurityValidator'
        ];

        let featuresFound = 0;
        for (const feature of requiredFeatures) {
          if (content.includes(feature)) {
            featuresFound++;
          }
        }

        const percentage = (featuresFound / requiredFeatures.length) * 100;
        logTest(`Validation Features in ${path.basename(file)}`, percentage >= 60, 
          `${featuresFound}/${requiredFeatures.length} features found (${percentage.toFixed(1)}%)`, 'validation');

      } else {
        logTest(`Validation File: ${path.basename(file)}`, false, 'File not found', 'validation');
      }
    }

    // Check if forms are using the new validation
    const formFiles = [
      './src/components/LoginForm.tsx',
      './src/components/SignupForm.tsx'
    ];

    for (const file of formFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes('useValidatedForm') || content.includes('useFormValidation')) {
          logTest(`Enhanced Validation in ${path.basename(file)}`, true, 'Uses enhanced validation', 'validation');
        } else {
          logTest(`Enhanced Validation in ${path.basename(file)}`, false, 'Not using enhanced validation', 'validation');
        }
      }
    }

  } catch (error) {
    logTest('Form Validation Test', false, `Error: ${error.message}`, 'validation');
  }
}

async function testAuthenticationState() {
  console.log('\n🧪 Testing Authentication State Management...');
  
  try {
    // Check if enhanced auth state management exists
    const authFiles = [
      './src/lib/auth-state-manager.ts',
      './src/hooks/useAuthState.ts',
      './src/app/api/auth/validate-session/route.ts'
    ];

    for (const file of authFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        const authFeatures = [
          'AuthState',
          'validateSession',
          'refreshState',
          'sessionId',
          'lastActivity'
        ];

        let featuresFound = 0;
        for (const feature of authFeatures) {
          if (content.includes(feature)) {
            featuresFound++;
          }
        }

        const percentage = (featuresFound / authFeatures.length) * 100;
        logTest(`Auth Features in ${path.basename(file)}`, percentage >= 60,
          `${featuresFound}/${authFeatures.length} features found (${percentage.toFixed(1)}%)`, 'authentication');

      } else {
        logTest(`Auth File: ${path.basename(file)}`, false, 'File not found', 'authentication');
      }
    }

  } catch (error) {
    logTest('Authentication State Test', false, `Error: ${error.message}`, 'authentication');
  }
}

async function testErrorHandling() {
  console.log('\n🧪 Testing Error Handling...');
  
  try {
    // Check if enhanced error handling exists
    const errorFiles = [
      './src/lib/error-recovery.ts',
      './src/components/EnhancedErrorBoundary.tsx'
    ];

    for (const file of errorFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        const errorFeatures = [
          'ErrorRecoveryManager',
          'withRecovery',
          'EnhancedErrorBoundary',
          'retry',
          'fallback'
        ];

        let featuresFound = 0;
        for (const feature of errorFeatures) {
          if (content.includes(feature)) {
            featuresFound++;
          }
        }

        const percentage = (featuresFound / errorFeatures.length) * 100;
        logTest(`Error Features in ${path.basename(file)}`, percentage >= 60,
          `${featuresFound}/${errorFeatures.length} features found (${percentage.toFixed(1)}%)`, 'error-handling');

      } else {
        logTest(`Error File: ${path.basename(file)}`, false, 'File not found', 'error-handling');
      }
    }

  } catch (error) {
    logTest('Error Handling Test', false, `Error: ${error.message}`, 'error-handling');
  }
}

async function testSessionManagement() {
  console.log('\n🧪 Testing Session Management...');
  
  try {
    // Check if enhanced session management exists
    const sessionFiles = [
      './src/lib/enhanced-session-manager.ts',
      './src/hooks/useEnhancedSessionMonitor.ts'
    ];

    for (const file of sessionFiles) {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        const sessionFeatures = [
          'EnhancedSessionManager',
          'SessionMetadata',
          'SecurityEvent',
          'validateSession',
          'terminateSession'
        ];

        let featuresFound = 0;
        for (const feature of sessionFeatures) {
          if (content.includes(feature)) {
            featuresFound++;
          }
        }

        const percentage = (featuresFound / sessionFeatures.length) * 100;
        logTest(`Session Features in ${path.basename(file)}`, percentage >= 60,
          `${featuresFound}/${sessionFeatures.length} features found (${percentage.toFixed(1)}%)`, 'session-management');

      } else {
        logTest(`Session File: ${path.basename(file)}`, false, 'File not found', 'session-management');
      }
    }

  } catch (error) {
    logTest('Session Management Test', false, `Error: ${error.message}`, 'session-management');
  }
}

async function runTypeScriptCheck() {
  console.log('\n🧪 Running TypeScript Check...');
  
  try {
    const result = await runCommand('npx', ['tsc', '--noEmit'], { timeout: 60000 });
    logTest('TypeScript Compilation', true, 'No TypeScript errors found', 'build');
  } catch (error) {
    logTest('TypeScript Compilation', false, `TypeScript errors: ${error.message}`, 'build');
  }
}

async function runLintCheck() {
  console.log('\n🧪 Running ESLint Check...');
  
  try {
    const result = await runCommand('npx', ['eslint', 'src/', '--ext', '.ts,.tsx'], { timeout: 60000 });
    logTest('ESLint Check', true, 'No linting errors found', 'build');
  } catch (error) {
    // ESLint might return non-zero exit code for warnings, check if it's actually errors
    if (error.message.includes('error')) {
      logTest('ESLint Check', false, `Linting errors found: ${error.message}`, 'build');
    } else {
      logTest('ESLint Check', true, 'Only warnings found, no errors', 'build');
    }
  }
}

async function generateReport() {
  const report = {
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: testResults.total > 0 ? ((testResults.passed / testResults.total) * 100).toFixed(1) : '0.0'
    },
    categories: {},
    details: testResults.details,
    timestamp: new Date().toISOString()
  };

  // Group results by category
  for (const result of testResults.details) {
    if (!report.categories[result.category]) {
      report.categories[result.category] = { passed: 0, failed: 0, total: 0 };
    }
    report.categories[result.category].total++;
    if (result.passed) {
      report.categories[result.category].passed++;
    } else {
      report.categories[result.category].failed++;
    }
  }

  // Write detailed JSON report
  const jsonReportPath = path.join(TEST_CONFIG.outputDir, 'security-test-report.json');
  fs.writeFileSync(jsonReportPath, JSON.stringify(report, null, 2));

  // Write summary text report
  const textReportPath = path.join(TEST_CONFIG.outputDir, 'security-test-summary.txt');
  let textReport = `
🔒 Comprehensive Security Test Report
=====================================

Generated: ${report.timestamp}

📊 SUMMARY
----------
Total Tests: ${report.summary.total}
Passed: ${report.summary.passed}
Failed: ${report.summary.failed}
Success Rate: ${report.summary.successRate}%

📋 CATEGORY BREAKDOWN
--------------------
`;

  for (const [category, stats] of Object.entries(report.categories)) {
    const categoryRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : '0.0';
    textReport += `${category}: ${stats.passed}/${stats.total} (${categoryRate}%)\n`;
  }

  textReport += `\n🔍 DETAILED RESULTS\n-------------------\n`;
  
  for (const result of testResults.details) {
    const status = result.passed ? '✅' : '❌';
    textReport += `${status} [${result.category}] ${result.name}: ${result.message}\n`;
  }

  fs.writeFileSync(textReportPath, textReport);

  console.log(`\n📄 Reports generated:`);
  console.log(`   JSON: ${jsonReportPath}`);
  console.log(`   Text: ${textReportPath}`);
}

// Main test execution
async function runComprehensiveSecurityTest() {
  console.log('🔒 COMPREHENSIVE SECURITY TESTING SUITE');
  console.log('=======================================');
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log(`🎯 Base URL: ${TEST_CONFIG.baseUrl}`);
  
  const testSuites = [
    { name: 'Input Sanitization', fn: testInputSanitization },
    { name: 'HTML Encoding', fn: testHtmlEncoding },
    { name: 'Form Validation', fn: testFormValidation },
    { name: 'Authentication State', fn: testAuthenticationState },
    { name: 'Error Handling', fn: testErrorHandling },
    { name: 'Session Management', fn: testSessionManagement },
    { name: 'TypeScript Check', fn: runTypeScriptCheck },
    { name: 'Lint Check', fn: runLintCheck }
  ];
  
  const startTime = Date.now();
  
  for (const suite of testSuites) {
    console.log(`\n🔬 Security Test Suite: ${suite.name}`);
    console.log('─'.repeat(60));
    
    try {
      await suite.fn();
    } catch (error) {
      logTest(`${suite.name} - Suite Error`, false, error.message, 'suite-error');
    }
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log('\n' + '='.repeat(60));
  console.log('🏁 COMPREHENSIVE SECURITY TEST COMPLETE');
  console.log('='.repeat(60));
  console.log(`⏱️  Total Duration: ${duration}s`);
  console.log(`📊 Tests Passed: ${testResults.passed}/${testResults.total}`);
  console.log(`📈 Success Rate: ${testResults.total > 0 ? ((testResults.passed / testResults.total) * 100).toFixed(1) : '0.0'}%`);
  
  if (testResults.failed > 0) {
    console.log(`\n⚠️  ${testResults.failed} tests failed. Check the detailed report for more information.`);
  } else {
    console.log('\n🎉 All security tests passed! Your application is secure.');
  }
  
  await generateReport();
  
  // Exit with appropriate code
  process.exit(testResults.failed > 0 ? 1 : 0);
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  logTest('Unhandled Rejection', false, reason.toString(), 'system-error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  logTest('Uncaught Exception', false, error.message, 'system-error');
  process.exit(1);
});

// Run the test suite
if (require.main === module) {
  runComprehensiveSecurityTest().catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runComprehensiveSecurityTest,
  testResults
};
