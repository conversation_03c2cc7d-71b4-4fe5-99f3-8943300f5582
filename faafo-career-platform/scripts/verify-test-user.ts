import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function verifyTestUser() {
  try {
    console.log('🔍 Checking test user: <EMAIL>');
    
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('✅ User found:', user.email);
    console.log('📧 Email verified:', !!user.emailVerified);
    console.log('🔒 Account locked:', !!user.lockedUntil && user.lockedUntil > new Date());
    console.log('🔢 Failed attempts:', user.failedLoginAttempts);
    
    // Test password
    const isValid = await bcrypt.compare('testpassword', user.password);
    console.log('🔑 Password "testpassword" valid:', isValid);
    
    if (!isValid) {
      console.log('❌ Password does not match. Updating password...');
      const hashedPassword = await bcrypt.hash('testpassword', 12);
      await prisma.user.update({
        where: { id: user.id },
        data: { 
          password: hashedPassword,
          failedLoginAttempts: 0,
          lockedUntil: null,
          emailVerified: new Date()
        }
      });
      console.log('✅ Password updated to "testpassword"');
    } else {
      console.log('✅ Password is correct');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyTestUser();
