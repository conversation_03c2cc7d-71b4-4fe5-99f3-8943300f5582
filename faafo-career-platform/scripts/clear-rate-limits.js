#!/usr/bin/env node

/**
 * Development utility to clear rate limit cache
 * Run with: node scripts/clear-rate-limits.js
 */

const { PrismaClient } = require('@prisma/client');

async function clearRateLimits() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧹 Clearing rate limit cache...');
    
    // Clear all rate limit entries
    const result = await prisma.rateLimitEntry.deleteMany({});
    
    console.log(`✅ Cleared ${result.count} rate limit entries`);
    console.log('🚀 Rate limit cache cleared successfully!');
    
  } catch (error) {
    console.error('❌ Error clearing rate limits:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  clearRateLimits();
}

module.exports = { clearRateLimits };
