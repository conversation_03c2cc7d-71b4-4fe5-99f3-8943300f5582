#!/usr/bin/env tsx

import { chrom<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  duration: number;
  details: string;
  error?: string;
}

class AuthenticationE2ETester {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;
  private results: TestResult[] = [];
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
  }

  async setup() {
    console.log('🚀 Setting up browser for authentication testing...');
    this.browser = await chromium.launch({ 
      headless: false, // Set to true for CI
      slowMo: 500 // Slow down for debugging
    });
    this.context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    this.page = await this.context.newPage();
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🔴 Browser Error: ${msg.text()}`);
      }
    });
  }

  async teardown() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  private async recordTest(name: string, testFn: () => Promise<void>) {
    const startTime = Date.now();
    console.log(`\n🧪 Testing: ${name}`);
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'PASS',
        duration,
        details: 'Test completed successfully'
      });
      console.log(`✅ ${name} - PASSED (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        details: 'Test failed',
        error: error instanceof Error ? error.message : String(error)
      });
      console.log(`❌ ${name} - FAILED (${duration}ms): ${error}`);
    }
  }

  async testHomepageAccess() {
    await this.recordTest('Homepage Access', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      await this.page.goto(this.baseUrl);
      await this.page.waitForLoadState('networkidle');
      
      const title = await this.page.title();
      if (!title.includes('FAAFO')) {
        throw new Error(`Unexpected page title: ${title}`);
      }
    });
  }

  async testSignInPageAccess() {
    await this.recordTest('Sign In Page Access', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      // Look for sign in link/button
      const signInSelector = 'a[href*="signin"], button:has-text("Sign In"), a:has-text("Sign In")';
      await this.page.waitForSelector(signInSelector, { timeout: 10000 });
      await this.page.click(signInSelector);
      
      // Wait for sign in page to load
      await this.page.waitForLoadState('networkidle');
      
      // Check if we're on the sign in page
      const url = this.page.url();
      if (!url.includes('signin') && !url.includes('auth')) {
        throw new Error(`Not on sign in page. Current URL: ${url}`);
      }
    });
  }

  async testCredentialsLogin() {
    await this.recordTest('Credentials Login', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      // Navigate to sign in page
      await this.page.goto(`${this.baseUrl}/api/auth/signin`);
      await this.page.waitForLoadState('networkidle');
      
      // Look for credentials provider
      const credentialsButton = 'form[action*="credentials"], button:has-text("Sign in with Credentials")';
      await this.page.waitForSelector(credentialsButton, { timeout: 10000 });
      await this.page.click(credentialsButton);
      
      // Fill in credentials
      await this.page.waitForSelector('input[name="email"], input[type="email"]', { timeout: 10000 });
      await this.page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
      
      await this.page.waitForSelector('input[name="password"], input[type="password"]', { timeout: 10000 });
      await this.page.fill('input[name="password"], input[type="password"]', 'testpassword');
      
      // Submit the form
      const submitButton = 'button[type="submit"], input[type="submit"]';
      await this.page.click(submitButton);
      
      // Wait for redirect after successful login
      await this.page.waitForLoadState('networkidle');
      
      // Check if we're redirected to dashboard or home
      const url = this.page.url();
      if (url.includes('signin') || url.includes('error')) {
        throw new Error(`Login failed. Current URL: ${url}`);
      }
    });
  }

  async testAuthenticatedState() {
    await this.recordTest('Authenticated State Verification', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      // Check for authenticated user indicators
      const authIndicators = [
        '[data-testid="user-menu"]',
        'button:has-text("Sign Out")',
        '.user-profile',
        '[data-auth="authenticated"]'
      ];
      
      let found = false;
      for (const selector of authIndicators) {
        try {
          await this.page.waitForSelector(selector, { timeout: 5000 });
          found = true;
          break;
        } catch {
          // Continue to next selector
        }
      }
      
      if (!found) {
        throw new Error('No authenticated state indicators found');
      }
    });
  }

  async testSessionPersistence() {
    await this.recordTest('Session Persistence', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      // Reload the page
      await this.page.reload();
      await this.page.waitForLoadState('networkidle');
      
      // Check if still authenticated
      const authIndicators = [
        '[data-testid="user-menu"]',
        'button:has-text("Sign Out")',
        '.user-profile'
      ];
      
      let found = false;
      for (const selector of authIndicators) {
        try {
          await this.page.waitForSelector(selector, { timeout: 5000 });
          found = true;
          break;
        } catch {
          // Continue to next selector
        }
      }
      
      if (!found) {
        throw new Error('Session not persisted after page reload');
      }
    });
  }

  async testSignOut() {
    await this.recordTest('Sign Out', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      // Look for sign out button
      const signOutSelectors = [
        'button:has-text("Sign Out")',
        'a:has-text("Sign Out")',
        '[data-testid="signout"]',
        'button:has-text("Logout")'
      ];
      
      let signOutButton = null;
      for (const selector of signOutSelectors) {
        try {
          signOutButton = await this.page.waitForSelector(selector, { timeout: 5000 });
          break;
        } catch {
          // Continue to next selector
        }
      }
      
      if (!signOutButton) {
        throw new Error('Sign out button not found');
      }
      
      await signOutButton.click();
      await this.page.waitForLoadState('networkidle');
      
      // Check if signed out (should see sign in button again)
      const signInIndicators = [
        'button:has-text("Sign In")',
        'a:has-text("Sign In")',
        '[data-auth="unauthenticated"]'
      ];
      
      let found = false;
      for (const selector of signInIndicators) {
        try {
          await this.page.waitForSelector(selector, { timeout: 5000 });
          found = true;
          break;
        } catch {
          // Continue to next selector
        }
      }
      
      if (!found) {
        throw new Error('Still appears to be authenticated after sign out');
      }
    });
  }

  async testInvalidCredentials() {
    await this.recordTest('Invalid Credentials Rejection', async () => {
      if (!this.page) throw new Error('Page not initialized');
      
      // Navigate to sign in page
      await this.page.goto(`${this.baseUrl}/api/auth/signin`);
      await this.page.waitForLoadState('networkidle');
      
      // Look for credentials provider
      const credentialsButton = 'form[action*="credentials"], button:has-text("Sign in with Credentials")';
      await this.page.waitForSelector(credentialsButton, { timeout: 10000 });
      await this.page.click(credentialsButton);
      
      // Fill in invalid credentials
      await this.page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
      await this.page.fill('input[name="password"], input[type="password"]', 'wrongpassword');
      
      // Submit the form
      const submitButton = 'button[type="submit"], input[type="submit"]';
      await this.page.click(submitButton);
      
      await this.page.waitForLoadState('networkidle');
      
      // Should see error message or stay on sign in page
      const url = this.page.url();
      const hasError = await this.page.locator('text=error, text=invalid, text=incorrect').count() > 0;
      
      if (!url.includes('signin') && !url.includes('error') && !hasError) {
        throw new Error('Invalid credentials were accepted');
      }
    });
  }

  async runAllTests() {
    console.log('🔐 Starting Authentication End-to-End Tests...\n');
    
    try {
      await this.setup();
      
      await this.testHomepageAccess();
      await this.testSignInPageAccess();
      await this.testCredentialsLogin();
      await this.testAuthenticatedState();
      await this.testSessionPersistence();
      await this.testSignOut();
      await this.testInvalidCredentials();
      
    } finally {
      await this.teardown();
    }
    
    this.printResults();
  }

  private printResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(50));
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;
    
    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${icon} ${result.name} (${result.duration}ms)`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    console.log('=' .repeat(50));
    console.log(`Total: ${total} | Passed: ${passed} | Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 All authentication tests passed!');
    } else {
      console.log(`\n⚠️  ${failed} test(s) failed. Please review the errors above.`);
    }
  }
}

async function main() {
  const tester = new AuthenticationE2ETester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { AuthenticationE2ETester };
