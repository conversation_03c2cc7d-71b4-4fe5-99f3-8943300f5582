#!/usr/bin/env node

/**
 * Test Script for Immediate Optional Improvements
 * 
 * Tests the three improvements we implemented:
 * 1. H1 headings for better SEO
 * 2. CSRF protection for forms
 * 3. Increased touch target sizes for mobile
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3004', // Updated to correct port
  testTimeout: 30000,
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  }
};

let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

function logTest(testName, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}: PASSED ${details ? `- ${details}` : ''}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: FAILED ${details ? `- ${details}` : ''}`);
  }
  testResults.details.push({ testName, passed, details });
}

// Test 1: Server Availability on Correct Port
async function testServerAvailability() {
  console.log('\n🌐 Testing Server Availability...');
  
  try {
    const serverCheck = execSync(`curl -s -o /dev/null -w "%{http_code}" ${TEST_CONFIG.baseUrl}`, 
      { encoding: 'utf8', timeout: 5000 });
    
    const serverRunning = serverCheck === '200' || serverCheck === '404';
    logTest('Development Server (Port 3004)', serverRunning, 
      `HTTP Status: ${serverCheck}`);
    
    return serverRunning;
    
  } catch (error) {
    logTest('Server Availability', false, error.message);
    return false;
  }
}

// Test 2: H1 Headings for SEO
async function testH1Headings() {
  console.log('\n📝 Testing H1 Headings for SEO...');
  
  try {
    // Test pages that should have H1 headings
    const pagesToTest = [
      { path: '/', name: 'Homepage' },
      { path: '/signup', name: 'Signup Page' },
      { path: '/assessment', name: 'Assessment Page' },
      { path: '/career-paths', name: 'Career Paths Page' },
      { path: '/contact', name: 'Contact Page' },
      { path: '/forum', name: 'Forum Page' },
      { path: '/resources', name: 'Resources Page' }
    ];
    
    let h1TestsPassed = 0;
    
    for (const page of pagesToTest) {
      try {
        // Fetch page content and check for H1 tags
        const pageContent = execSync(`curl -s ${TEST_CONFIG.baseUrl}${page.path}`, 
          { encoding: 'utf8', timeout: 10000 });
        
        const hasH1 = pageContent.includes('<h1') || pageContent.includes('h1 className');
        if (hasH1) h1TestsPassed++;
        
        logTest(`H1 Heading: ${page.name}`, hasH1, 
          hasH1 ? 'H1 tag found' : 'No H1 tag found');
          
      } catch (error) {
        logTest(`H1 Heading: ${page.name}`, false, `Error fetching page: ${error.message}`);
      }
    }
    
    logTest('H1 Headings Coverage', 
      h1TestsPassed >= pagesToTest.length * 0.8,
      `${h1TestsPassed}/${pagesToTest.length} pages have H1 headings`);
    
    return true;
    
  } catch (error) {
    logTest('H1 Headings Testing', false, error.message);
    return false;
  }
}

// Test 3: CSRF Protection Implementation
async function testCSRFProtection() {
  console.log('\n🔒 Testing CSRF Protection...');
  
  try {
    // Check if CSRF token endpoint exists
    try {
      const csrfResponse = execSync(`curl -s -o /dev/null -w "%{http_code}" ${TEST_CONFIG.baseUrl}/api/csrf-token`, 
        { encoding: 'utf8', timeout: 5000 });
      
      const csrfEndpointExists = csrfResponse === '200' || csrfResponse === '405';
      logTest('CSRF Token Endpoint', csrfEndpointExists, 
        `Status: ${csrfResponse}`);
        
    } catch (error) {
      logTest('CSRF Token Endpoint', false, error.message);
    }
    
    // Check if CSRF hooks exist in codebase
    const csrfHookExists = fs.existsSync('src/hooks/useCSRF.ts');
    logTest('CSRF Hook Implementation', csrfHookExists, 
      csrfHookExists ? 'useCSRF hook found' : 'useCSRF hook missing');
    
    // Check if forms have CSRF protection
    const formsToCheck = [
      'src/app/signup/page.tsx',
      'src/app/contact/page.tsx',
      'src/app/forum/new/page.tsx'
    ];
    
    let formsWithCSRF = 0;
    
    for (const formFile of formsToCheck) {
      try {
        if (fs.existsSync(formFile)) {
          const fileContent = fs.readFileSync(formFile, 'utf8');
          const hasCSRF = fileContent.includes('useCSRF') || fileContent.includes('getHeaders');
          if (hasCSRF) formsWithCSRF++;
          
          logTest(`CSRF Protection: ${path.basename(formFile)}`, hasCSRF,
            hasCSRF ? 'CSRF implementation found' : 'No CSRF implementation');
        } else {
          logTest(`CSRF Protection: ${path.basename(formFile)}`, false, 'File not found');
        }
      } catch (error) {
        logTest(`CSRF Protection: ${path.basename(formFile)}`, false, error.message);
      }
    }
    
    logTest('CSRF Forms Coverage', 
      formsWithCSRF >= formsToCheck.length * 0.8,
      `${formsWithCSRF}/${formsToCheck.length} forms have CSRF protection`);
    
    return true;
    
  } catch (error) {
    logTest('CSRF Protection Testing', false, error.message);
    return false;
  }
}

// Test 4: Touch Target Sizes for Mobile
async function testTouchTargetSizes() {
  console.log('\n📱 Testing Touch Target Sizes...');
  
  try {
    // Check UI components for proper touch target sizes
    const componentsToCheck = [
      { file: 'src/components/ui/button.tsx', name: 'Button Component' },
      { file: 'src/components/ui/input.tsx', name: 'Input Component' },
      { file: 'src/components/ui/checkbox.tsx', name: 'Checkbox Component' },
      { file: 'src/components/ui/select.tsx', name: 'Select Component' },
      { file: 'src/components/ui/switch.tsx', name: 'Switch Component' }
    ];
    
    let componentsWithProperTargets = 0;
    
    for (const component of componentsToCheck) {
      try {
        if (fs.existsSync(component.file)) {
          const fileContent = fs.readFileSync(component.file, 'utf8');
          
          // Check for minimum touch target size indicators
          const hasMinHeight = fileContent.includes('min-h-[44px]') || 
                              fileContent.includes('min-h-[40px]') ||
                              fileContent.includes('h-11') ||
                              fileContent.includes('h-12');
          
          const hasMinWidth = fileContent.includes('min-w-[44px]') || 
                             fileContent.includes('min-w-[40px]');
          
          const hasTouchTargets = hasMinHeight || hasMinWidth;
          if (hasTouchTargets) componentsWithProperTargets++;
          
          logTest(`Touch Targets: ${component.name}`, hasTouchTargets,
            hasTouchTargets ? 'Proper touch target sizes found' : 'No touch target improvements');
            
        } else {
          logTest(`Touch Targets: ${component.name}`, false, 'Component file not found');
        }
      } catch (error) {
        logTest(`Touch Targets: ${component.name}`, false, error.message);
      }
    }
    
    logTest('Touch Target Coverage', 
      componentsWithProperTargets >= componentsToCheck.length * 0.8,
      `${componentsWithProperTargets}/${componentsToCheck.length} components have proper touch targets`);
    
    return true;
    
  } catch (error) {
    logTest('Touch Target Testing', false, error.message);
    return false;
  }
}

// Main testing execution
async function runImprovementsTesting() {
  console.log('🚀 IMMEDIATE OPTIONAL IMPROVEMENTS TESTING');
  console.log('==========================================');
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log(`🌐 Base URL: ${TEST_CONFIG.baseUrl}`);
  console.log(`👤 Test User: ${TEST_CONFIG.testUser.email}`);
  
  const testSuites = [
    { name: 'Server Availability', fn: testServerAvailability },
    { name: 'H1 Headings for SEO', fn: testH1Headings },
    { name: 'CSRF Protection', fn: testCSRFProtection },
    { name: 'Touch Target Sizes', fn: testTouchTargetSizes }
  ];
  
  console.log(`\n🧪 Running ${testSuites.length} test suites...\n`);
  
  for (const suite of testSuites) {
    console.log(`🔬 Test Suite: ${suite.name}`);
    console.log('─'.repeat(60));
    
    try {
      await suite.fn();
    } catch (error) {
      logTest(`${suite.name} Suite`, false, error.message);
    }
  }
  
  // Generate final report
  console.log('\n📊 IMPROVEMENTS TEST RESULTS');
  console.log('============================');
  console.log(`✅ Passed: ${testResults.passed}/${testResults.total}`);
  console.log(`❌ Failed: ${testResults.failed}/${testResults.total}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / testResults.total) * 100)}%`);
  
  const overallSuccess = testResults.passed >= testResults.total * 0.8;
  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}`);
  
  if (overallSuccess) {
    console.log('\n🎉 All immediate optional improvements have been successfully implemented!');
    console.log('✨ Your application now has:');
    console.log('   • Better SEO with proper H1 headings');
    console.log('   • Enhanced security with CSRF protection');
    console.log('   • Improved mobile accessibility with proper touch targets');
  } else {
    console.log('\n⚠️  Some improvements need attention. Please review the failed tests above.');
  }
  
  return overallSuccess;
}

// Execute the testing
if (require.main === module) {
  runImprovementsTesting()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Testing failed:', error);
      process.exit(1);
    });
}

module.exports = { runImprovementsTesting };
