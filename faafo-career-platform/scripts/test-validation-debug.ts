#!/usr/bin/env tsx

/**
 * Interview Practice Validation Debug Script
 * 
 * Direct testing of validation schemas to identify the 400 Bad Request issue
 * without requiring the server to be running.
 */

import { z } from 'zod';

// Copy the exact validation schema from the API route
const submitResponseSchema = z.object({
  questionId: z.string().uuid(),
  responseText: z.string().min(10, 'Response must be at least 10 characters').max(5000, 'Response too long'),
  audioUrl: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  responseTime: z.number().min(0).max(3600), // Max 1 hour
  preparationTime: z.number().min(0).max(1800).default(0), // Max 30 minutes
  userNotes: z.string().max(1000).optional(),
  requestFeedback: z.boolean().default(true),
});

interface TestCase {
  name: string;
  payload: any;
  expectedToPass: boolean;
  description: string;
}

class ValidationDebugger {
  
  async runValidationTests(): Promise<void> {
    console.log('🔍 Interview Practice Validation Debug');
    console.log('=====================================\n');

    const testCases: TestCase[] = [
      {
        name: 'Valid Standard Payload',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'This is a comprehensive technical response about system design. I would approach this problem by first understanding the requirements.',
          responseTime: 120,
          preparationTime: 30,
          userNotes: 'Good question',
          requestFeedback: true,
        },
        expectedToPass: true,
        description: 'Standard valid payload that should work'
      },
      {
        name: 'Frontend Typical Payload',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'This is a typical response from the frontend interface.',
          responseTime: 60,
          preparationTime: 15,
          userNotes: 'Test notes',
          requestFeedback: true,
        },
        expectedToPass: true,
        description: 'Typical payload sent from frontend'
      },
      {
        name: 'Minimal Required Fields',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Minimum length response text here.',
          responseTime: 30,
          preparationTime: 0,
        },
        expectedToPass: true,
        description: 'Only required fields provided'
      },
      {
        name: 'Invalid UUID',
        payload: {
          questionId: 'invalid-uuid',
          responseText: 'Valid response text here.',
          responseTime: 30,
          preparationTime: 0,
        },
        expectedToPass: false,
        description: 'Invalid UUID format'
      },
      {
        name: 'Response Too Short',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Short',
          responseTime: 30,
          preparationTime: 0,
        },
        expectedToPass: false,
        description: 'Response text under 10 characters'
      },
      {
        name: 'Response Too Long',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'A'.repeat(5001),
          responseTime: 30,
          preparationTime: 0,
        },
        expectedToPass: false,
        description: 'Response text over 5000 characters'
      },
      {
        name: 'Invalid Response Time',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Valid response text here.',
          responseTime: 4000, // Over 3600 limit
          preparationTime: 0,
        },
        expectedToPass: false,
        description: 'Response time over 1 hour limit'
      },
      {
        name: 'Invalid Preparation Time',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Valid response text here.',
          responseTime: 30,
          preparationTime: 2000, // Over 1800 limit
        },
        expectedToPass: false,
        description: 'Preparation time over 30 minute limit'
      },
      {
        name: 'Negative Times',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Valid response text here.',
          responseTime: -10,
          preparationTime: -5,
        },
        expectedToPass: false,
        description: 'Negative time values'
      },
      {
        name: 'String Times (Common Frontend Issue)',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Valid response text here.',
          responseTime: '120', // String instead of number
          preparationTime: '30', // String instead of number
        },
        expectedToPass: false,
        description: 'Time values as strings instead of numbers'
      },
      {
        name: 'Missing Required Fields',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          // Missing responseText, responseTime, preparationTime
        },
        expectedToPass: false,
        description: 'Missing required fields'
      },
      {
        name: 'Extra Fields',
        payload: {
          questionId: '123e4567-e89b-12d3-a456-************',
          responseText: 'Valid response text here.',
          responseTime: 120,
          preparationTime: 30,
          extraField: 'should be ignored',
          anotherExtra: 123,
        },
        expectedToPass: true,
        description: 'Extra fields that should be ignored'
      }
    ];

    let passedTests = 0;
    let failedTests = 0;

    for (const testCase of testCases) {
      console.log(`🧪 Testing: ${testCase.name}`);
      console.log(`   Description: ${testCase.description}`);
      console.log(`   Expected to pass: ${testCase.expectedToPass}`);
      
      try {
        const result = submitResponseSchema.safeParse(testCase.payload);
        
        if (result.success) {
          console.log(`   ✅ Validation passed`);
          console.log(`   📋 Parsed data:`, JSON.stringify(result.data, null, 4));
          
          if (testCase.expectedToPass) {
            console.log(`   🎯 Result matches expectation`);
            passedTests++;
          } else {
            console.log(`   ⚠️ Expected to fail but passed`);
            failedTests++;
          }
        } else {
          console.log(`   ❌ Validation failed`);
          console.log(`   📋 Errors:`);
          result.error.errors.forEach(error => {
            console.log(`      - ${error.path.join('.')}: ${error.message}`);
          });
          
          if (!testCase.expectedToPass) {
            console.log(`   🎯 Result matches expectation`);
            passedTests++;
          } else {
            console.log(`   ⚠️ Expected to pass but failed`);
            failedTests++;
          }
        }
        
      } catch (error) {
        console.log(`   💥 Unexpected error:`, error);
        failedTests++;
      }
      
      console.log(''); // Empty line for readability
    }

    console.log('📊 Test Summary');
    console.log('===============');
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📈 Total: ${testCases.length}`);

    if (failedTests === 0) {
      console.log('\n🎉 All validation tests passed!');
    } else {
      console.log('\n⚠️ Some tests failed - validation schema may have issues');
    }

    // Now test the specific payload that's failing in the frontend
    console.log('\n🔍 Testing Frontend Payload Simulation');
    console.log('======================================');
    
    this.testFrontendPayload();
  }

  private testFrontendPayload(): void {
    console.log('🔄 Simulating exact frontend payload...');
    
    // This simulates what the frontend is actually sending
    const frontendPayload = {
      questionId: '123e4567-e89b-12d3-a456-************', // Valid UUID
      responseText: 'This is a comprehensive technical response about system design. I would approach this problem by first understanding the requirements, then designing a scalable architecture using microservices, implementing proper caching strategies, and ensuring high availability through load balancing and redundancy.',
      responseTime: 120, // 2 minutes in seconds
      preparationTime: 30, // 30 seconds
      userNotes: 'Good question about system design',
      requestFeedback: true,
    };

    console.log('Frontend payload:', JSON.stringify(frontendPayload, null, 2));

    const result = submitResponseSchema.safeParse(frontendPayload);
    
    if (result.success) {
      console.log('✅ Frontend payload validation PASSED');
      console.log('📋 This means the validation schema is working correctly');
      console.log('🔍 The 400 error must be coming from somewhere else');
      console.log('📋 Parsed data:', JSON.stringify(result.data, null, 2));
    } else {
      console.log('❌ Frontend payload validation FAILED');
      console.log('🔍 This is likely the source of the 400 error');
      console.log('📋 Validation errors:');
      result.error.errors.forEach(error => {
        console.log(`   - ${error.path.join('.')}: ${error.message}`);
      });
    }

    // Test edge cases that might be happening
    console.log('\n🔍 Testing Edge Cases');
    console.log('=====================');

    const edgeCases = [
      {
        name: 'Undefined values',
        payload: {
          ...frontendPayload,
          userNotes: undefined,
          audioUrl: undefined,
          videoUrl: undefined,
        }
      },
      {
        name: 'Null values',
        payload: {
          ...frontendPayload,
          userNotes: null,
          audioUrl: null,
          videoUrl: null,
        }
      },
      {
        name: 'Empty string values',
        payload: {
          ...frontendPayload,
          userNotes: '',
          audioUrl: '',
          videoUrl: '',
        }
      },
      {
        name: 'Float time values',
        payload: {
          ...frontendPayload,
          responseTime: 120.5,
          preparationTime: 30.7,
        }
      }
    ];

    edgeCases.forEach(testCase => {
      console.log(`\n🧪 Edge case: ${testCase.name}`);
      const result = submitResponseSchema.safeParse(testCase.payload);
      
      if (result.success) {
        console.log('   ✅ Passed');
      } else {
        console.log('   ❌ Failed');
        result.error.errors.forEach(error => {
          console.log(`      - ${error.path.join('.')}: ${error.message}`);
        });
      }
    });
  }
}

async function main() {
  const validator = new ValidationDebugger();
  await validator.runValidationTests();
}

// Run the validation debug
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main as debugValidation };
