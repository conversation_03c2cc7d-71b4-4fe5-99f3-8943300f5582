# Technical Troubleshooting Guide
> **Document Type**: Technical Reference  
> **Date**: 2025-01-20  
> **Audience**: AI Agents & Developers  
> **Next.js Version**: 14.2.5

## 🔍 **Diagnostic Framework**

### **Problem Classification System**

#### **Level 1: Critical (App Won't Start)**
- React rendering errors
- Missing critical imports
- TypeScript compilation failures
- Build process failures

#### **Level 2: Functional (App Starts, Features Broken)**
- Component functionality issues
- asChild patterns not working
- Navigation problems
- Page-specific errors

#### **Level 3: Cosmetic (App Works, UX Issues)**
- Styling problems
- Unnecessary UI elements
- Performance warnings
- Non-critical console errors

---

## 🛠 **Systematic Debugging Process**

### **Phase 1: Initial Assessment (5 minutes)**

#### **Quick Health Check**
```bash
# 1. Check if app starts
npm run dev

# 2. Check build status  
npm run build

# 3. Check for obvious errors
grep -r "Error\|Failed" . --include="*.log" --include="*.txt"
```

#### **Common Error Patterns**
```typescript
// Pattern 1: Missing Slot import
"Cannot find name 'Slot'" 
→ Add: import { Slot } from "@radix-ui/react-slot"

// Pattern 2: Suspense boundary missing
"useSearchParams() should be wrapped in a suspense boundary"
→ Wrap component in <Suspense>

// Pattern 3: Dynamic server usage
"couldn't be rendered statically because it used `headers`"
→ Add: export const dynamic = 'force-dynamic'

// Pattern 4: TypeScript cloneElement issues
"Object literal may only specify known properties"
→ Fix typing: React.ReactElement<any>
```

### **Phase 2: Component Analysis (10 minutes)**

#### **Critical Components to Check**
```typescript
// 1. Badge Component
File: src/components/ui/badge.tsx
Check: Slot import, asChild implementation
Fix: import { Slot } from "@radix-ui/react-slot"

// 2. Button Component  
File: src/components/ui/button.tsx
Check: asChild functionality working
Verify: Comp = asChild ? Slot : "button"

// 3. CollapsibleTrigger
File: src/components/ui/collapsible.tsx
Check: React.cloneElement typing
Fix: Use React.ReactElement<any> typing

// 4. Navigation Bar
File: src/components/layout/NavigationBar.tsx
Check: No "Authenticated" text displayed
Remove: AuthStatusIndicator component
```

#### **Page-Level Components**
```typescript
// 1. Dashboard Page
File: src/app/dashboard/page.tsx
Check: useSearchParams wrapped in Suspense
Pattern: <Suspense><ComponentWithSearchParams /></Suspense>

// 2. Resume Builder Page
File: src/app/resume-builder/page.tsx  
Check: useSearchParams wrapped in Suspense
Add: export const dynamic = 'force-dynamic'

// 3. Tools/Resources Pages
Files: src/app/tools/page.tsx, src/app/resources/page.tsx
Check: Button-Link patterns use asChild correctly
Pattern: <Button asChild><Link /></Button>
```

### **Phase 3: Build Verification (5 minutes)**

#### **Build Process Validation**
```bash
# 1. Clean build
rm -rf .next
npm run build

# 2. Check specific error types
npm run build 2>&1 | grep -E "(Error|Failed|Warning)"

# 3. Verify page generation
# Should see: "Generating static pages (82/82)"
```

#### **Success Criteria**
```
✅ TypeScript compilation: No errors
✅ Page generation: 82/82 pages successful
✅ Bundle optimization: Completed
✅ Static analysis: No critical issues
```

---

## 🔧 **Component-Specific Fixes**

### **Badge Component Issues**

#### **Problem**: `Cannot find name 'Slot'`
```typescript
// BEFORE (Broken)
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

function Badge({ asChild = false, ...props }) {
  const Comp = asChild ? Slot : "span" // ❌ Slot not imported
}

// AFTER (Fixed)
import * as React from "react"
import { Slot } from "@radix-ui/react-slot" // ✅ Added import
import { cva, type VariantProps } from "class-variance-authority"

function Badge({ asChild = false, ...props }) {
  const Comp = asChild ? Slot : "span" // ✅ Works now
}
```

### **CollapsibleTrigger TypeScript Issues**

#### **Problem**: `Object literal may only specify known properties`
```typescript
// BEFORE (Broken)
if (asChild) {
  return React.cloneElement(children as React.ReactElement, {
    className: cn(className, children.props.className) // ❌ TypeScript error
  });
}

// AFTER (Fixed)
if (asChild && React.isValidElement(children)) {
  const element = children as React.ReactElement<any>; // ✅ Proper typing
  return React.cloneElement(element, {
    ...element.props,
    className: cn(className, element.props.className) // ✅ Works now
  });
}
```

### **Suspense Boundary Implementation**

#### **Problem**: `useSearchParams() should be wrapped in a suspense boundary`
```typescript
// BEFORE (Broken)
export default function DashboardPage() {
  const searchParams = useSearchParams(); // ❌ No Suspense
  // ... component logic
}

// AFTER (Fixed)
function DashboardContent() {
  const searchParams = useSearchParams(); // ✅ Inside Suspense
  // ... component logic
}

export default function DashboardPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}> {/* ✅ Suspense wrapper */}
      <DashboardContent />
    </Suspense>
  );
}
```

---

## 📊 **Error Pattern Recognition**

### **Import-Related Errors**
```bash
# Pattern: Missing imports
"Cannot find name 'X'"
"Module not found: Can't resolve 'X'"

# Solution: Add missing imports
import { X } from "appropriate-package"
```

### **React Rendering Errors**
```bash
# Pattern: SSR/Hydration issues
"Text content does not match server-rendered HTML"
"useSearchParams() should be wrapped in a suspense boundary"

# Solution: Add Suspense boundaries or force dynamic rendering
export const dynamic = 'force-dynamic'
```

### **TypeScript Compilation Errors**
```bash
# Pattern: Type mismatches
"Object literal may only specify known properties"
"Property 'X' does not exist on type 'Y'"

# Solution: Fix typing with proper type assertions
const element = children as React.ReactElement<any>;
```

### **Build Process Errors**
```bash
# Pattern: Static generation failures
"couldn't be rendered statically because it used `headers`"
"Dynamic server usage"

# Solution: Mark routes as dynamic
export const dynamic = 'force-dynamic'
```

---

## 🎯 **Testing Strategy**

### **Component-Level Testing**
```typescript
// Test each component individually
// 1. Badge with asChild
<Badge asChild><Link href="/test">Test</Link></Badge>

// 2. Button with asChild  
<Button asChild><Link href="/test">Test</Link></Button>

// 3. CollapsibleTrigger with asChild
<CollapsibleTrigger asChild>
  <button>Trigger</button>
</CollapsibleTrigger>
```

### **Page-Level Testing**
```bash
# Test critical pages
1. http://localhost:3001/dashboard
2. http://localhost:3001/resume-builder  
3. http://localhost:3001/tools
4. http://localhost:3001/resources

# Check for:
- Page loads without errors
- useSearchParams works
- Navigation functional
- No React errors in console
```

### **Build Testing**
```bash
# Production build test
npm run build
npm start

# Check production behavior
- All pages accessible
- Static generation successful
- No runtime errors
```

---

## 🚀 **Performance Optimization**

### **Bundle Analysis**
```bash
# Check bundle sizes
npm run build | grep -A 20 "Route (app)"

# Look for:
- Reasonable bundle sizes
- Proper code splitting
- No duplicate dependencies
```

### **Runtime Performance**
```bash
# Check for performance issues
- Large bundle sizes (>500kB)
- Slow page loads (>3s)
- Memory leaks
- Excessive re-renders
```

---

## 📋 **Maintenance Checklist**

### **Regular Health Checks**
```bash
□ npm run build succeeds
□ All 82 pages generate
□ No TypeScript errors
□ No React console errors
□ All navigation works
□ Critical user flows functional
```

### **Code Quality Checks**
```bash
□ No unused imports
□ Proper TypeScript typing
□ Consistent component patterns
□ No debugging artifacts
□ Clean console output
```

### **Documentation Updates**
```bash
□ Update this guide with new issues found
□ Document any new patterns discovered
□ Keep troubleshooting steps current
□ Add new component fixes as needed
```

---

## 🆘 **Emergency Recovery**

### **When Everything Breaks**
1. **Stop and assess** - Don't make random changes
2. **Check git history** - What changed recently?
3. **Follow systematic process** - Use this guide step by step
4. **Test incrementally** - Fix one thing at a time
5. **Document findings** - Update this guide

### **Nuclear Option**
```bash
# Complete reset and rebuild
rm -rf node_modules .next
npm install
npm run build
```

### **Get Back to Working State**
1. Follow the complete restoration journey document
2. Apply fixes in the exact order documented
3. Test after each major change
4. Don't skip verification steps

---

## ✅ **Success Validation**

### **Application Health Indicators**
```
🟢 Build: 82/82 pages successful
🟢 TypeScript: No compilation errors  
🟢 Runtime: No React errors
🟢 Navigation: All links functional
🟢 Components: All asChild patterns working
🟢 Performance: Reasonable bundle sizes
🟢 UX: Clean interface, no debugging artifacts
```

**Status**: Ready for production deployment! 🚀
