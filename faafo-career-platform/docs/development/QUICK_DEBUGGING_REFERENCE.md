# Quick Debugging Reference Guide
> **Document Type**: Emergency Reference  
> **Date**: 2025-01-20  
> **For**: Future AI Agents  

## 🚨 **Emergency Checklist**

### **React Rendering Errors**
```bash
# Check these immediately:
□ Missing @radix-ui/react-slot import in Badge/Button components
□ asChild patterns incorrectly implemented  
□ useSearchParams without Suspense boundary
□ Complex React components in 404/500 pages
□ TypeScript errors in CollapsibleTrigger
```

### **Build Failures**
```bash
# Common build issues:
□ "Cannot find name 'Slot'" → Add import { Slot } from "@radix-ui/react-slot"
□ "useSearchParams() should be wrapped in suspense" → Add Suspense boundary
□ "Dynamic server usage" → Add export const dynamic = 'force-dynamic'
□ TypeScript cloneElement errors → Fix typing with React.ReactElement<any>
```

---

## 🔧 **Instant Fixes**

### **1. Badge Component Missing Slot**
```typescript
// File: src/components/ui/badge.tsx
// Add this import at the top:
import { Slot } from "@radix-ui/react-slot"
```

### **2. Suspense Boundary for Search Params**
```typescript
// File: src/app/dashboard/page.tsx or src/app/resume-builder/page.tsx
import { Suspense } from 'react'

function ContentComponent() {
  const searchParams = useSearchParams(); // This needs Suspense
  // ... rest of component
}

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ContentComponent />
    </Suspense>
  );
}
```

### **3. Fix Link-Button Patterns**
```typescript
// WRONG:
<Link href="/path">
  <Button>Text</Button>
</Link>

// CORRECT:
<Button asChild>
  <Link href="/path">Text</Link>
</Button>
```

### **4. CollapsibleTrigger TypeScript Fix**
```typescript
// File: src/components/ui/collapsible.tsx
if (asChild && React.isValidElement(children)) {
  const element = children as React.ReactElement<any>;
  return React.cloneElement(element, {
    ...element.props,
    className: cn(className, element.props.className)
  });
}
```

---

## 📋 **Verification Steps**

### **After Each Fix**
```bash
1. npm run build          # Check if build succeeds
2. npm run dev           # Test in development  
3. Check browser console # Look for React errors
4. Test affected pages   # Verify functionality
```

### **Final Verification**
```bash
□ All 82 pages build successfully
□ No TypeScript errors
□ All asChild patterns working
□ Navigation functional
□ No "Authenticated" text in nav
□ Dashboard and Resume Builder load correctly
```

---

## 🎯 **Key Files to Check**

### **Always Verify These Files**
```
src/components/ui/badge.tsx          # Slot import
src/components/ui/button.tsx         # asChild functionality  
src/components/ui/collapsible.tsx    # TypeScript issues
src/app/dashboard/page.tsx           # Suspense boundary
src/app/resume-builder/page.tsx      # Suspense boundary
src/components/layout/NavigationBar.tsx # Remove "Authenticated"
```

### **Pages with Link-Button Patterns**
```
src/app/tools/page.tsx               # Button asChild usage
src/app/resources/page.tsx           # Button asChild usage
```

---

## ⚡ **Emergency Commands**

```bash
# Quick build check
npm run build 2>&1 | grep -E "(Error|Failed)"

# Find missing imports
grep -r "Cannot find name" . --include="*.tsx" --include="*.ts"

# Find useSearchParams without Suspense
grep -r "useSearchParams" . --include="*.tsx" | grep -v "Suspense"

# Check for asChild issues
grep -r "asChild" . --include="*.tsx" | head -10
```

---

## 🚀 **Success Indicators**

### **Build Success**
```
✅ Compiled successfully
✅ Checking validity of types
✅ Collecting page data  
✅ Generating static pages (82/82)
✅ Finalizing page optimization
```

### **Application Working**
```
✅ http://localhost:3001 loads
✅ Navigation works
✅ Dashboard loads (with Suspense)
✅ Resume Builder loads (with Suspense)  
✅ All buttons clickable
✅ No React errors in console
```

---

## 📞 **When All Else Fails**

### **Nuclear Option - Complete Restoration**
1. Read `COMPLETE_DEBUGGING_RESTORATION_JOURNEY.md`
2. Follow the exact steps in Phase 3: Complete Restoration
3. Verify each component individually
4. Test build after each major change
5. Document any new issues found

### **Get Help**
- Check the complete journey document for detailed explanations
- Look for similar patterns in the codebase
- Test in isolation to identify the exact problem
- Always restore original functionality, don't leave debugging artifacts

---

## 🎉 **Expected Final State**

```
Application Status: ✅ PRODUCTION READY
Build Status: ✅ 82/82 pages successful  
Components: ✅ All functional with asChild
Navigation: ✅ Clean, no "Authenticated" text
Pages: ✅ Dashboard and Resume Builder with Suspense
Errors: ✅ None - all resolved
Ready for: ✅ Deployment to production
```

**Remember**: The goal is always to restore COMPLETE original functionality, not just make it work!
