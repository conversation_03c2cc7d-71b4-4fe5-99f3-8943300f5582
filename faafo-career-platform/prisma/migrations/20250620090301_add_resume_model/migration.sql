/*
  Warnings:

  - Added the required column `updatedAt` to the `Profile` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ExperienceLevel" AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT');

-- C<PERSON><PERSON>num
CREATE TYPE "ProfileVisibility" AS ENUM ('PRIVATE', 'PUBLIC', 'COMMUNITY_ONLY');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "InterviewSessionType" AS ENUM ('QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION');

-- Create<PERSON>num
CREATE TYPE "InterviewType" AS ENUM ('PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHA<PERSON>OR<PERSON>', 'CASE_STUDY');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "InterviewSessionStatus" AS ENUM ('NOT_STARTED', 'IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED');

-- CreateEnum
CREATE TYPE "InterviewQuestionType" AS ENUM ('BEHAVIORAL', 'TECHNICAL', 'SITUATIONAL', 'COMPANY_CULTURE', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'STRESS_TEST', 'CASE_STUDY', 'ROLE_SPECIFIC');

-- CreateEnum
CREATE TYPE "InterviewCategory" AS ENUM ('GENERAL', 'TECHNICAL_SKILLS', 'SOFT_SKILLS', 'LEADERSHIP', 'PROBLEM_SOLVING', 'COMMUNICATION', 'TEAMWORK', 'ADAPTABILITY', 'CREATIVITY', 'ANALYTICAL_THINKING', 'CUSTOMER_SERVICE', 'SALES', 'MANAGEMENT', 'STRATEGY', 'ETHICS', 'INDUSTRY_KNOWLEDGE');

-- DropForeignKey
ALTER TABLE "Profile" DROP CONSTRAINT "Profile_userId_fkey";

-- AlterTable
ALTER TABLE "Profile" ADD COLUMN     "careerInterests" JSONB,
ADD COLUMN     "company" TEXT,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "currentIndustry" TEXT,
ADD COLUMN     "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "experienceLevel" "ExperienceLevel",
ADD COLUMN     "firstName" TEXT,
ADD COLUMN     "jobTitle" TEXT,
ADD COLUMN     "lastName" TEXT,
ADD COLUMN     "lastProfileUpdate" TIMESTAMP(3),
ADD COLUMN     "location" TEXT,
ADD COLUMN     "phoneNumber" TEXT,
ADD COLUMN     "profileCompletionScore" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "profilePublic" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "profileVisibility" "ProfileVisibility" NOT NULL DEFAULT 'COMMUNITY_ONLY',
ADD COLUMN     "showEmail" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "showPhone" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "skillsToLearn" JSONB,
ADD COLUMN     "targetIndustry" TEXT,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "website" TEXT,
ADD COLUMN     "weeklyLearningGoal" INTEGER;

-- CreateTable
CREATE TABLE "CareerPathBookmark" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "careerPathId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "CareerPathBookmark_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SecurityToken" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SecurityToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RateLimitEntry" (
    "id" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RateLimitEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InterviewSession" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionType" "InterviewSessionType" NOT NULL,
    "careerPath" TEXT,
    "experienceLevel" "ExperienceLevel",
    "companyType" TEXT,
    "industryFocus" TEXT,
    "specificRole" TEXT,
    "interviewType" "InterviewType",
    "preparationTime" TEXT,
    "focusAreas" JSONB,
    "difficulty" "SkillLevel" NOT NULL DEFAULT 'BEGINNER',
    "status" "InterviewSessionStatus" NOT NULL DEFAULT 'IN_PROGRESS',
    "totalQuestions" INTEGER NOT NULL DEFAULT 0,
    "completedQuestions" INTEGER NOT NULL DEFAULT 0,
    "overallScore" DOUBLE PRECISION,
    "timeSpent" INTEGER NOT NULL DEFAULT 0,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "lastActiveAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "sessionConfig" JSONB,
    "aiInsights" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InterviewQuestion" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "questionText" TEXT NOT NULL,
    "questionType" "InterviewQuestionType" NOT NULL,
    "category" "InterviewCategory" NOT NULL,
    "difficulty" "SkillLevel" NOT NULL DEFAULT 'BEGINNER',
    "expectedDuration" INTEGER NOT NULL DEFAULT 180,
    "context" TEXT,
    "hints" JSONB,
    "followUpQuestions" JSONB,
    "industrySpecific" BOOLEAN NOT NULL DEFAULT false,
    "questionOrder" INTEGER NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,
    "tags" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InterviewResponse" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "questionId" TEXT NOT NULL,
    "responseText" TEXT,
    "audioUrl" TEXT,
    "videoUrl" TEXT,
    "responseTime" INTEGER NOT NULL DEFAULT 0,
    "preparationTime" INTEGER NOT NULL DEFAULT 0,
    "aiScore" DOUBLE PRECISION,
    "aiAnalysis" JSONB,
    "feedback" JSONB,
    "strengths" JSONB,
    "improvements" JSONB,
    "starMethodScore" DOUBLE PRECISION,
    "confidenceLevel" DOUBLE PRECISION,
    "communicationScore" DOUBLE PRECISION,
    "technicalScore" DOUBLE PRECISION,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "needsReview" BOOLEAN NOT NULL DEFAULT false,
    "userNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewResponse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "InterviewProgress" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "skillArea" "InterviewCategory" NOT NULL,
    "competencyLevel" "SkillLevel" NOT NULL DEFAULT 'BEGINNER',
    "totalSessions" INTEGER NOT NULL DEFAULT 0,
    "completedSessions" INTEGER NOT NULL DEFAULT 0,
    "averageScore" DOUBLE PRECISION,
    "bestScore" DOUBLE PRECISION,
    "lastSessionScore" DOUBLE PRECISION,
    "totalPracticeTime" INTEGER NOT NULL DEFAULT 0,
    "lastPracticed" TIMESTAMP(3),
    "improvementRate" DOUBLE PRECISION,
    "strengthAreas" JSONB,
    "improvementAreas" JSONB,
    "sessionHistory" JSONB,
    "milestones" JSONB,
    "nextRecommendations" JSONB,
    "streakCount" INTEGER NOT NULL DEFAULT 0,
    "longestStreak" INTEGER NOT NULL DEFAULT 0,
    "lastStreakDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewProgress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Resume" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "personalInfo" JSONB NOT NULL,
    "summary" TEXT,
    "experience" JSONB NOT NULL,
    "education" JSONB NOT NULL,
    "skills" JSONB NOT NULL,
    "sections" JSONB,
    "template" TEXT NOT NULL DEFAULT 'modern',
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastExported" TIMESTAMP(3),
    "exportCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Resume_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CareerPathBookmark_userId_idx" ON "CareerPathBookmark"("userId");

-- CreateIndex
CREATE INDEX "CareerPathBookmark_careerPathId_idx" ON "CareerPathBookmark"("careerPathId");

-- CreateIndex
CREATE UNIQUE INDEX "CareerPathBookmark_userId_careerPathId_key" ON "CareerPathBookmark"("userId", "careerPathId");

-- CreateIndex
CREATE INDEX "SecurityToken_expiresAt_idx" ON "SecurityToken"("expiresAt");

-- CreateIndex
CREATE UNIQUE INDEX "SecurityToken_identifier_type_key" ON "SecurityToken"("identifier", "type");

-- CreateIndex
CREATE INDEX "RateLimitEntry_identifier_createdAt_idx" ON "RateLimitEntry"("identifier", "createdAt");

-- CreateIndex
CREATE INDEX "InterviewSession_userId_status_idx" ON "InterviewSession"("userId", "status");

-- CreateIndex
CREATE INDEX "InterviewSession_userId_createdAt_idx" ON "InterviewSession"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "InterviewSession_status_createdAt_idx" ON "InterviewSession"("status", "createdAt");

-- CreateIndex
CREATE INDEX "InterviewSession_userId_status_createdAt_idx" ON "InterviewSession"("userId", "status", "createdAt");

-- CreateIndex
CREATE INDEX "InterviewQuestion_sessionId_questionOrder_idx" ON "InterviewQuestion"("sessionId", "questionOrder");

-- CreateIndex
CREATE UNIQUE INDEX "InterviewQuestion_sessionId_questionOrder_key" ON "InterviewQuestion"("sessionId", "questionOrder");

-- CreateIndex
CREATE INDEX "InterviewResponse_sessionId_isCompleted_idx" ON "InterviewResponse"("sessionId", "isCompleted");

-- CreateIndex
CREATE INDEX "InterviewResponse_userId_sessionId_idx" ON "InterviewResponse"("userId", "sessionId");

-- CreateIndex
CREATE INDEX "InterviewResponse_questionId_isCompleted_idx" ON "InterviewResponse"("questionId", "isCompleted");

-- CreateIndex
CREATE UNIQUE INDEX "InterviewResponse_userId_questionId_key" ON "InterviewResponse"("userId", "questionId");

-- CreateIndex
CREATE INDEX "InterviewProgress_userId_lastPracticed_idx" ON "InterviewProgress"("userId", "lastPracticed");

-- CreateIndex
CREATE UNIQUE INDEX "InterviewProgress_userId_skillArea_key" ON "InterviewProgress"("userId", "skillArea");

-- CreateIndex
CREATE INDEX "Resume_userId_idx" ON "Resume"("userId");

-- CreateIndex
CREATE INDEX "Resume_userId_isActive_idx" ON "Resume"("userId", "isActive");

-- CreateIndex
CREATE INDEX "Resume_createdAt_idx" ON "Resume"("createdAt");

-- CreateIndex
CREATE INDEX "LearningResource_isActive_idx" ON "LearningResource"("isActive");

-- CreateIndex
CREATE INDEX "LearningResource_category_isActive_idx" ON "LearningResource"("category", "isActive");

-- CreateIndex
CREATE INDEX "LearningResource_skillLevel_isActive_idx" ON "LearningResource"("skillLevel", "isActive");

-- CreateIndex
CREATE INDEX "LearningResource_type_isActive_idx" ON "LearningResource"("type", "isActive");

-- CreateIndex
CREATE INDEX "LearningResource_cost_isActive_idx" ON "LearningResource"("cost", "isActive");

-- CreateIndex
CREATE INDEX "LearningResource_category_skillLevel_isActive_idx" ON "LearningResource"("category", "skillLevel", "isActive");

-- CreateIndex
CREATE INDEX "LearningResource_title_idx" ON "LearningResource"("title");

-- CreateIndex
CREATE INDEX "LearningResource_createdAt_idx" ON "LearningResource"("createdAt");

-- CreateIndex
CREATE INDEX "ResourceRating_resourceId_idx" ON "ResourceRating"("resourceId");

-- CreateIndex
CREATE INDEX "ResourceRating_resourceId_rating_idx" ON "ResourceRating"("resourceId", "rating");

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CareerPathBookmark" ADD CONSTRAINT "CareerPathBookmark_careerPathId_fkey" FOREIGN KEY ("careerPathId") REFERENCES "CareerPath"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CareerPathBookmark" ADD CONSTRAINT "CareerPathBookmark_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InterviewSession" ADD CONSTRAINT "InterviewSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InterviewQuestion" ADD CONSTRAINT "InterviewQuestion_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "InterviewSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InterviewResponse" ADD CONSTRAINT "InterviewResponse_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "InterviewQuestion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InterviewResponse" ADD CONSTRAINT "InterviewResponse_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "InterviewSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InterviewResponse" ADD CONSTRAINT "InterviewResponse_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InterviewProgress" ADD CONSTRAINT "InterviewProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Resume" ADD CONSTRAINT "Resume_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
