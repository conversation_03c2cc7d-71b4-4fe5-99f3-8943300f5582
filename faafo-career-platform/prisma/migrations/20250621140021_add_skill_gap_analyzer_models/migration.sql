/*
  Warnings:

  - You are about to alter the column `title` on the `Resume` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(200)`.
  - You are about to alter the column `template` on the `Resume` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `name` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `email` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(254)`.
  - You are about to alter the column `image` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(500)`.
  - You are about to alter the column `password` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `passwordResetToken` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.

*/
-- <PERSON>reateEnum
CREATE TYPE "SkillAssessmentType" AS ENUM ('SELF_ASSESSMENT', 'PEER_VALIDATION', 'CERTIFICATION', 'PERFORMANCE_BASED', 'AI_EVALUATED');

-- CreateEnum
CREATE TYPE "SkillGapTimeframe" AS ENUM ('THREE_MONTHS', 'SIX_MONTHS', 'ONE_YEAR', 'TWO_YEARS', 'CUSTOM');

-- CreateEnum
CREATE TYPE "SkillGapAnalysisStatus" AS ENUM ('ACTIVE', 'COMPLETED', 'PAUSED', 'EXPIRED', 'ARCHIVED');

-- CreateEnum
CREATE TYPE "SkillDemandLevel" AS ENUM ('VERY_LOW', 'LOW', 'MODERATE', 'HIGH', 'VERY_HIGH', 'CRITICAL');

-- CreateEnum
CREATE TYPE "SkillGrowthTrend" AS ENUM ('DECLINING', 'STABLE', 'GROWING', 'RAPIDLY_GROWING', 'EMERGING');

-- CreateEnum
CREATE TYPE "RecommendationPriority" AS ENUM ('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'OPTIONAL');

-- AlterTable
ALTER TABLE "Resume" ALTER COLUMN "title" SET DATA TYPE VARCHAR(200),
ALTER COLUMN "template" SET DATA TYPE VARCHAR(50);

-- AlterTable
ALTER TABLE "User" ALTER COLUMN "name" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "email" SET DATA TYPE VARCHAR(254),
ALTER COLUMN "image" SET DATA TYPE VARCHAR(500),
ALTER COLUMN "password" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "passwordResetToken" SET DATA TYPE VARCHAR(255);

-- CreateTable
CREATE TABLE "SkillAssessment" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,
    "selfRating" INTEGER NOT NULL,
    "confidenceLevel" INTEGER NOT NULL,
    "assessmentType" "SkillAssessmentType" NOT NULL DEFAULT 'SELF_ASSESSMENT',
    "assessmentDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    "validatedBy" TEXT,
    "validatedAt" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillAssessment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SkillGapAnalysis" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "targetCareerPathId" TEXT,
    "targetCareerPathName" TEXT NOT NULL,
    "experienceLevel" "ExperienceLevel" NOT NULL,
    "timeframe" "SkillGapTimeframe" NOT NULL,
    "analysisData" JSONB NOT NULL,
    "skillGaps" JSONB NOT NULL,
    "learningPlan" JSONB NOT NULL,
    "marketData" JSONB,
    "progressTracking" JSONB,
    "status" "SkillGapAnalysisStatus" NOT NULL DEFAULT 'ACTIVE',
    "completionPercentage" INTEGER NOT NULL DEFAULT 0,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillGapAnalysis_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SkillMarketData" (
    "id" TEXT NOT NULL,
    "skillId" TEXT NOT NULL,
    "region" TEXT NOT NULL DEFAULT 'GLOBAL',
    "demandLevel" "SkillDemandLevel" NOT NULL,
    "averageSalaryImpact" DOUBLE PRECISION,
    "jobPostingsCount" INTEGER NOT NULL DEFAULT 0,
    "growthTrend" "SkillGrowthTrend" NOT NULL,
    "industryRelevance" JSONB NOT NULL,
    "dataSource" TEXT NOT NULL,
    "dataDate" TIMESTAMP(3) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillMarketData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LearningPathRecommendation" (
    "id" TEXT NOT NULL,
    "skillGapAnalysisId" TEXT NOT NULL,
    "learningPathId" TEXT NOT NULL,
    "priority" "RecommendationPriority" NOT NULL,
    "estimatedHours" INTEGER NOT NULL,
    "skillsAddressed" JSONB NOT NULL,
    "completionOrder" INTEGER NOT NULL,
    "prerequisites" JSONB,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "startedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LearningPathRecommendation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SkillAssessment_userId_assessmentDate_idx" ON "SkillAssessment"("userId", "assessmentDate");

-- CreateIndex
CREATE INDEX "SkillAssessment_skillId_assessmentType_idx" ON "SkillAssessment"("skillId", "assessmentType");

-- CreateIndex
CREATE UNIQUE INDEX "SkillAssessment_userId_skillId_assessmentType_key" ON "SkillAssessment"("userId", "skillId", "assessmentType");

-- CreateIndex
CREATE INDEX "SkillGapAnalysis_userId_status_idx" ON "SkillGapAnalysis"("userId", "status");

-- CreateIndex
CREATE INDEX "SkillGapAnalysis_userId_createdAt_idx" ON "SkillGapAnalysis"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "SkillGapAnalysis_targetCareerPathId_idx" ON "SkillGapAnalysis"("targetCareerPathId");

-- CreateIndex
CREATE INDEX "SkillMarketData_skillId_demandLevel_idx" ON "SkillMarketData"("skillId", "demandLevel");

-- CreateIndex
CREATE INDEX "SkillMarketData_dataDate_isActive_idx" ON "SkillMarketData"("dataDate", "isActive");

-- CreateIndex
CREATE UNIQUE INDEX "SkillMarketData_skillId_region_dataDate_key" ON "SkillMarketData"("skillId", "region", "dataDate");

-- CreateIndex
CREATE INDEX "LearningPathRecommendation_skillGapAnalysisId_priority_idx" ON "LearningPathRecommendation"("skillGapAnalysisId", "priority");

-- CreateIndex
CREATE UNIQUE INDEX "LearningPathRecommendation_skillGapAnalysisId_learningPathI_key" ON "LearningPathRecommendation"("skillGapAnalysisId", "learningPathId");

-- CreateIndex
CREATE INDEX "Skill_category_name_idx" ON "Skill"("category", "name");

-- CreateIndex
CREATE INDEX "Skill_name_description_idx" ON "Skill"("name", "description");

-- AddForeignKey
ALTER TABLE "SkillAssessment" ADD CONSTRAINT "SkillAssessment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillAssessment" ADD CONSTRAINT "SkillAssessment_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillGapAnalysis" ADD CONSTRAINT "SkillGapAnalysis_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillGapAnalysis" ADD CONSTRAINT "SkillGapAnalysis_targetCareerPathId_fkey" FOREIGN KEY ("targetCareerPathId") REFERENCES "CareerPath"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillMarketData" ADD CONSTRAINT "SkillMarketData_skillId_fkey" FOREIGN KEY ("skillId") REFERENCES "Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LearningPathRecommendation" ADD CONSTRAINT "LearningPathRecommendation_skillGapAnalysisId_fkey" FOREIGN KEY ("skillGapAnalysisId") REFERENCES "SkillGapAnalysis"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LearningPathRecommendation" ADD CONSTRAINT "LearningPathRecommendation_learningPathId_fkey" FOREIGN KEY ("learningPathId") REFERENCES "LearningPath"("id") ON DELETE CASCADE ON UPDATE CASCADE;
