import prisma from '../src/lib/prisma';

async function seedAdditionalPaths() {
  console.log(`Starting additional career paths seeding...`);

  // Product Manager - Strategy and leadership focus
  const productManagerPath = await prisma.careerPath.upsert({
    where: { slug: 'product-manager' },
    update: {},
    create: {
      name: 'Product Manager',
      slug: 'product-manager',
      overview: 'Guide product development from conception to launch by defining strategy, prioritizing features, and coordinating cross-functional teams. Critical role in $6.8 trillion software industry, driving innovation at companies like Meta, Google, and emerging AI startups. Product-led growth strategies dominate SaaS, fintech, and consumer apps. Many positions offer remote work flexibility. Typically requires bachelor\'s degree in Business, Engineering, Computer Science, or MBA for senior roles.',
      pros: JSON.stringify([
        'High demand with 19% projected growth through 2032',
        'Median salary $142,000 (Glassdoor 2025): Entry $90,000, Senior $180,000+',
        'Strategic role with significant business impact',
        'Leadership opportunities without direct management',
        'Diverse career paths in tech, finance, healthcare, and more',
        'Product strategy, user research, and roadmap planning skills transfer between tech, finance, healthcare',
      ]),
      cons: JSON.stringify([
        'High responsibility with accountability for product success/failure',
        'Requires balancing competing stakeholder interests',
        'Can be stressful managing tight deadlines and changing priorities',
        'Need strong technical understanding without being hands-on',
        'Difficult to measure individual contribution to team success',
        'May face resistance when making difficult prioritization decisions',
      ]),
      actionableSteps: [
        'Complete "Business Fundamentals" on Coursera and read "Lean Analytics" by Alistair Croll (6-8 weeks)',
        'Complete "Agile Product Management" on Udemy and read "Inspired" by Marty Cagan (4-6 weeks)',
        'Get certified in Jira Administration, complete Figma Academy courses, and master Google Analytics 4 certification',
        'Complete "Technical Product Management" on Udemy and learn basic SQL, API concepts, and mobile development fundamentals',
        'Complete "User Research Methods" on Interaction Design Foundation and practice with Hotjar, Mixpanel, or Amplitude',
        'Enroll in Product School\'s Product Management Certificate (8 weeks) or General Assembly\'s PM Circuit (10 weeks)',
        'Apply for Associate PM roles at tech companies or transition internally by proposing product initiatives in current role',
        'Join Product Hunt community, attend ProductCon or Mind the Product conferences ($200-500), and engage on Product Twitter',
      ],
      isActive: true,
    },
  });

  // Cloud Solutions Architect - Infrastructure and scalability focus
  const cloudArchitectPath = await prisma.careerPath.upsert({
    where: { slug: 'cloud-solutions-architect' },
    update: {},
    create: {
      name: 'Cloud Solutions Architect',
      slug: 'cloud-solutions-architect',
      overview: 'Design and implement scalable cloud infrastructure solutions, helping organizations migrate to and optimize their cloud environments. Critical role in $545 billion cloud computing market - 94% of enterprises use cloud services, with AWS, Microsoft Azure, and Google Cloud driving digital transformation across industries from banking to healthcare. High remote work availability due to cloud-native nature. Typically requires bachelor\'s degree in Computer Science, Information Technology, or equivalent experience with cloud certifications.',
      pros: JSON.stringify([
        'Extremely high demand with 25% projected growth through 2032',
        'Median salary $130,890 (Glassdoor 2025): Entry $110,000, Senior $200,000+',
        'Work with cutting-edge cloud technologies (AWS, Azure, GCP)',
        'High remote work availability (85% of positions)',
        'Strategic role with significant business impact on scalability',
        'Strong job security due to cloud adoption trends',
      ]),
      cons: JSON.stringify([
        'Requires deep technical knowledge across multiple domains',
        'High pressure to design fault-tolerant, secure systems',
        'Must master new AWS, Azure, GCP services released monthly and evolving container orchestration platforms',
        'On-call responsibilities for critical infrastructure issues',
        'Complex troubleshooting across distributed systems',
        'Need to balance cost optimization with performance requirements',
      ]),
      actionableSteps: [
        'Master one major cloud platform: start with AWS, Azure, or GCP fundamentals',
        'Complete HashiCorp Terraform Associate certification and AWS CloudFormation course on A Cloud Guru (6-8 weeks)',
        'Complete AWS Networking Specialty certification and "Cloud Security Fundamentals" on Pluralsight (8-10 weeks)',
        'Pursue cloud certifications: AWS Solutions Architect, Azure Architect, GCP Professional',
        'Complete AWS Well-Architected Labs, build 3-tier web application on cloud, and document architecture decisions on GitHub',
        'Complete Docker Certified Associate and Certified Kubernetes Administrator (CKA) certifications (10-12 weeks)',
        'Complete "DevOps Engineering on AWS" specialization on Coursera and Jenkins certification (8-10 weeks)',
        'Create portfolio website showcasing 3-5 cloud projects with architecture diagrams, cost analysis, and performance metrics',
      ],
      isActive: true,
    },
  });

  // DevOps Engineer - Automation and deployment focus
  const devopsEngineerPath = await prisma.careerPath.upsert({
    where: { slug: 'devops-engineer' },
    update: {},
    create: {
      name: 'DevOps Engineer',
      slug: 'devops-engineer',
      overview: 'Bridge development and operations by automating deployment pipelines, managing infrastructure, and ensuring reliable software delivery. Essential for companies scaling from startup to enterprise - Netflix deploys 1000+ times daily, Spotify manages 4000+ microservices. DevOps adoption accelerated by cloud migration, containerization, and microservices architecture. Remote work opportunities widely available. Typically requires bachelor\'s degree in Computer Science, Software Engineering, or equivalent experience with strong programming background.',
      pros: JSON.stringify([
        'High demand with 22% projected growth through 2032',
        'Median salary $115,737 (Glassdoor 2025): Entry $85,000, Senior $160,000+',
        'Work with modern automation tools and technologies',
        'High impact on development team productivity',
        'Remote work opportunities widely available (70% of positions)',
        'Exposure to cutting-edge tools: Kubernetes, Terraform, Jenkins, Docker, monitoring platforms',
      ]),
      cons: JSON.stringify([
        'On-call responsibilities for production systems',
        'High pressure during deployment and incident response',
        'Need to understand both development and operations deeply',
        'Rapidly changing toolchain requires constant upskilling',
        'Can be stressful managing critical infrastructure',
        'May face blame when deployments or systems fail',
      ]),
      actionableSteps: [
        'Complete Linux Professional Institute LPIC-1 certification and "Linux Command Line Basics" on Udacity (6-8 weeks)',
        'Complete "Git and GitHub Masterclass" on Udemy and practice GitFlow, feature branching, and pull request workflows',
        'Complete "Jenkins Essential Training" on LinkedIn Learning and GitHub Actions certification (4-6 weeks)',
        'Complete Red Hat Certified Specialist in Ansible Automation and Puppet Certified Professional (8-10 weeks)',
        'Build microservices application with Docker Compose, deploy to Kubernetes cluster, and implement rolling updates',
        'Complete AWS Solutions Architect Associate and Terraform Associate certifications (10-12 weeks)',
        'Complete "Monitoring and Observability" course on Pluralsight and Elastic Certified Engineer certification (6-8 weeks)',
        'Create GitHub portfolio with CI/CD pipelines, Infrastructure as Code examples, and monitoring dashboards with documentation',
      ],
      isActive: true,
    },
  });

  console.log(`Created career path: ${productManagerPath.name} (ID: ${productManagerPath.id})`);
  console.log(`Created career path: ${cloudArchitectPath.name} (ID: ${cloudArchitectPath.id})`);
  console.log(`Created career path: ${devopsEngineerPath.name} (ID: ${devopsEngineerPath.id})`);

  // Create sophisticated suggestion rules for new paths

  // Product Manager Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: productManagerPath.id,
      questionKey: 'top_skills',
      answerValue: 'project_management',
      weight: 8.0,
      notes: 'Project management skills translate well to product management.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: productManagerPath.id,
      questionKey: 'top_skills',
      answerValue: 'leadership',
      weight: 8.0,
      notes: 'Leadership skills are essential for coordinating cross-functional teams.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: productManagerPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'product_management',
      weight: 9.0,
      notes: 'Direct interest in product management indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: productManagerPath.id,
      questionKey: 'core_values',
      answerValue: 'autonomy',
      weight: 6.0,
      notes: 'Product managers need autonomy to make strategic decisions.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: productManagerPath.id,
      questionKey: 'desired_outcomes_compensation',
      answerValue: 'significant_increase',
      weight: 7.0,
      notes: 'Product management offers strong compensation growth potential.',
    },
  });

  // Cloud Solutions Architect Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: cloudArchitectPath.id,
      questionKey: 'top_skills',
      answerValue: 'technical_programming',
      weight: 7.0,
      notes: 'Technical skills are essential for cloud architecture.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: cloudArchitectPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'cloud_computing',
      weight: 9.0,
      notes: 'Direct interest in cloud computing indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: cloudArchitectPath.id,
      questionKey: 'top_skills',
      answerValue: 'problem_solving',
      weight: 7.0,
      notes: 'Problem-solving skills crucial for designing scalable architectures.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: cloudArchitectPath.id,
      questionKey: 'desired_outcomes_compensation',
      answerValue: 'significant_increase',
      weight: 8.0,
      notes: 'Cloud architecture offers excellent compensation potential.',
    },
  });

  // DevOps Engineer Rules
  await prisma.suggestionRule.create({
    data: {
      careerPathId: devopsEngineerPath.id,
      questionKey: 'top_skills',
      answerValue: 'technical_programming',
      weight: 7.0,
      notes: 'Programming skills valuable for automation and scripting.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: devopsEngineerPath.id,
      questionKey: 'skill_development_interest',
      answerValue: 'devops',
      weight: 9.0,
      notes: 'Direct interest in DevOps indicates strong career fit.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: devopsEngineerPath.id,
      questionKey: 'top_skills',
      answerValue: 'problem_solving',
      weight: 6.0,
      notes: 'Problem-solving essential for troubleshooting complex systems.',
    },
  });

  await prisma.suggestionRule.create({
    data: {
      careerPathId: devopsEngineerPath.id,
      questionKey: 'core_values',
      answerValue: 'continuous_learning',
      weight: 7.0,
      notes: 'DevOps field requires constant learning of new tools and practices.',
    },
  });

  console.log(`Additional career paths seeding completed.`);
}

export default seedAdditionalPaths;
