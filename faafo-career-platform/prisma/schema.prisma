generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String                     @id @default(cuid())
  name                 String?                    @db.Var<PERSON>har(100)
  email                String                     @unique @db.Var<PERSON>har(254)
  emailVerified        DateTime?
  image                String?                    @db.Var<PERSON>har(500)
  password             String                     @db.VarChar(255)
  passwordResetToken   String?                    @unique @db.VarChar(255)
  passwordResetExpires DateTime?
  failedLoginAttempts  Int                        @default(0)
  lockedUntil          DateTime?
  createdAt            DateTime                   @default(now())
  updatedAt            DateTime                   @updatedAt
  accounts             Account[]
  assessments          Assessment[]
  careerPathBookmarks  CareerPathBookmark[]
  forumBookmarks       ForumBookmark[]
  moderatorRoles       ForumModerator[]
  forumPosts           ForumPost[]
  forumPostReactions   ForumPostReaction[]
  forumReplies         ForumReply[]
  forumReplyReactions  ForumReplyReaction[]
  forumReports         ForumReport[]
  freedomFund          FreedomFund?
  interviewProgress    InterviewProgress[]
  interviewResponses   InterviewResponse[]
  interviewSessions    InterviewSession[]
  learningAnalytics    LearningAnalytics[]
  profile              Profile?
  resourceRatings      ResourceRating[]
  sessions             Session[]
  achievements         UserAchievement[]
  goals                UserGoal[]
  learningPaths        UserLearningPath[]
  learningPathProgress UserLearningPathProgress[]
  learningProgress     UserLearningProgress[]
  skillProgress        UserSkillProgress[]
  resumes              Resume[]
  skillAssessments     SkillAssessment[]
  skillGapAnalyses     SkillGapAnalysis[]
}

model Profile {
  id                     String            @id @default(uuid())
  userId                 String            @unique
  bio                    String?
  profilePictureUrl      String?
  socialMediaLinks       Json?
  firstName              String?
  lastName               String?
  phoneNumber            String?
  location               String?
  website                String?
  jobTitle               String?
  company                String?
  currentIndustry        String?
  targetIndustry         String?
  experienceLevel        ExperienceLevel?
  careerInterests        Json?
  skillsToLearn          Json?
  weeklyLearningGoal     Int?
  profileVisibility      ProfileVisibility @default(COMMUNITY_ONLY)
  emailNotifications     Boolean           @default(true)
  profilePublic          Boolean           @default(false)
  showEmail              Boolean           @default(false)
  showPhone              Boolean           @default(false)
  profileCompletionScore Int               @default(0)
  lastProfileUpdate      DateTime?
  forumSignature         String?
  forumBio               String?
  forumReputation        Int               @default(0)
  forumPostCount         Int               @default(0)
  forumReplyCount        Int               @default(0)
  joinedAt               DateTime          @default(now())
  lastActiveAt           DateTime          @default(now())
  currentCareerPath      String?
  progressLevel          String?
  achievements           Json?
  createdAt              DateTime          @default(now())
  updatedAt              DateTime          @updatedAt
  user                   User              @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Assessment {
  id          String               @id @default(uuid())
  userId      String
  status      AssessmentStatus     @default(IN_PROGRESS)
  currentStep Int                  @default(0)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  completedAt DateTime?
  user        User                 @relation(fields: [userId], references: [id])
  responses   AssessmentResponse[]
}

model AssessmentResponse {
  id           String     @id @default(uuid())
  assessmentId String
  questionKey  String
  answerValue  Json
  createdAt    DateTime   @default(now())
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@index([assessmentId])
}

model CareerPath {
  id                String               @id @default(uuid())
  name              String               @unique
  slug              String               @unique
  overview          String
  pros              String
  cons              String
  actionableSteps   Json
  isActive          Boolean              @default(true)
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  bookmarks         CareerPathBookmark[]
  suggestionRules   SuggestionRule[]
  relatedIndustries Industry[]           @relation("CareerPathToIndustry")
  learningPaths     LearningPath[]       @relation("CareerPathToLearningPath")
  learningResources LearningResource[]   @relation("CareerPathToLearningResource")
  relatedSkills     Skill[]              @relation("CareerPathToSkill")
  skillGapAnalyses  SkillGapAnalysis[]
}

model Skill {
  id                String              @id @default(uuid())
  name              String              @unique
  description       String?
  category          String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  userProgress      UserSkillProgress[]
  careerPaths       CareerPath[]        @relation("CareerPathToSkill")
  learningPaths     LearningPath[]      @relation("LearningPathToSkill")
  learningResources LearningResource[]  @relation("SkillToLearningResource")
  assessments       SkillAssessment[]
  marketData        SkillMarketData[]

  @@index([category, name])
  @@index([name, description])
}

model Industry {
  id          String       @id @default(uuid())
  name        String       @unique
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  careerPaths CareerPath[] @relation("CareerPathToIndustry")
}

model SuggestionRule {
  id           String     @id @default(uuid())
  careerPathId String
  questionKey  String
  answerValue  Json
  weight       Float      @default(1.0)
  notes        String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id])

  @@index([careerPathId])
}

model ForumPost {
  id               String              @id @default(uuid())
  title            String
  content          String
  authorId         String
  categoryId       String?
  tags             Json?
  viewCount        Int                 @default(0)
  likeCount        Int                 @default(0)
  replyCount       Int                 @default(0)
  isLocked         Boolean             @default(false)
  isHidden         Boolean             @default(false)
  isPinned         Boolean             @default(false)
  moderatedBy      String?
  moderatedAt      DateTime?
  moderationReason String?
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  bookmarks        ForumBookmark[]
  author           User                @relation(fields: [authorId], references: [id])
  category         ForumCategory?      @relation(fields: [categoryId], references: [id])
  reactions        ForumPostReaction[]
  replies          ForumReply[]
  reports          ForumReport[]
}

model ForumReply {
  id               String               @id @default(uuid())
  content          String
  authorId         String
  postId           String
  likeCount        Int                  @default(0)
  isHidden         Boolean              @default(false)
  moderatedBy      String?
  moderatedAt      DateTime?
  moderationReason String?
  createdAt        DateTime             @default(now())
  updatedAt        DateTime             @updatedAt
  author           User                 @relation(fields: [authorId], references: [id])
  post             ForumPost            @relation(fields: [postId], references: [id])
  reactions        ForumReplyReaction[]
  reports          ForumReport[]
}

model FreedomFund {
  id                   String   @id @default(uuid())
  userId               String   @unique
  monthlyExpenses      Float
  coverageMonths       Int
  targetSavings        Float
  currentSavingsAmount Float?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  user                 User     @relation(fields: [userId], references: [id])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model LearningResource {
  id                String                   @id @default(uuid())
  title             String
  description       String
  url               String                   @unique
  type              LearningResourceType
  category          LearningResourceCategory
  skillLevel        SkillLevel
  author            String?
  duration          String?
  cost              LearningResourceCost     @default(FREE)
  format            LearningResourceFormat
  isActive          Boolean                  @default(true)
  createdAt         DateTime                 @default(now())
  updatedAt         DateTime                 @updatedAt
  learningPathSteps LearningPathStep[]
  ratings           ResourceRating[]
  userProgress      UserLearningProgress[]
  careerPaths       CareerPath[]             @relation("CareerPathToLearningResource")
  skills            Skill[]                  @relation("SkillToLearningResource")

  @@index([isActive])
  @@index([category, isActive])
  @@index([skillLevel, isActive])
  @@index([type, isActive])
  @@index([cost, isActive])
  @@index([category, skillLevel, isActive])
  @@index([title])
  @@index([createdAt])
}

model UserLearningProgress {
  id          String           @id @default(uuid())
  userId      String
  resourceId  String
  status      ProgressStatus   @default(NOT_STARTED)
  completedAt DateTime?
  notes       String?
  rating      Int?
  review      String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  resource    LearningResource @relation(fields: [resourceId], references: [id])
  user        User             @relation(fields: [userId], references: [id])

  @@unique([userId, resourceId])
}

model ResourceRating {
  id         String           @id @default(uuid())
  userId     String
  resourceId String
  rating     Int
  review     String?
  isHelpful  Boolean?
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  resource   LearningResource @relation(fields: [resourceId], references: [id])
  user       User             @relation(fields: [userId], references: [id])

  @@unique([userId, resourceId])
  @@index([resourceId])
  @@index([resourceId, rating])
}

model ForumCategory {
  id          String           @id @default(uuid())
  name        String           @unique
  slug        String           @unique
  description String?
  guidelines  String?
  parentId    String?
  icon        String?
  color       String?
  sortOrder   Int              @default(0)
  isActive    Boolean          @default(true)
  postCount   Int              @default(0)
  replyCount  Int              @default(0)
  lastPostAt  DateTime?
  lastPostBy  String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  parent      ForumCategory?   @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    ForumCategory[]  @relation("CategoryHierarchy")
  moderators  ForumModerator[]
  posts       ForumPost[]
}

model ForumPostReaction {
  id        String       @id @default(uuid())
  userId    String
  postId    String
  type      ReactionType
  createdAt DateTime     @default(now())
  post      ForumPost    @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User         @relation(fields: [userId], references: [id])

  @@unique([userId, postId])
}

model ForumReplyReaction {
  id        String       @id @default(uuid())
  userId    String
  replyId   String
  type      ReactionType
  createdAt DateTime     @default(now())
  reply     ForumReply   @relation(fields: [replyId], references: [id], onDelete: Cascade)
  user      User         @relation(fields: [userId], references: [id])

  @@unique([userId, replyId])
}

model ForumBookmark {
  id        String    @id @default(uuid())
  userId    String
  postId    String
  createdAt DateTime  @default(now())
  post      ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id])

  @@unique([userId, postId])
}

model CareerPathBookmark {
  id           String     @id @default(uuid())
  userId       String
  careerPathId String
  createdAt    DateTime   @default(now())
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id])

  @@unique([userId, careerPathId])
  @@index([userId])
  @@index([careerPathId])
}

model ForumModerator {
  id          String         @id @default(uuid())
  userId      String
  categoryId  String?
  role        ModeratorRole
  permissions Json
  assignedBy  String
  assignedAt  DateTime       @default(now())
  isActive    Boolean        @default(true)
  category    ForumCategory? @relation(fields: [categoryId], references: [id])
  user        User           @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([categoryId])
}

model ForumReport {
  id          String       @id @default(uuid())
  reporterId  String
  postId      String?
  replyId     String?
  reason      ReportReason
  description String?
  status      ReportStatus @default(PENDING)
  reviewedBy  String?
  reviewedAt  DateTime?
  resolution  String?
  createdAt   DateTime     @default(now())
  post        ForumPost?   @relation(fields: [postId], references: [id])
  reply       ForumReply?  @relation(fields: [replyId], references: [id])
  reporter    User         @relation(fields: [reporterId], references: [id])

  @@index([status])
  @@index([createdAt])
}

model UserGoal {
  id           String       @id @default(uuid())
  userId       String
  title        String
  description  String?
  type         GoalType
  category     GoalCategory
  status       GoalStatus   @default(ACTIVE)
  targetValue  Int
  currentValue Int          @default(0)
  targetDate   DateTime?
  isPublic     Boolean      @default(false)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

model Achievement {
  id               String            @id @default(uuid())
  title            String            @unique
  description      String
  icon             String
  type             AchievementType
  criteria         Json
  points           Int               @default(0)
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  userAchievements UserAchievement[]
}

model UserAchievement {
  id            String      @id @default(uuid())
  userId        String
  achievementId String
  unlockedAt    DateTime    @default(now())
  progress      Json?
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@index([userId])
}

model LearningPath {
  id              String                       @id @default(uuid())
  title           String
  description     String
  slug            String                       @unique
  difficulty      SkillLevel
  estimatedHours  Int
  prerequisites   Json?
  isActive        Boolean                      @default(true)
  createdBy       String?
  tags            Json?
  imageUrl        String?
  category        LearningResourceCategory
  createdAt       DateTime                     @default(now())
  updatedAt       DateTime                     @updatedAt
  steps           LearningPathStep[]
  userPaths       UserLearningPath[]
  careerPaths     CareerPath[]                 @relation("CareerPathToLearningPath")
  skills          Skill[]                      @relation("LearningPathToSkill")
  recommendations LearningPathRecommendation[]
}

model LearningPathStep {
  id               String                     @id @default(uuid())
  learningPathId   String
  title            String
  description      String
  stepOrder        Int
  stepType         LearningStepType
  estimatedMinutes Int
  resourceId       String?
  externalUrl      String?
  content          Json?
  isRequired       Boolean                    @default(true)
  prerequisites    Json?
  createdAt        DateTime                   @default(now())
  updatedAt        DateTime                   @updatedAt
  learningPath     LearningPath               @relation(fields: [learningPathId], references: [id], onDelete: Cascade)
  resource         LearningResource?          @relation(fields: [resourceId], references: [id])
  userProgress     UserLearningPathProgress[]

  @@index([learningPathId, stepOrder])
}

model UserLearningPath {
  id              String                     @id @default(uuid())
  userId          String
  learningPathId  String
  status          LearningPathStatus         @default(NOT_STARTED)
  startedAt       DateTime?
  completedAt     DateTime?
  lastAccessedAt  DateTime                   @default(now())
  currentStepId   String?
  completedSteps  Int                        @default(0)
  totalSteps      Int                        @default(0)
  progressPercent Int                        @default(0)
  totalTimeSpent  Int                        @default(0)
  notes           String?
  rating          Int?
  review          String?
  createdAt       DateTime                   @default(now())
  updatedAt       DateTime                   @updatedAt
  learningPath    LearningPath               @relation(fields: [learningPathId], references: [id], onDelete: Cascade)
  user            User                       @relation(fields: [userId], references: [id], onDelete: Cascade)
  stepProgress    UserLearningPathProgress[]

  @@unique([userId, learningPathId])
  @@index([userId, status])
}

model UserLearningPathProgress {
  id                 String           @id @default(uuid())
  userId             String
  userLearningPathId String
  stepId             String
  status             ProgressStatus   @default(NOT_STARTED)
  startedAt          DateTime?
  completedAt        DateTime?
  timeSpent          Int              @default(0)
  score              Int?
  attempts           Int              @default(0)
  notes              String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  step               LearningPathStep @relation(fields: [stepId], references: [id], onDelete: Cascade)
  user               User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  userLearningPath   UserLearningPath @relation(fields: [userLearningPathId], references: [id], onDelete: Cascade)

  @@unique([userId, stepId])
  @@index([userId, status])
}

model UserSkillProgress {
  id                 String     @id @default(uuid())
  userId             String
  skillId            String
  currentLevel       SkillLevel @default(BEGINNER)
  progressPoints     Int        @default(0)
  lastPracticed      DateTime?
  completedResources Int        @default(0)
  completedPaths     Int        @default(0)
  practiceHours      Int        @default(0)
  selfAssessment     Int?
  peerValidations    Int        @default(0)
  certifications     Json?
  createdAt          DateTime   @default(now())
  updatedAt          DateTime   @updatedAt
  skill              Skill      @relation(fields: [skillId], references: [id], onDelete: Cascade)
  user               User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, skillId])
  @@index([userId, currentLevel])
}

model LearningAnalytics {
  id                String   @id @default(uuid())
  userId            String
  date              DateTime @default(now())
  timeSpent         Int      @default(0)
  resourcesViewed   Int      @default(0)
  pathsProgressed   Int      @default(0)
  skillsImproved    Int      @default(0)
  loginStreak       Int      @default(0)
  weeklyGoalMet     Boolean  @default(false)
  monthlyGoalMet    Boolean  @default(false)
  avgCompletionTime Float?
  learningVelocity  Float?
  deviceType        String?
  sessionDuration   Int?
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([userId, date])
}

model SecurityToken {
  id         String   @id @default(uuid())
  identifier String
  type       String
  token      String
  expiresAt  DateTime
  metadata   Json?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([identifier, type])
  @@index([expiresAt])
}

model RateLimitEntry {
  id         String   @id @default(uuid())
  identifier String
  metadata   Json?
  createdAt  DateTime @default(now())

  @@index([identifier, createdAt])
}

model InterviewSession {
  id                 String                 @id @default(uuid())
  userId             String
  sessionType        InterviewSessionType
  careerPath         String?
  experienceLevel    ExperienceLevel?
  companyType        String?
  industryFocus      String?
  specificRole       String?
  interviewType      InterviewType?
  preparationTime    String?
  focusAreas         Json?
  difficulty         SkillLevel             @default(BEGINNER)
  status             InterviewSessionStatus @default(IN_PROGRESS)
  totalQuestions     Int                    @default(0)
  completedQuestions Int                    @default(0)
  overallScore       Float?
  timeSpent          Int                    @default(0)
  startedAt          DateTime               @default(now())
  completedAt        DateTime?
  lastActiveAt       DateTime               @default(now())
  sessionConfig      Json?
  aiInsights         Json?
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  questions          InterviewQuestion[]
  responses          InterviewResponse[]
  user               User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, status])
  @@index([userId, createdAt])
  @@index([status, createdAt])
  @@index([userId, status, createdAt])
}

model InterviewQuestion {
  id                String                @id @default(uuid())
  sessionId         String
  questionText      String
  questionType      InterviewQuestionType
  category          InterviewCategory
  difficulty        SkillLevel            @default(BEGINNER)
  expectedDuration  Int                   @default(180)
  context           String?
  hints             Json?
  followUpQuestions Json?
  industrySpecific  Boolean               @default(false)
  questionOrder     Int
  isRequired        Boolean               @default(true)
  tags              Json?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  session           InterviewSession      @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  responses         InterviewResponse[]

  @@unique([sessionId, questionOrder])
  @@index([sessionId, questionOrder])
}

model InterviewResponse {
  id                 String            @id @default(uuid())
  userId             String
  sessionId          String
  questionId         String
  responseText       String?
  audioUrl           String?
  videoUrl           String?
  responseTime       Int               @default(0)
  preparationTime    Int               @default(0)
  aiScore            Float?
  aiAnalysis         Json?
  feedback           Json?
  strengths          Json?
  improvements       Json?
  starMethodScore    Float?
  confidenceLevel    Float?
  communicationScore Float?
  technicalScore     Float?
  isCompleted        Boolean           @default(false)
  needsReview        Boolean           @default(false)
  userNotes          String?
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  question           InterviewQuestion @relation(fields: [questionId], references: [id], onDelete: Cascade)
  session            InterviewSession  @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user               User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, questionId])
  @@index([sessionId, isCompleted])
  @@index([userId, sessionId])
  @@index([questionId, isCompleted])
}

model InterviewProgress {
  id                  String            @id @default(uuid())
  userId              String
  skillArea           InterviewCategory
  competencyLevel     SkillLevel        @default(BEGINNER)
  totalSessions       Int               @default(0)
  completedSessions   Int               @default(0)
  averageScore        Float?
  bestScore           Float?
  lastSessionScore    Float?
  totalPracticeTime   Int               @default(0)
  lastPracticed       DateTime?
  improvementRate     Float?
  strengthAreas       Json?
  improvementAreas    Json?
  sessionHistory      Json?
  milestones          Json?
  nextRecommendations Json?
  streakCount         Int               @default(0)
  longestStreak       Int               @default(0)
  lastStreakDate      DateTime?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  user                User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, skillArea])
  @@index([userId, lastPracticed])
}

model Resume {
  id           String    @id @default(uuid())
  userId       String
  title        String    @db.VarChar(200)
  personalInfo Json // { firstName, lastName, email, phone, location, website, linkedIn }
  summary      String?   @db.Text
  experience   Json // Array of { company, position, startDate, endDate, description, achievements }
  education    Json // Array of { institution, degree, field, startDate, endDate, gpa, honors }
  skills       Json // Array of { name, level, category }
  sections     Json? // Additional custom sections
  template     String    @default("modern") @db.VarChar(50) // Template identifier
  isPublic     Boolean   @default(false)
  isActive     Boolean   @default(true)
  lastExported DateTime?
  exportCount  Int       @default(0)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([userId, isActive])
  @@index([createdAt])
}

model SkillAssessment {
  id              String              @id @default(uuid())
  userId          String
  skillId         String
  selfRating      Int // 1-10 scale
  confidenceLevel Int // 1-10 scale
  assessmentType  SkillAssessmentType @default(SELF_ASSESSMENT)
  assessmentDate  DateTime            @default(now())
  notes           String?
  validatedBy     String? // For peer validations
  validatedAt     DateTime?
  isActive        Boolean             @default(true)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  skill Skill @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@unique([userId, skillId, assessmentType])
  @@index([userId, assessmentDate])
  @@index([skillId, assessmentType])
}

model SkillGapAnalysis {
  id                   String                 @id @default(uuid())
  userId               String
  targetCareerPathId   String?
  targetCareerPathName String // For custom career paths
  experienceLevel      ExperienceLevel
  timeframe            SkillGapTimeframe
  analysisData         Json // AI analysis results
  skillGaps            Json // Array of skill gaps with priorities
  learningPlan         Json // Generated learning plan
  marketData           Json? // Market demand data
  progressTracking     Json? // Milestone tracking
  status               SkillGapAnalysisStatus @default(ACTIVE)
  completionPercentage Int                    @default(0)
  lastUpdated          DateTime               @default(now())
  expiresAt            DateTime // Analysis validity period
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt

  user             User                         @relation(fields: [userId], references: [id], onDelete: Cascade)
  targetCareerPath CareerPath?                  @relation(fields: [targetCareerPathId], references: [id])
  recommendations  LearningPathRecommendation[]

  @@index([userId, status])
  @@index([userId, createdAt])
  @@index([targetCareerPathId])
}

model SkillMarketData {
  id                  String           @id @default(uuid())
  skillId             String
  region              String           @default("GLOBAL")
  demandLevel         SkillDemandLevel
  averageSalaryImpact Float? // Percentage salary increase
  jobPostingsCount    Int              @default(0)
  growthTrend         SkillGrowthTrend
  industryRelevance   Json // Array of relevant industries
  dataSource          String
  dataDate            DateTime
  isActive            Boolean          @default(true)
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt

  skill Skill @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@unique([skillId, region, dataDate])
  @@index([skillId, demandLevel])
  @@index([dataDate, isActive])
}

model LearningPathRecommendation {
  id                 String                 @id @default(uuid())
  skillGapAnalysisId String
  learningPathId     String
  priority           RecommendationPriority
  estimatedHours     Int
  skillsAddressed    Json // Array of skill IDs this path addresses
  completionOrder    Int
  prerequisites      Json? // Array of prerequisite skill IDs
  isCompleted        Boolean                @default(false)
  startedAt          DateTime?
  completedAt        DateTime?
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt

  skillGapAnalysis SkillGapAnalysis @relation(fields: [skillGapAnalysisId], references: [id], onDelete: Cascade)
  learningPath     LearningPath     @relation(fields: [learningPathId], references: [id], onDelete: Cascade)

  @@unique([skillGapAnalysisId, learningPathId])
  @@index([skillGapAnalysisId, priority])
}

enum ExperienceLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum ProfileVisibility {
  PRIVATE
  PUBLIC
  COMMUNITY_ONLY
}

enum AssessmentStatus {
  IN_PROGRESS
  COMPLETED
}

enum LearningResourceType {
  COURSE
  ARTICLE
  VIDEO
  PODCAST
  BOOK
  CERTIFICATION
  TUTORIAL
  WORKSHOP
}

enum LearningResourceCategory {
  CYBERSECURITY
  DATA_SCIENCE
  BLOCKCHAIN
  PROJECT_MANAGEMENT
  DIGITAL_MARKETING
  FINANCIAL_LITERACY
  LANGUAGE_LEARNING
  ARTIFICIAL_INTELLIGENCE
  WEB_DEVELOPMENT
  MOBILE_DEVELOPMENT
  CLOUD_COMPUTING
  ENTREPRENEURSHIP
  UX_UI_DESIGN
  PRODUCT_MANAGEMENT
  DEVOPS
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum LearningResourceCost {
  FREE
  FREEMIUM
  PAID
  SUBSCRIPTION
}

enum LearningResourceFormat {
  SELF_PACED
  INSTRUCTOR_LED
  INTERACTIVE
  HANDS_ON
  THEORETICAL
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BOOKMARKED
}

enum ReactionType {
  LIKE
  HELPFUL
  INSIGHTFUL
  FUNNY
  LOVE
  DISAGREE
}

enum ModeratorRole {
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum ReportReason {
  SPAM
  INAPPROPRIATE_CONTENT
  HARASSMENT
  OFF_TOPIC
  MISINFORMATION
  COPYRIGHT_VIOLATION
  OTHER
}

enum ReportStatus {
  PENDING
  UNDER_REVIEW
  RESOLVED
  DISMISSED
}

enum GoalType {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  CUSTOM
}

enum GoalCategory {
  LEARNING_RESOURCES
  SKILLS
  CERTIFICATIONS
  PROJECTS
  CAREER_MILESTONES
  NETWORKING
}

enum GoalStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum AchievementType {
  LEARNING_MILESTONE
  STREAK_ACHIEVEMENT
  COMPLETION_BADGE
  COMMUNITY_CONTRIBUTOR
  SKILL_MASTER
  GOAL_ACHIEVER
}

enum LearningStepType {
  RESOURCE
  QUIZ
  ASSIGNMENT
  PROJECT
  DISCUSSION
  REFLECTION
  EXTERNAL_LINK
  VIDEO
  READING
  PRACTICE
}

enum LearningPathStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  PAUSED
  ARCHIVED
}

enum InterviewSessionType {
  QUICK_PRACTICE
  FOCUSED_SESSION
  MOCK_INTERVIEW
  BEHAVIORAL_PRACTICE
  TECHNICAL_PRACTICE
  CUSTOM_SESSION
}

enum InterviewType {
  PHONE
  VIDEO
  IN_PERSON
  PANEL
  GROUP
  TECHNICAL_SCREEN
  BEHAVIORAL
  CASE_STUDY
}

enum InterviewSessionStatus {
  NOT_STARTED
  IN_PROGRESS
  PAUSED
  COMPLETED
  ABANDONED
}

enum InterviewQuestionType {
  BEHAVIORAL
  TECHNICAL
  SITUATIONAL
  COMPANY_CULTURE
  LEADERSHIP
  PROBLEM_SOLVING
  COMMUNICATION
  STRESS_TEST
  CASE_STUDY
  ROLE_SPECIFIC
}

enum InterviewCategory {
  GENERAL
  TECHNICAL_SKILLS
  SOFT_SKILLS
  LEADERSHIP
  PROBLEM_SOLVING
  COMMUNICATION
  TEAMWORK
  ADAPTABILITY
  CREATIVITY
  ANALYTICAL_THINKING
  CUSTOMER_SERVICE
  SALES
  MANAGEMENT
  STRATEGY
  ETHICS
  INDUSTRY_KNOWLEDGE
}

enum SkillAssessmentType {
  SELF_ASSESSMENT
  PEER_VALIDATION
  CERTIFICATION
  PERFORMANCE_BASED
  AI_EVALUATED
}

enum SkillGapTimeframe {
  THREE_MONTHS
  SIX_MONTHS
  ONE_YEAR
  TWO_YEARS
  CUSTOM
}

enum SkillGapAnalysisStatus {
  ACTIVE
  COMPLETED
  PAUSED
  EXPIRED
  ARCHIVED
}

enum SkillDemandLevel {
  VERY_LOW
  LOW
  MODERATE
  HIGH
  VERY_HIGH
  CRITICAL
}

enum SkillGrowthTrend {
  DECLINING
  STABLE
  GROWING
  RAPIDLY_GROWING
  EMERGING
}

enum RecommendationPriority {
  CRITICAL
  HIGH
  MEDIUM
  LOW
  OPTIONAL
}
