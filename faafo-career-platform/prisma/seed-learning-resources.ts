import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const learningResourcesData = [
  // ===== CYBERSECURITY RESOURCES =====
  // Cybersecurity Resources - Beginner
  {
    title: 'Ethical Hacking Essentials (E|HE)',
    description: 'Strong foundations in ethical hacking and penetration testing for entry-level careers',
    url: 'https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Network Defense Essentials (N|DE)',
    description: 'Fundamentals of network security, protocols, controls, and identity/access management',
    url: 'https://www.eccouncil.org/train-certify/network-defense-essentials-nde/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'CISA Learning',
    description: 'Government-backed cybersecurity training from beginner to advanced levels',
    url: 'https://www.cisa.gov/cybersecurity-training-exercises',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'CISA',
    duration: 'Various modules',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Cybersecurity Fundamentals',
    description: 'Learn terminology, roles, concepts like encryption, cryptography, and attacker tactics',
    url: 'https://skillsbuild.org/students/course-catalog/cybersecurity',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'BEGINNER',
    author: 'IBM SkillsBuild',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Cybersecurity Resources - Intermediate
  {
    title: 'Digital Forensics Essentials (D|FE)',
    description: 'Steps, practices, and methodologies for digital forensics investigations',
    url: 'https://www.eccouncil.org/train-certify/digital-forensics-essentials-dfe/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Cloud Security Essentials (C|SE)',
    description: 'Fundamentals of cloud computing and securing identities, data, and applications',
    url: 'https://www.eccouncil.org/train-certify/cloud-security-essentials-cse/',
    type: 'COURSE',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'EC-Council',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Certified in Cybersecurity (CC)',
    description: 'Entry-level certification covering security principles, business continuity, and incident response',
    url: 'https://www.isc2.org/landing/1mcc',
    type: 'CERTIFICATION',
    category: 'CYBERSECURITY',
    skillLevel: 'INTERMEDIATE',
    author: 'ISC2',
    duration: 'Self-paced training',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Data Science Resources - Beginner
  {
    title: 'Data Science: Machine Learning',
    description: 'Basics of machine learning, cross-validation, popular algorithms, and avoiding overtraining',
    url: 'https://pll.harvard.edu/course/data-science-machine-learning',
    type: 'COURSE',
    category: 'DATA_SCIENCE',
    skillLevel: 'BEGINNER',
    author: 'Harvard University',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Machine Learning Crash Course',
    description: 'Fast-paced introduction with animated videos, interactive visualizations, and hands-on practice',
    url: 'https://developers.google.com/machine-learning/crash-course',
    type: 'COURSE',
    category: 'DATA_SCIENCE',
    skillLevel: 'BEGINNER',
    author: 'Google',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'INTERACTIVE'
  },
  {
    title: 'Introduction to Data Science',
    description: 'Foundational data science skills led by tech experts',
    url: 'https://skillsbuild.org/students/course-catalog/data-science',
    type: 'COURSE',
    category: 'DATA_SCIENCE',
    skillLevel: 'BEGINNER',
    author: 'IBM SkillsBuild',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Blockchain Resources - Beginner
  {
    title: 'Blockchain Basics',
    description: 'Introduction to blockchain technology, cryptocurrency, and smart contracts',
    url: 'https://www.coursera.org/learn/blockchain-basics',
    type: 'COURSE',
    category: 'BLOCKCHAIN',
    skillLevel: 'BEGINNER',
    author: 'University at Buffalo',
    duration: 'Self-paced',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  {
    title: 'Blockchain Fundamentals',
    description: 'Comprehensive introduction to blockchain technology and its applications',
    url: 'https://www.edx.org/professional-certificate/uc-berkeleyx-blockchain-fundamentals',
    type: 'CERTIFICATION',
    category: 'BLOCKCHAIN',
    skillLevel: 'BEGINNER',
    author: 'UC Berkeley',
    duration: 'Self-paced',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },

  // Project Management Resources - Beginner
  {
    title: 'Project Management Foundations',
    description: 'Introduction to project management principles and methodologies',
    url: 'https://www.linkedin.com/learning/project-management-foundations-2019',
    type: 'COURSE',
    category: 'PROJECT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'LinkedIn Learning',
    duration: 'Video course',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Introduction to Project Management',
    description: 'Foundational concepts in project management',
    url: 'https://www.open.edu/openlearn/money-business/introduction-project-management/content-section-0',
    type: 'COURSE',
    category: 'PROJECT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Open University',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Digital Marketing Resources - Beginner
  {
    title: 'Fundamentals of Digital Marketing',
    description: 'Comprehensive introduction to digital marketing concepts and tools',
    url: 'https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing',
    type: 'COURSE',
    category: 'DIGITAL_MARKETING',
    skillLevel: 'BEGINNER',
    author: 'Google Digital Garage',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Social Media Marketing',
    description: 'Introduction to social media marketing strategies and platforms',
    url: 'https://academy.hubspot.com/courses/social-media',
    type: 'COURSE',
    category: 'DIGITAL_MARKETING',
    skillLevel: 'BEGINNER',
    author: 'HubSpot Academy',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // AI Resources - Beginner
  {
    title: 'AI For Everyone',
    description: 'Non-technical introduction to AI concepts and applications',
    url: 'https://www.coursera.org/learn/ai-for-everyone',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'BEGINNER',
    author: 'DeepLearning.AI',
    duration: 'Self-paced',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  {
    title: 'Elements of AI',
    description: 'Introduction to AI concepts and their practical applications',
    url: 'https://www.elementsofai.com/',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'BEGINNER',
    author: 'University of Helsinki',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'INTERACTIVE'
  },

  // ===== CLOUD ENGINEERING / DEVOPS RESOURCES =====
  // Cloud/DevOps Resources - Beginner
  {
    title: 'AWS Cloud Practitioner Essentials',
    description: 'Fundamental understanding of AWS Cloud concepts, services, and terminology',
    url: 'https://aws.amazon.com/training/digital/aws-cloud-practitioner-essentials/',
    type: 'COURSE',
    category: 'DEVOPS',
    skillLevel: 'BEGINNER',
    author: 'Amazon Web Services',
    duration: '6 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Google Cloud Fundamentals',
    description: 'Introduction to Google Cloud Platform services and concepts',
    url: 'https://cloud.google.com/training/courses/gcp-fundamentals',
    type: 'COURSE',
    category: 'DEVOPS',
    skillLevel: 'BEGINNER',
    author: 'Google Cloud',
    duration: '8 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Microsoft Azure Fundamentals',
    description: 'Foundational knowledge of cloud services and Microsoft Azure',
    url: 'https://docs.microsoft.com/en-us/learn/paths/azure-fundamentals/',
    type: 'COURSE',
    category: 'DEVOPS',
    skillLevel: 'BEGINNER',
    author: 'Microsoft',
    duration: '10 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Docker Getting Started',
    description: 'Learn containerization fundamentals with Docker',
    url: 'https://docs.docker.com/get-started/',
    type: 'TUTORIAL',
    category: 'DEVOPS',
    skillLevel: 'BEGINNER',
    author: 'Docker',
    duration: '2 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },

  // Cloud/DevOps Resources - Intermediate
  {
    title: 'Kubernetes Basics',
    description: 'Learn container orchestration with Kubernetes',
    url: 'https://kubernetes.io/docs/tutorials/kubernetes-basics/',
    type: 'TUTORIAL',
    category: 'DEVOPS',
    skillLevel: 'INTERMEDIATE',
    author: 'Kubernetes',
    duration: '4 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'AWS Solutions Architect Associate',
    description: 'Comprehensive AWS architecture and services training',
    url: 'https://aws.amazon.com/training/classroom/architecting-on-aws/',
    type: 'CERTIFICATION',
    category: 'DEVOPS',
    skillLevel: 'INTERMEDIATE',
    author: 'Amazon Web Services',
    duration: '3 days',
    cost: 'PAID',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Terraform Getting Started',
    description: 'Infrastructure as Code with Terraform',
    url: 'https://learn.hashicorp.com/terraform',
    type: 'TUTORIAL',
    category: 'DEVOPS',
    skillLevel: 'INTERMEDIATE',
    author: 'HashiCorp',
    duration: '6 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },

  // Cloud/DevOps Resources - Advanced
  {
    title: 'AWS DevOps Engineer Professional',
    description: 'Advanced DevOps practices on AWS platform',
    url: 'https://aws.amazon.com/certification/certified-devops-engineer-professional/',
    type: 'CERTIFICATION',
    category: 'DEVOPS',
    skillLevel: 'ADVANCED',
    author: 'Amazon Web Services',
    duration: 'Self-paced prep',
    cost: 'PAID',
    format: 'SELF_PACED'
  },

  // ===== PRODUCT MANAGEMENT RESOURCES =====
  // Product Management Resources - Beginner
  {
    title: 'Google Product Management Certificate',
    description: 'Comprehensive product management fundamentals from Google',
    url: 'https://grow.google/certificates/product-management/',
    type: 'CERTIFICATION',
    category: 'PRODUCT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Google',
    duration: '3-6 months',
    cost: 'SUBSCRIPTION',
    format: 'SELF_PACED'
  },
  {
    title: 'Product Management Fundamentals',
    description: 'Introduction to product management principles and practices',
    url: 'https://www.coursera.org/learn/uva-darden-product-management',
    type: 'COURSE',
    category: 'PRODUCT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'University of Virginia',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  {
    title: 'Product School Free Course',
    description: 'Product management basics from industry experts',
    url: 'https://productschool.com/free-product-management-course/',
    type: 'COURSE',
    category: 'PRODUCT_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Product School',
    duration: '2 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Product Management Resources - Intermediate
  {
    title: 'Agile Development Specialization',
    description: 'Agile methodologies for product development',
    url: 'https://www.coursera.org/specializations/agile-development',
    type: 'COURSE',
    category: 'PRODUCT_MANAGEMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'University of Virginia',
    duration: '4 months',
    cost: 'SUBSCRIPTION',
    format: 'SELF_PACED'
  },
  {
    title: 'Product Analytics Fundamentals',
    description: 'Data-driven product management and analytics',
    url: 'https://amplitude.com/academy',
    type: 'COURSE',
    category: 'PRODUCT_MANAGEMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Amplitude',
    duration: '3 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // ===== UX/UI DESIGN RESOURCES =====
  // UX/UI Design Resources - Beginner
  {
    title: 'Google UX Design Certificate',
    description: 'Comprehensive UX design training from Google',
    url: 'https://grow.google/certificates/ux-design/',
    type: 'CERTIFICATION',
    category: 'UX_UI_DESIGN',
    skillLevel: 'BEGINNER',
    author: 'Google',
    duration: '3-6 months',
    cost: 'SUBSCRIPTION',
    format: 'SELF_PACED'
  },
  {
    title: 'Figma Academy',
    description: 'Learn design fundamentals and Figma tool',
    url: 'https://www.figma.com/academy/',
    type: 'COURSE',
    category: 'UX_UI_DESIGN',
    skillLevel: 'BEGINNER',
    author: 'Figma',
    duration: '4 hours',
    cost: 'FREE',
    format: 'INTERACTIVE'
  },
  {
    title: 'Adobe XD Tutorials',
    description: 'UI/UX design with Adobe XD',
    url: 'https://helpx.adobe.com/xd/tutorials.html',
    type: 'TUTORIAL',
    category: 'UX_UI_DESIGN',
    skillLevel: 'BEGINNER',
    author: 'Adobe',
    duration: '6 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'Interaction Design Foundation',
    description: 'Comprehensive UX design courses and resources',
    url: 'https://www.interaction-design.org/',
    type: 'COURSE',
    category: 'UX_UI_DESIGN',
    skillLevel: 'BEGINNER',
    author: 'IxDF',
    duration: 'Various',
    cost: 'SUBSCRIPTION',
    format: 'SELF_PACED'
  },

  // UX/UI Design Resources - Intermediate
  {
    title: 'Design Systems with Figma',
    description: 'Learn to create and maintain design systems',
    url: 'https://www.figma.com/resources/learn-design-systems/',
    type: 'COURSE',
    category: 'UX_UI_DESIGN',
    skillLevel: 'INTERMEDIATE',
    author: 'Figma',
    duration: '3 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // ===== AI/MACHINE LEARNING ENGINEERING RESOURCES =====
  // AI/ML Resources - Beginner
  {
    title: 'Machine Learning Crash Course',
    description: 'Fast-paced introduction to machine learning with TensorFlow APIs',
    url: 'https://developers.google.com/machine-learning/crash-course',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'BEGINNER',
    author: 'Google',
    duration: '15 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Python for Data Science and AI',
    description: 'Learn Python programming for data science and AI applications',
    url: 'https://www.coursera.org/learn/python-for-applied-data-science-ai',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'BEGINNER',
    author: 'IBM',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },

  // AI/ML Resources - Intermediate
  {
    title: 'Deep Learning Specialization',
    description: 'Comprehensive deep learning course series',
    url: 'https://www.coursera.org/specializations/deep-learning',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'INTERMEDIATE',
    author: 'DeepLearning.AI',
    duration: '3 months',
    cost: 'SUBSCRIPTION',
    format: 'SELF_PACED'
  },
  {
    title: 'Kaggle Learn',
    description: 'Free micro-courses in data science and machine learning',
    url: 'https://www.kaggle.com/learn',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'INTERMEDIATE',
    author: 'Kaggle',
    duration: 'Various',
    cost: 'FREE',
    format: 'HANDS_ON'
  },

  // AI/ML Resources - Advanced
  {
    title: 'MLOps Specialization',
    description: 'Machine Learning Operations and deployment practices',
    url: 'https://www.coursera.org/specializations/machine-learning-engineering-for-production-mlops',
    type: 'COURSE',
    category: 'ARTIFICIAL_INTELLIGENCE',
    skillLevel: 'ADVANCED',
    author: 'DeepLearning.AI',
    duration: '4 months',
    cost: 'SUBSCRIPTION',
    format: 'SELF_PACED'
  },

  // ===== WEB DEVELOPMENT RESOURCES =====
  // Web Development Resources - Beginner
  {
    title: 'freeCodeCamp Full Stack Development',
    description: 'Comprehensive full-stack web development curriculum',
    url: 'https://www.freecodecamp.org/',
    type: 'COURSE',
    category: 'WEB_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'freeCodeCamp',
    duration: '300+ hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'The Odin Project',
    description: 'Open source curriculum for learning web development',
    url: 'https://www.theodinproject.com/',
    type: 'COURSE',
    category: 'WEB_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'The Odin Project',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'MDN Web Docs',
    description: 'Comprehensive web development documentation and tutorials',
    url: 'https://developer.mozilla.org/en-US/docs/Learn',
    type: 'TUTORIAL',
    category: 'WEB_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Mozilla',
    duration: 'Various',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Web Development Resources - Intermediate
  {
    title: 'React Official Tutorial',
    description: 'Learn React from the official documentation',
    url: 'https://react.dev/learn',
    type: 'TUTORIAL',
    category: 'WEB_DEVELOPMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Meta',
    duration: '10 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'Node.js Developer Roadmap',
    description: 'Complete guide to becoming a Node.js developer',
    url: 'https://nodejs.org/en/learn',
    type: 'TUTORIAL',
    category: 'WEB_DEVELOPMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Node.js Foundation',
    duration: 'Various',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // ===== FINANCIAL LITERACY RESOURCES =====
  // Financial Literacy Resources - Beginner
  {
    title: 'Personal Finance for Career Changers',
    description: 'Essential financial planning strategies for those considering or making a career transition',
    url: 'https://www.nerdwallet.com/article/finance/financial-planning-career-change',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'NerdWallet',
    duration: '15 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Emergency Fund Building Guide',
    description: 'Step-by-step guide to building an emergency fund before making major career changes',
    url: 'https://www.khanacademy.org/economics-finance-domain/core-finance/investment-vehicles-tutorial/ira-401ks/v/emergency-fund',
    type: 'VIDEO',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'Khan Academy',
    duration: '8 minutes',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Budgeting for Freelancers and Contractors',
    description: 'Financial management strategies for irregular income and self-employment',
    url: 'https://www.freshbooks.com/hub/accounting/budgeting-for-freelancers',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'FreshBooks',
    duration: '12 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Salary Negotiation Masterclass',
    description: 'Comprehensive guide to negotiating salary, benefits, and compensation packages',
    url: 'https://www.linkedin.com/learning/salary-negotiation',
    type: 'COURSE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'LinkedIn Learning',
    duration: '2 hours',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Investment Basics for Beginners',
    description: 'Introduction to investing, retirement planning, and building long-term wealth',
    url: 'https://www.investopedia.com/university/beginner/',
    type: 'COURSE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'Investopedia',
    duration: 'Self-paced',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Financial Literacy Resources - Intermediate
  {
    title: 'Tax Planning for Career Transitions',
    description: 'Understanding tax implications of career changes, freelancing, and self-employment',
    url: 'https://www.irs.gov/businesses/small-businesses-self-employed/self-employed-individuals-tax-center',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'INTERMEDIATE',
    author: 'IRS',
    duration: '20 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Financial Planning for Entrepreneurs',
    description: 'Advanced financial strategies for starting and running a business',
    url: 'https://www.sba.gov/business-guide/plan-your-business/calculate-your-startup-costs',
    type: 'TUTORIAL',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'INTERMEDIATE',
    author: 'Small Business Administration',
    duration: '30 minutes',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Retirement Planning During Career Changes',
    description: 'How to maintain and optimize retirement savings during career transitions',
    url: 'https://www.fidelity.com/learning-center/personal-finance/retirement/career-change-retirement',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'INTERMEDIATE',
    author: 'Fidelity',
    duration: '15 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Personal Finance Course',
    description: 'Comprehensive personal finance education covering budgeting, investing, and financial planning',
    url: 'https://www.coursera.org/learn/personal-finance',
    type: 'COURSE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'INTERMEDIATE',
    author: 'University of Illinois',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },

  // Financial Literacy Resources - Advanced
  {
    title: 'Advanced Investment Strategies',
    description: 'Portfolio management, asset allocation, and advanced investment techniques',
    url: 'https://www.edx.org/course/introduction-to-investments',
    type: 'COURSE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'ADVANCED',
    author: 'Indian Institute of Management',
    duration: '6 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },

  // ===== MOBILE DEVELOPMENT RESOURCES =====
  // Mobile Development Resources - Beginner
  {
    title: 'iOS App Development for Beginners',
    description: 'Complete introduction to iOS development with Swift and SwiftUI',
    url: 'https://developer.apple.com/tutorials/swiftui',
    type: 'TUTORIAL',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Apple Developer',
    duration: '10 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'Android Development Fundamentals',
    description: 'Learn Android app development with Kotlin and Android Studio',
    url: 'https://developer.android.com/courses/android-basics-kotlin/course',
    type: 'COURSE',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Google Android',
    duration: '6 weeks',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'React Native Complete Guide',
    description: 'Build cross-platform mobile apps with React Native',
    url: 'https://reactnative.dev/docs/tutorial',
    type: 'TUTORIAL',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Meta',
    duration: '8 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'Flutter Development Bootcamp',
    description: 'Complete Flutter and Dart development course for cross-platform apps',
    url: 'https://flutter.dev/docs/get-started/codelab',
    type: 'TUTORIAL',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Google Flutter',
    duration: '4 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'Mobile UI/UX Design Principles',
    description: 'Design principles and best practices for mobile user interfaces',
    url: 'https://material.io/design/introduction',
    type: 'ARTICLE',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Google Material Design',
    duration: '2 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Mobile Development Resources - Intermediate
  {
    title: 'Advanced iOS Development',
    description: 'Advanced iOS concepts including Core Data, networking, and app architecture',
    url: 'https://developer.apple.com/documentation/technologies',
    type: 'TUTORIAL',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Apple Developer',
    duration: '15 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'Android Architecture Components',
    description: 'Learn MVVM, Room database, and modern Android architecture patterns',
    url: 'https://developer.android.com/topic/architecture',
    type: 'TUTORIAL',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Google Android',
    duration: '8 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },
  {
    title: 'App Store Optimization (ASO)',
    description: 'Learn how to optimize mobile apps for app store discovery and downloads',
    url: 'https://developer.apple.com/app-store/product-page/',
    type: 'ARTICLE',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Apple Developer',
    duration: '3 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Mobile App Testing and Deployment',
    description: 'Testing strategies and deployment processes for mobile applications',
    url: 'https://firebase.google.com/docs/app-distribution',
    type: 'TUTORIAL',
    category: 'MOBILE_DEVELOPMENT',
    skillLevel: 'INTERMEDIATE',
    author: 'Google Firebase',
    duration: '4 hours',
    cost: 'FREE',
    format: 'HANDS_ON'
  },

  // ===== ENTREPRENEURSHIP RESOURCES =====
  // Entrepreneurship Resources - Beginner
  {
    title: 'Lean Startup Methodology',
    description: 'Learn the lean startup approach to building successful businesses',
    url: 'https://www.edx.org/course/entrepreneurship-micromaster',
    type: 'COURSE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'BEGINNER',
    author: 'Babson College',
    duration: '6 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Business Model Canvas',
    description: 'Learn to design and validate business models using the Business Model Canvas',
    url: 'https://www.strategyzer.com/canvas/business-model-canvas',
    type: 'TUTORIAL',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'BEGINNER',
    author: 'Strategyzer',
    duration: '2 hours',
    cost: 'FREE',
    format: 'INTERACTIVE'
  },
  {
    title: 'Y Combinator Startup School',
    description: 'Free online course covering all aspects of starting a company',
    url: 'https://www.startupschool.org/',
    type: 'COURSE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'BEGINNER',
    author: 'Y Combinator',
    duration: '10 weeks',
    cost: 'FREE',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Legal Basics for Startups',
    description: 'Essential legal knowledge for entrepreneurs and startup founders',
    url: 'https://www.nolo.com/legal-encyclopedia/small-business-startup',
    type: 'ARTICLE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'BEGINNER',
    author: 'Nolo',
    duration: '4 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Customer Development and Validation',
    description: 'Learn how to validate business ideas through customer development',
    url: 'https://steveblank.com/category/customer-development/',
    type: 'ARTICLE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'BEGINNER',
    author: 'Steve Blank',
    duration: '6 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Entrepreneurship Resources - Intermediate
  {
    title: 'Startup Funding and Investment',
    description: 'Understanding venture capital, angel investment, and startup funding',
    url: 'https://www.coursera.org/learn/venture-capital',
    type: 'COURSE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'INTERMEDIATE',
    author: 'University of Pennsylvania',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Sales and Customer Acquisition',
    description: 'Sales strategies and customer acquisition techniques for startups',
    url: 'https://blog.hubspot.com/sales/sales-training',
    type: 'TUTORIAL',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'INTERMEDIATE',
    author: 'HubSpot',
    duration: '8 hours',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Building and Leading Teams',
    description: 'Leadership and team building skills for entrepreneurs',
    url: 'https://www.linkedin.com/learning/building-and-leading-teams',
    type: 'COURSE',
    category: 'ENTREPRENEURSHIP',
    skillLevel: 'INTERMEDIATE',
    author: 'LinkedIn Learning',
    duration: '3 hours',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },

  // ===== LANGUAGE LEARNING RESOURCES =====
  // Language Learning Resources - Beginner
  {
    title: 'Business English for International Careers',
    description: 'Professional English communication skills for global career opportunities',
    url: 'https://www.futurelearn.com/courses/business-english-for-cross-cultural-communication',
    type: 'COURSE',
    category: 'LANGUAGE_LEARNING',
    skillLevel: 'BEGINNER',
    author: 'University of London',
    duration: '4 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Technical Communication Skills',
    description: 'Writing and communication skills for technical professionals',
    url: 'https://www.coursera.org/learn/technical-writing',
    type: 'COURSE',
    category: 'LANGUAGE_LEARNING',
    skillLevel: 'BEGINNER',
    author: 'Moscow Institute of Physics and Technology',
    duration: '5 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Cross-Cultural Communication',
    description: 'Understanding cultural differences in international business communication',
    url: 'https://www.edx.org/course/intercultural-communication',
    type: 'COURSE',
    category: 'LANGUAGE_LEARNING',
    skillLevel: 'INTERMEDIATE',
    author: 'University of California San Diego',
    duration: '6 weeks',
    cost: 'FREEMIUM',
    format: 'INSTRUCTOR_LED'
  },
  {
    title: 'Professional Presentation Skills',
    description: 'Develop confident presentation and public speaking skills',
    url: 'https://www.toastmasters.org/education/pathways-learning-experience',
    type: 'COURSE',
    category: 'LANGUAGE_LEARNING',
    skillLevel: 'BEGINNER',
    author: 'Toastmasters International',
    duration: 'Ongoing',
    cost: 'PAID',
    format: 'INSTRUCTOR_LED'
  }
];

async function seedCareerPaths() {
  console.log('🌱 Seeding career paths...');

  const careerPathsData = [
    {
      name: 'Cybersecurity Specialist',
      slug: 'cybersecurity-specialist',
      overview: 'Protect organizations from digital threats through ethical hacking, network security, and incident response.',
      pros: JSON.stringify([
        'High demand and job security',
        'Excellent salary potential',
        'Intellectually challenging work',
        'Remote work opportunities',
        'Continuous learning and growth'
      ]),
      cons: JSON.stringify([
        'High-stress environment',
        'Requires continuous education',
        'On-call responsibilities',
        'Rapidly evolving threat landscape'
      ]),
      actionableSteps: JSON.stringify([
        'Complete foundational cybersecurity courses',
        'Obtain entry-level certifications (Security+, CEH)',
        'Practice with hands-on labs and CTF challenges',
        'Build a home lab environment',
        'Apply for junior security analyst positions'
      ])
    },
    {
      name: 'Data Scientist',
      slug: 'data-scientist',
      overview: 'Extract insights from data using machine learning, statistics, and programming to drive business decisions.',
      pros: JSON.stringify([
        'High earning potential',
        'Diverse industry applications',
        'Intellectual stimulation',
        'Remote work friendly',
        'Growing field with many opportunities'
      ]),
      cons: JSON.stringify([
        'Requires strong mathematical background',
        'Data cleaning can be tedious',
        'Need to stay current with tools',
        'May require advanced degree'
      ]),
      actionableSteps: JSON.stringify([
        'Learn Python and R programming',
        'Master statistics and machine learning concepts',
        'Complete data science projects for portfolio',
        'Obtain relevant certifications',
        'Network with data science professionals'
      ])
    },
    {
      name: 'Digital Marketing Specialist',
      slug: 'digital-marketing-specialist',
      overview: 'Drive business growth through online marketing strategies, content creation, and digital advertising.',
      pros: JSON.stringify([
        'Creative and analytical work',
        'Lower barrier to entry',
        'Freelance opportunities',
        'Measurable results',
        'Diverse career paths'
      ]),
      cons: JSON.stringify([
        'Constantly changing platforms',
        'Can be competitive',
        'Results pressure',
        'Need to stay current with trends'
      ]),
      actionableSteps: JSON.stringify([
        'Learn Google Ads and Analytics',
        'Master social media marketing',
        'Build a personal brand online',
        'Create marketing campaigns portfolio',
        'Obtain digital marketing certifications'
      ])
    },
    {
      name: 'Cloud Engineer / DevOps Specialist',
      slug: 'cloud-engineer-devops',
      overview: 'Design, implement, and manage cloud infrastructure and automated deployment pipelines for scalable applications.',
      pros: JSON.stringify([
        'Extremely high demand in the market',
        'Excellent salary potential ($90k-$180k+)',
        'Remote work opportunities',
        'Cutting-edge technology exposure',
        'Strong job security and growth'
      ]),
      cons: JSON.stringify([
        'Steep learning curve initially',
        'On-call responsibilities',
        'Rapidly evolving tools and platforms',
        'High-pressure production environments'
      ]),
      actionableSteps: JSON.stringify([
        'Learn cloud fundamentals (AWS, Azure, or GCP)',
        'Master containerization with Docker and Kubernetes',
        'Practice Infrastructure as Code (Terraform, CloudFormation)',
        'Build CI/CD pipelines and automation scripts',
        'Obtain cloud certifications and build portfolio projects'
      ])
    },
    {
      name: 'Product Manager',
      slug: 'product-manager',
      overview: 'Drive product strategy, roadmap, and feature development by bridging business, technology, and user needs.',
      pros: JSON.stringify([
        'High strategic impact on business',
        'Excellent career progression opportunities',
        'Strong salary potential ($80k-$200k+)',
        'Cross-functional collaboration',
        'No coding required but technical understanding helpful'
      ]),
      cons: JSON.stringify([
        'High responsibility and accountability',
        'Balancing competing stakeholder demands',
        'Success depends on team execution',
        'Can be stressful with tight deadlines'
      ]),
      actionableSteps: JSON.stringify([
        'Learn product management fundamentals and frameworks',
        'Understand user research and data analysis',
        'Practice with product management tools (Jira, Figma, Analytics)',
        'Build a portfolio of product case studies',
        'Network with product professionals and seek mentorship'
      ])
    },
    {
      name: 'UX/UI Designer',
      slug: 'ux-ui-designer',
      overview: 'Create intuitive and visually appealing user experiences for digital products through research, design, and testing.',
      pros: JSON.stringify([
        'Creative and fulfilling work',
        'Strong demand across industries',
        'Good salary potential ($60k-$130k+)',
        'Remote work opportunities',
        'Visible impact on user satisfaction'
      ]),
      cons: JSON.stringify([
        'Subjective feedback and design critiques',
        'Tight deadlines and iteration cycles',
        'Need to stay current with design trends',
        'Balancing user needs with business constraints'
      ]),
      actionableSteps: JSON.stringify([
        'Learn design fundamentals and design thinking process',
        'Master design tools (Figma, Adobe XD, Sketch)',
        'Build a strong portfolio with diverse projects',
        'Practice user research and usability testing',
        'Seek feedback from experienced designers and iterate'
      ])
    },
    {
      name: 'AI/Machine Learning Engineer',
      slug: 'ai-ml-engineer',
      overview: 'Develop and deploy machine learning models and AI systems to solve complex business problems and automate processes.',
      pros: JSON.stringify([
        'Cutting-edge technology and innovation',
        'Extremely high salary potential ($100k-$250k+)',
        'High demand across all industries',
        'Intellectually challenging work',
        'Significant impact on business outcomes'
      ]),
      cons: JSON.stringify([
        'Requires strong mathematical and programming skills',
        'Steep learning curve and continuous education',
        'Complex problem-solving and debugging',
        'May require advanced degree for some positions'
      ]),
      actionableSteps: JSON.stringify([
        'Master Python programming and ML libraries (scikit-learn, TensorFlow, PyTorch)',
        'Learn statistics, linear algebra, and machine learning algorithms',
        'Complete hands-on projects and build a portfolio',
        'Understand data engineering and MLOps practices',
        'Obtain relevant certifications and contribute to open source projects'
      ])
    },
    {
      name: 'Full-Stack Web Developer',
      slug: 'full-stack-web-developer',
      overview: 'Build complete web applications from front-end user interfaces to back-end servers and databases.',
      pros: JSON.stringify([
        'Versatile skill set with many opportunities',
        'Strong job market demand',
        'Good salary potential ($60k-$140k+)',
        'Creative and technical work',
        'Freelance and remote work opportunities'
      ]),
      cons: JSON.stringify([
        'Need to stay current with rapidly changing technologies',
        'Can be overwhelming to master both front-end and back-end',
        'Competitive job market for entry-level positions',
        'Long hours during project deadlines'
      ]),
      actionableSteps: JSON.stringify([
        'Learn HTML, CSS, and JavaScript fundamentals',
        'Master a front-end framework (React, Vue, or Angular)',
        'Learn back-end development (Node.js, Python, or Java)',
        'Understand databases and API development',
        'Build full-stack projects and deploy them online'
      ])
    },
    {
      name: 'Mobile App Developer',
      slug: 'mobile-app-developer',
      overview: 'Design and develop mobile applications for iOS, Android, and cross-platform environments.',
      pros: JSON.stringify([
        'High demand in growing mobile market',
        'Excellent salary potential ($70k-$150k+)',
        'Creative and technical work',
        'Opportunity to reach millions of users',
        'Freelance and remote work opportunities'
      ]),
      cons: JSON.stringify([
        'Platform-specific learning curves',
        'App store approval processes',
        'Device fragmentation challenges',
        'Rapidly evolving mobile technologies'
      ]),
      actionableSteps: JSON.stringify([
        'Choose a platform (iOS, Android, or cross-platform)',
        'Learn platform-specific languages (Swift, Kotlin, React Native, Flutter)',
        'Master mobile UI/UX design principles',
        'Build and publish apps to app stores',
        'Learn mobile-specific technologies (APIs, databases, push notifications)'
      ])
    },
    {
      name: 'Entrepreneur / Startup Founder',
      slug: 'entrepreneur-startup-founder',
      overview: 'Start and grow your own business, bringing innovative ideas to market and building scalable companies.',
      pros: JSON.stringify([
        'Unlimited earning potential',
        'Complete autonomy and creative control',
        'Opportunity to solve meaningful problems',
        'Build wealth through equity and ownership',
        'Flexible work arrangements'
      ]),
      cons: JSON.stringify([
        'High risk and uncertainty',
        'Irregular income, especially initially',
        'Long hours and high stress',
        'Need to wear many hats',
        'High failure rate for startups'
      ]),
      actionableSteps: JSON.stringify([
        'Identify a market problem and validate your solution',
        'Develop a minimum viable product (MVP)',
        'Learn business fundamentals (finance, marketing, sales)',
        'Build a network of mentors and advisors',
        'Secure initial funding and customers'
      ])
    },
    {
      name: 'Financial Advisor / Planner',
      slug: 'financial-advisor-planner',
      overview: 'Help individuals and businesses make informed financial decisions and achieve their financial goals.',
      pros: JSON.stringify([
        'Meaningful work helping others achieve financial security',
        'Strong earning potential ($50k-$200k+)',
        'Growing demand as population ages',
        'Flexible work arrangements possible',
        'Continuous learning and professional development'
      ]),
      cons: JSON.stringify([
        'Requires licensing and ongoing education',
        'Income often tied to market performance',
        'Building client base takes time',
        'High responsibility for client financial outcomes'
      ]),
      actionableSteps: JSON.stringify([
        'Obtain necessary licenses (Series 7, 66, CFP)',
        'Learn financial planning software and tools',
        'Develop expertise in investment strategies',
        'Build communication and sales skills',
        'Gain experience through internships or entry-level positions'
      ])
    }
  ];

  try {
    for (const careerPath of careerPathsData) {
      await prisma.careerPath.upsert({
        where: { slug: careerPath.slug },
        update: careerPath,
        create: careerPath,
      });
    }

    console.log(`✅ Successfully seeded ${careerPathsData.length} career paths`);
  } catch (error) {
    console.error('❌ Error seeding career paths:', error);
    throw error;
  }
}

async function connectResourcesToCareerPaths() {
  console.log('🔗 Connecting learning resources to career paths...');

  try {
    // Get all career paths that actually exist in our database
    const cybersecurityPath = await prisma.careerPath.findUnique({
      where: { slug: 'cybersecurity-specialist' }
    });
    const dataSciencePath = await prisma.careerPath.findUnique({
      where: { slug: 'data-scientist' }
    });
    const digitalMarketingPath = await prisma.careerPath.findUnique({
      where: { slug: 'digital-marketing-specialist' }
    });
    const uxUiDesignerPath = await prisma.careerPath.findUnique({
      where: { slug: 'ux-ui-designer' }
    });
    const aiMlEngineerPath = await prisma.careerPath.findUnique({
      where: { slug: 'ai-machine-learning-engineer' }
    });
    const freelanceDevPath = await prisma.careerPath.findUnique({
      where: { slug: 'freelance-web-developer' }
    });
    const onlineBusinessPath = await prisma.careerPath.findUnique({
      where: { slug: 'simple-online-business' }
    });

    // Log which paths were found
    console.log('Found career paths:', {
      cybersecurity: !!cybersecurityPath,
      dataScience: !!dataSciencePath,
      digitalMarketing: !!digitalMarketingPath,
      uxUi: !!uxUiDesignerPath,
      aiMl: !!aiMlEngineerPath,
      freelanceDev: !!freelanceDevPath,
      onlineBusiness: !!onlineBusinessPath
    });

    if (!cybersecurityPath || !dataSciencePath || !digitalMarketingPath ||
        !uxUiDesignerPath || !aiMlEngineerPath || !freelanceDevPath || !onlineBusinessPath) {
      throw new Error('Some required career paths not found');
    }

    // Connect cybersecurity resources
    const cybersecurityResources = await prisma.learningResource.findMany({
      where: { category: 'CYBERSECURITY' }
    });

    for (const resource of cybersecurityResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: cybersecurityPath.id }
          }
        }
      });
    }

    // Connect data science resources
    const dataScienceResources = await prisma.learningResource.findMany({
      where: { category: 'DATA_SCIENCE' }
    });

    for (const resource of dataScienceResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: dataSciencePath.id }
          }
        }
      });
    }

    // Connect digital marketing resources
    const digitalMarketingResources = await prisma.learningResource.findMany({
      where: { category: 'DIGITAL_MARKETING' }
    });

    for (const resource of digitalMarketingResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: digitalMarketingPath.id }
          }
        }
      });
    }

    // Connect AI resources to both data science and AI/ML engineer paths
    const aiResources = await prisma.learningResource.findMany({
      where: { category: 'ARTIFICIAL_INTELLIGENCE' }
    });

    for (const resource of aiResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: dataSciencePath.id },
              { id: aiMlEngineerPath.id }
            ]
          }
        }
      });
    }

    // Connect UX/UI Design resources
    const uxUiResources = await prisma.learningResource.findMany({
      where: { category: 'UX_UI_DESIGN' }
    });

    for (const resource of uxUiResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: uxUiDesignerPath.id }
          }
        }
      });
    }

    // Connect Web Development resources to freelance dev path
    const webDevResources = await prisma.learningResource.findMany({
      where: { category: 'WEB_DEVELOPMENT' }
    });

    for (const resource of webDevResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: freelanceDevPath.id },
              { id: uxUiDesignerPath.id }
            ]
          }
        }
      });
    }

    // Connect Mobile Development resources to freelance dev path
    const mobileDevResources = await prisma.learningResource.findMany({
      where: { category: 'MOBILE_DEVELOPMENT' }
    });

    for (const resource of mobileDevResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: freelanceDevPath.id }
          }
        }
      });
    }

    // Connect Entrepreneurship resources to online business path
    const entrepreneurshipResources = await prisma.learningResource.findMany({
      where: { category: 'ENTREPRENEURSHIP' }
    });

    for (const resource of entrepreneurshipResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: onlineBusinessPath.id }
          }
        }
      });
    }

    // Connect Financial Literacy resources to online business path
    const financialLiteracyResources = await prisma.learningResource.findMany({
      where: { category: 'FINANCIAL_LITERACY' }
    });

    for (const resource of financialLiteracyResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: { id: onlineBusinessPath.id }
          }
        }
      });
    }

    // Connect Language Learning resources to multiple paths (beneficial for all careers)
    const languageLearningResources = await prisma.learningResource.findMany({
      where: { category: 'LANGUAGE_LEARNING' }
    });

    for (const resource of languageLearningResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: uxUiDesignerPath.id },
              { id: onlineBusinessPath.id },
              { id: digitalMarketingPath.id }
            ]
          }
        }
      });
    }

    // Connect Blockchain resources to multiple tech paths
    const blockchainResources = await prisma.learningResource.findMany({
      where: { category: 'BLOCKCHAIN' }
    });

    for (const resource of blockchainResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: freelanceDevPath.id },
              { id: onlineBusinessPath.id }
            ]
          }
        }
      });
    }

    // Connect Project Management resources to multiple paths
    const projectMgmtResources = await prisma.learningResource.findMany({
      where: { category: 'PROJECT_MANAGEMENT' }
    });

    for (const resource of projectMgmtResources) {
      await prisma.learningResource.update({
        where: { id: resource.id },
        data: {
          careerPaths: {
            connect: [
              { id: freelanceDevPath.id },
              { id: onlineBusinessPath.id },
              { id: uxUiDesignerPath.id }
            ]
          }
        }
      });
    }

    console.log('✅ Successfully connected learning resources to career paths');
  } catch (error) {
    console.error('❌ Error connecting resources to career paths:', error);
    throw error;
  }
}

// This function is now replaced by the exported version below

// Export the main seeding function for use in other seed files
export async function seedLearningResources() {
  console.log('🌱 Seeding learning resources...');
  await seedLearningResourcesData();
  await connectResourcesToCareerPaths();
  console.log('✅ Learning resources seeded successfully!');
}

// Rename the internal function to avoid conflicts
async function seedLearningResourcesData() {
  console.log('Creating learning resources...');

  try {
    for (const resource of learningResourcesData) {
      await prisma.learningResource.upsert({
        where: { url: resource.url },
        update: {
          title: resource.title,
          description: resource.description,
          type: resource.type as any,
          category: resource.category as any,
          skillLevel: resource.skillLevel as any,
          author: resource.author,
          duration: resource.duration,
          cost: resource.cost as any,
          format: resource.format as any,
        },
        create: resource as any,
      });
    }

    console.log(`✅ Successfully seeded ${learningResourcesData.length} learning resources`);
  } catch (error) {
    console.error('❌ Error seeding learning resources:', error);
    throw error;
  }
}

// Main function to run seeding
async function main() {
  await seedCareerPaths();
  await seedLearningResources();
}

// Only run main if this file is executed directly
if (require.main === module) {
  main()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
