{"summary": {"total": 25, "passed": 23, "failed": 2, "successRate": "92.0"}, "categories": {"sanitization": {"passed": 4, "failed": 0, "total": 4}, "html-encoding": {"passed": 8, "failed": 0, "total": 8}, "validation": {"passed": 4, "failed": 0, "total": 4}, "authentication": {"passed": 2, "failed": 1, "total": 3}, "error-handling": {"passed": 2, "failed": 0, "total": 2}, "session-management": {"passed": 2, "failed": 0, "total": 2}, "build": {"passed": 1, "failed": 1, "total": 2}}, "details": [{"name": "DOMPurify Installation", "passed": true, "message": "DOMPurify libraries are installed", "category": "sanitization", "timestamp": "2025-06-20T20:13:25.125Z"}, {"name": "Sanitization File: client-validation.ts", "passed": true, "message": "Contains sanitization logic", "category": "sanitization", "timestamp": "2025-06-20T20:13:25.125Z"}, {"name": "Sanitization File: html-encoding.ts", "passed": true, "message": "Contains sanitization logic", "category": "sanitization", "timestamp": "2025-06-20T20:13:25.126Z"}, {"name": "Sanitization File: validation.ts", "passed": true, "message": "Contains sanitization logic", "category": "sanitization", "timestamp": "2025-06-20T20:13:25.126Z"}, {"name": "HTML Encoding Function: encodeHtml", "passed": true, "message": "Function implemented", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "HTML Encoding Function: encodeHtmlAttribute", "passed": true, "message": "Function implemented", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "HTML Encoding Function: safeDisplayText", "passed": true, "message": "Function implemented", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "HTML Encoding Function: safeUserName", "passed": true, "message": "Function implemented", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "HTML Encoding Function: SafeText", "passed": true, "message": "Function implemented", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "Safe Display in ForumPost.tsx", "passed": true, "message": "Uses safe display functions", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "Safe Display in InterviewResultsAndFeedback.tsx", "passed": true, "message": "Uses safe display functions", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.127Z"}, {"name": "Safe Display in CareerPathCard.tsx", "passed": true, "message": "Uses safe display functions", "category": "html-encoding", "timestamp": "2025-06-20T20:13:25.128Z"}, {"name": "Validation Features in client-validation.ts", "passed": true, "message": "4/5 features found (80.0%)", "category": "validation", "timestamp": "2025-06-20T20:13:25.128Z"}, {"name": "Validation Features in useFormValidation.ts", "passed": true, "message": "3/5 features found (60.0%)", "category": "validation", "timestamp": "2025-06-20T20:13:25.128Z"}, {"name": "Enhanced Validation in LoginForm.tsx", "passed": true, "message": "Uses enhanced validation", "category": "validation", "timestamp": "2025-06-20T20:13:25.128Z"}, {"name": "Enhanced Validation in SignupForm.tsx", "passed": true, "message": "Uses enhanced validation", "category": "validation", "timestamp": "2025-06-20T20:13:25.128Z"}, {"name": "Auth Features in auth-state-manager.ts", "passed": true, "message": "5/5 features found (100.0%)", "category": "authentication", "timestamp": "2025-06-20T20:13:25.129Z"}, {"name": "Auth Features in useAuthState.ts", "passed": true, "message": "4/5 features found (80.0%)", "category": "authentication", "timestamp": "2025-06-20T20:13:25.129Z"}, {"name": "Auth Features in route.ts", "passed": false, "message": "2/5 features found (40.0%)", "category": "authentication", "timestamp": "2025-06-20T20:13:25.129Z"}, {"name": "Error Features in error-recovery.ts", "passed": true, "message": "4/5 features found (80.0%)", "category": "error-handling", "timestamp": "2025-06-20T20:13:25.129Z"}, {"name": "Error Features in EnhancedErrorBoundary.tsx", "passed": true, "message": "5/5 features found (100.0%)", "category": "error-handling", "timestamp": "2025-06-20T20:13:25.129Z"}, {"name": "Session Features in enhanced-session-manager.ts", "passed": true, "message": "5/5 features found (100.0%)", "category": "session-management", "timestamp": "2025-06-20T20:13:25.130Z"}, {"name": "Session Features in useEnhancedSessionMonitor.ts", "passed": true, "message": "5/5 features found (100.0%)", "category": "session-management", "timestamp": "2025-06-20T20:13:25.130Z"}, {"name": "TypeScript Compilation", "passed": false, "message": "TypeScript errors: Command failed with code 2: ", "category": "build", "timestamp": "2025-06-20T20:13:28.912Z"}, {"name": "ESLint Check", "passed": true, "message": "Only warnings found, no errors", "category": "build", "timestamp": "2025-06-20T20:13:35.051Z"}], "timestamp": "2025-06-20T20:13:35.051Z"}